/********************************/
/****** INICI JS PEU ANTIC ******/

$(window).on("resize load", function() {
	if ($(".footer-bottom__list-item")[0]){
		separatorHandler();
	}
	if($(".footer-bottom")[0] && $(".NG-goToTop")[0]){
		var footerH = $(".footer-bottom").height() + 15;
		$(".NG-goToTop").css("bottom", footerH);
	}
  });
  
  //Borrar el último separador de cada salto de línea en el menú del footer
  function separatorHandler() {
	var lastItemTop = $('.footer-bottom__list-item').first().position().top;
	$('.footer-bottom__list-item').each(function() {
	  if ($(this).position().top > lastItemTop) {
		lastItemTop = $(this).position().top;
		$(this).prev('.footer-bottom__list-item').removeClass('separator');
		$(this).addClass('separator');
	  } else {
		$(this).addClass('separator');
	  }
	});
  }

var listgroup1 = {
	list: $('.list-group1'),
	addclass: function(list){
		if (!$(list).parent('.cerca_xarxes').length ==1){
			$(list).addClass('w-45');
		}
	}
}

$(document).ready(function() {
	$( ".list-group1" ).each(function() {
		try {
			listgroup1.addclass(this);
		} catch (e) {
			if (console) {
				console.log("Error en afagir class a list-group1: " + e);
			}
		}
	});
	try
	{
		var w2 = $(".avis_legal img.adaptImage").first().width();
		//CAS1: Posem a 2-10 en el cas que l'imatge sigui inferior a 122px (gencat)
		if(w2 < 122)
		{
			$("#peuImatge").attr("class", "col-sm-2 col-md-2 col-lg-2");
			$("#peuAvis").attr("class", "col-sm-10 col-md-10 col-lg-10");
		}
		//CAS2: Posem a 3-9 en desktop i 4-8 en tablet en el cas que l'imatge sigui inferior a 192px (cads)
		else if(w2 < 192)
		{
			$("#peuImatge").attr("class", "col-sm-4 col-md-3 col-lg-3");
			$("#peuAvis").attr("class", "col-sm-8 col-md-9 col-lg-9");
		}
		//CAS3: Posem a 3-9 en desktop i 4-8 en tablet en el cas que l'imatge sigui inferior a 255px (agricultura)
		else if(w2 < 255)
		{
			$("#peuImatge").attr("class", "col-sm-5 col-md-4 col-lg-3");
			$("#peuAvis").attr("class", "col-sm-7 col-md-8 col-lg-9");
		}

		var w3 = $(".avis_legal img.checkImage").first().width();
		if(w3 > 200)
		{
			$(".avis_legal img.checkImage").removeAttr("height");
			$(".avis_legal img.checkImage").attr("width", 200);
		}
	}
	catch(e){
		if(console)
			console.log("Error en el recalcul del peu: "+e);
	}
});
/******* FI JS PEU ANTIC  *******/
/********************************/

/********************************/
/******* INICI JS PEU NOU *******/
// 'use strict';

var fpcaPeu = {
	hoverImage: function(){
		var image = document.getElementsByClassName('js-footer-hover-img');
		if(image != undefined){
			for(var x = 0; x < image.length; ++x ){
				image[x].addEventListener('mouseover', function(ev){
					var source = this.getAttribute('data-hover');
					this.src = source;
				});
				image[x].addEventListener('mouseleave', function(ev){
					var source = this.getAttribute('data-default');
					this.src = source;
				});
			}
		}
	},
	btnScrollVisibility: function(el, num){
        var scrollTop = document.documentElement.scrollTop;

		if(el != undefined){
		  if(scrollTop < num){
			el.style.display = "none";
		  } else {
			el.style.display = "block";
		  }
		}
	},
	scrollToTop: function(){
		var btn = document.getElementsByClassName('js-NG-goToTop');
		if(btn != undefined && btn.length > 0){
			fpcaPeu.btnScrollVisibility(btn[0], 150);
			btn[0].addEventListener("click", function(){
				window.scrollTo({top: 0, behavior: 'smooth'});
			})
		}
		if(btn != undefined){
			window.addEventListener("scroll", function(){
				fpcaPeu.btnScrollVisibility(btn[0], 150);
			})
		}
	},
	refresh: function(){

	},
	init: function(){
		fpcaPeu.hoverImage();
		fpcaPeu.scrollToTop();
	}
}


function ready() {
	document.addEventListener('DOMContentLoaded', function(){
		fpcaPeu.init();
		window.addEventListener('resize', function(){
		});
	});
};

ready();
/******** FI JS PEU NOU *********/
/********************************/
