import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { 
	Card2Mode,
	CertificateTypeEnum,
	LoginSimulatMethods, 
	PtComponentEventOutput, 
	PtLoginService, 
	PtSelectModel, 
	PtValidations 
} from 'pt-ui-components-mf-lib';
import { AppRoutes } from 'src/app/core/models/config.model';
import { SimulatedLoginRequest } from './models/login-simulat.model';

const CERTIFICATE_TYPE = 'certificateType';
const LOGIN_METHOD = 'method';
const COMPANY_NIF = 'nifCompany';

@Component({
	selector: 'app-login-simulat',
	templateUrl: './login-simulat.component.html',
	styleUrls: ['./login-simulat.component.sass']
})
export class LoginSimulatComponent implements OnInit {
	
	// Select options
	certificateOptions: PtSelectModel[] = [];
	loginOptions: PtSelectModel[] = [];

	// Other
	componentForm: FormGroup;
	yearRange:string;
	Card2Mode = Card2Mode;
	LoginSimulatMethods = LoginSimulatMethods;

	certJuridic = [
		CertificateTypeEnum.JUR_PERSON,
		CertificateTypeEnum.PUBLIC_EMP,CertificateTypeEnum.REPRE_JUR, 
		CertificateTypeEnum.REPRE_ENT_JUR
	];

	constructor(		
		private fb: FormBuilder,
		private loginService: PtLoginService,
		private router: Router
	) { }

	ngOnInit () {
		this.setCertificatOptions();
		this.setLoginOptions();

		this.setForm();

		this.setYearRange();
	}

	private setCertificatOptions () {
		const allowedCertificates:CertificateTypeEnum[] = [
			CertificateTypeEnum.FIS_PERSON,CertificateTypeEnum.JUR_PERSON,
			CertificateTypeEnum.PUBLIC_EMP,CertificateTypeEnum.REPRE_JUR, 
			CertificateTypeEnum.REPRE_ENT_JUR
		]
		const certificates = Object.values(CertificateTypeEnum);
		
		this.certificateOptions = certificates.filter(certificate => 
			allowedCertificates.includes(certificate)
		).map(certificate => Object.assign({
			id: certificate,
			label: `MODULE_TRACKING.CERTIFICATES.${certificate}`
		}));
	}

	private setLoginOptions () {
		const loginMethods = Object.keys(LoginSimulatMethods);

		this.loginOptions = loginMethods.map(loginMethod => {
			return {
				id: loginMethod,
				label: `MODULE_TRACKING.LOGIN_METHODS.${loginMethod}`
			}
		});
	}
	
	private setForm = (): void => {
		this.componentForm = this.fb.group({
			nifUser: [null, [Validators.required,PtValidations.dniNie]],
			nifCompany: [null, PtValidations.dniNieCif],
      method: [null, Validators.required],
			certificateType: [null],
			dateLogin: [null]
		});
	}

	private setYearRange (date?: Date){		
		const rangeDate = date ? new Date(date) : new Date();
		this.yearRange = `${rangeDate.getFullYear()-11}:${rangeDate.getFullYear()+9}`;
	}

	changeRangeYear(event:PtComponentEventOutput):void {
		this.setYearRange(event.componentValue);
	}

	onLoginChange() {
		const loginType = this.componentForm.get(LOGIN_METHOD).value;
		
		if (loginType === LoginSimulatMethods.CERTIFICAT) {
			this.componentForm.get(CERTIFICATE_TYPE).setValidators(Validators.required);
		} else {
			this.componentForm.get(COMPANY_NIF).reset();
			this.componentForm.get(COMPANY_NIF).clearValidators();
			this.componentForm.get(CERTIFICATE_TYPE).reset();
			this.componentForm.get(CERTIFICATE_TYPE).clearValidators();
			this.componentForm.updateValueAndValidity();
			this.componentForm.disable();
		}

		this.componentForm.get(CERTIFICATE_TYPE).markAsUntouched();
		this.componentForm.enable();
	}

	public isSelectedCertJuridic = (): boolean => {
		const certType = this.componentForm.get(CERTIFICATE_TYPE).value;
		return this.certJuridic.includes(certType);
	}

	onCertChange() {
		
		if (this.isSelectedCertJuridic()) {
			this.componentForm.get(COMPANY_NIF).setValidators(Validators.required);
		} else {
			this.componentForm.get(COMPANY_NIF).reset();
			this.componentForm.get(COMPANY_NIF).clearValidators();
			this.componentForm.updateValueAndValidity();
			this.componentForm.disable();
		}

		this.componentForm.get(COMPANY_NIF).markAsUntouched();
		this.componentForm.enable();
	}

	submit = async () => {
		const response = await this.loginService.loginSimulat(true,this.setSubmitRequest());
		
		if (response?.content) this.router.navigate(['/'+AppRoutes.WEB_MAP]);
	}

	private setSubmitRequest(): SimulatedLoginRequest {
		const dateLoginValue = this.componentForm.value['dateLogin'] ? 
			new Date(this.componentForm.value['dateLogin']).getTime() : -1

		return {
			...this.componentForm.value,
			dateLogin: dateLoginValue
		}
	}
}
