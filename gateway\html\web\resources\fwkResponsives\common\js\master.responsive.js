/*Created by <PERSON><PERSON>@*/
/*Transforma les llistes no responsive en responsive*/
var llistaResponsive = {
    transforma: function (taula, index){
        if(!$(taula).parent().hasClass("table-responsive")){
            $( "<div class='table-responsive pd-15 pd-30"+" taula_"+index+"'></div>" ).insertBefore(taula);
            $(taula).appendTo(".taula_"+index);
            if(!$(taula).hasClass("table")){
                $(taula).addClass("table");
            }
        }else{
            if(!$(taula).hasClass("table")){
                $(taula).addClass("table");
            }
        }
    }
}
$(document).ready(function() {
    /*******Transforma cercador********/
    $(".converteix-cercador").closest("section").removeClass("padding-xs");
    /*******Transforma cercador FI********/

    /*******Els mes consultats*********/
    if (genericResponsive.esMobil) {
        cambio = document.querySelectorAll("els_mes_buscats");
        $(cambio).append('<div class="neteja"></div>');
        $(cambio).css("z-index", "9999");
        $(cambio).insertAfter(".converteix-cercador").addClass("padding-xs");
    }
    /*******Els mes consultats FI******/

    /*************************************COMPONENT: fpca_tramit****************/
    /* ELEMENT: LLEGIR MES */
    var tamanyTextComplet = $(".text-complet").innerHeight();
    /*Accessibility - improve keyboard navigation*/
    var closedTextComplete = function(){
        if($(".element-llegir-mes .text-complet").length > 0){
            var textCompleteChildren = $(".fpca_tramit .element-llegir-mes .text-complet").find('a');
            $(textCompleteChildren).each(function(){
                $(this).attr("tabindex", -1);
            })
        } else {
            return false;
        }

    }
    var openTextComplete = function(){
        if($(".element-llegir-mes .text-complet").length > 0){
            var textCompleteChildren = $(".fpca_tramit .element-llegir-mes .text-complet").find('a');
            $(textCompleteChildren).each(function(){
                $(this).attr("tabindex", 0);
            })
        } else {
            return false;
        }

    }
    var llegeixCollapse = function(){
        tamanyFinal = $(".text-complet").height();

            if (!$(".boto-llegeix-mes").hasClass('desplegat')) {
                $(".text-tallat").animate({
                    height: tamanyFinal + "px"
                }, 500);
                $(".boto-llegeix-mes").addClass('desplegat');
                openTextComplete();
                $(".boto-llegeix-mes").empty();
                if (!($(".boto-llegeix-mes").attr('data-contract') === undefined)) {
                    $(".boto-llegeix-mes").append($(".boto-llegeix-mes").attr('data-contract'));
                } else
                    $(".boto-llegeix-mes").append("Llegiu-ne menys");
            } else {
                $(".boto-llegeix-mes").removeClass('desplegat');
                closedTextComplete()
                $(".text-tallat").animate({
                    height: tamanyInici + "px"
                }, 500);
                $(".boto-llegeix-mes").empty();
                if (!($(".boto-llegeix-mes").attr('data-expand') === undefined)) {
                    $(".boto-llegeix-mes").append($(".boto-llegeix-mes").attr('data-expand'));
                } else
                    $(".boto-llegeix-mes").append("Llegiu-ne més");
            }
    }
    if (tamanyTextComplet <= 48) {
        $(".boto-llegeix-mes").css("display", "none");
    } else {
        var tamanyInici = $(".text-tallat").height();
        closedTextComplete();
        $(".boto-llegeix-mes").click(function() {
            llegeixCollapse();
        });
        $(".boto-llegeix-mes").keydown(function(e){
            if(e.which === 13){
                llegeixCollapse();
            }
        });
    }

    /* ELEMENT: LLEGIR MES FI*/

    /*Funció per a l'acordió de pestanyes*/
    $(".accord-pestanya").click(function() {
        var idCont = "#" + $(this).attr("id");
        var ajust = $(this).parent().height();
        var pathname = window.location.pathname + idCont;
        setTimeout(function() {
            var posicio = $(idCont).offset().top - ajust;
            $("body,html").animate({
                scrollTop: posicio
            }, '500');
        }, 600);
    });
    /*Funció que aporta funcionalitat de scroll a l'acordió INICI*/
    //if(genericResponsive.esMobil()){
    $(".llistat_collapse .panel-title").click(function() {
        var idCont = "#" + $(this).closest(".panel-heading").next().attr("id");
        var ajust = $(this).parent().parent().height() * 2;
        //var pathname = window.location.pathname+idCont;
        if(!$(idCont).hasClass("in")) {
            var isTablet = window.innerWidth < 1025;
            var isMobile = window.innerWidth < 768;
            setTimeout(function() {
                var posicio = $(idCont).offset().top - ajust;
                if(isTablet || isMobile) {
                    posicio -= 50;
                }
                $("body,html").animate({
                    scrollTop: posicio
                }, '500');
            }, 600);
        }
    });
    //}
    /*Funció que aporta funcionalitat de scroll a l'acordió FI*/
    /*Funció elimina paddings de la peça a mòbil*/
    if (genericResponsive.esMobil) {
        $(".offPaddingsArticle").each(function() {
            $(this).closest("section").removeClass("padding-xs padding-sm padding-md");
        });
    }
    $( "table" ).each(function(index) {
        try {
            llistaResponsive.transforma(this,index);
        } catch (e) {
            if (console) {
                console.log("Error intentant fer responsive una de les taules: " + e);
            }
        }
    });
}

);
    




/*************************************COMPONENT: fpca_tramit FI****************/
function whoIam() {
    var check = false;
    (function(a, b) {
        if (/(android|bb\d+|meego).+mobile|avantgo|bada\/|blackberry|blazer|compal|elaine|fennec|hiptop|iemobile|ip(hone|od)|iris|kindle|lge |maemo|midp|mmp|mobile.+firefox|netfront|opera m(ob|in)i|palm( os)?|phone|p(ixi|re)\/|plucker|pocket|psp|series(4|6)0|symbian|treo|up\.(browser|link)|vodafone|wap|windows ce|xda|xiino/i.test(a) || /1207|6310|6590|3gso|4thp|50[1-6]i|770s|802s|a wa|abac|ac(er|oo|s\-)|ai(ko|rn)|al(av|ca|co)|amoi|an(ex|ny|yw)|aptu|ar(ch|go)|as(te|us)|attw|au(di|\-m|r |s )|avan|be(ck|ll|nq)|bi(lb|rd)|bl(ac|az)|br(e|v)w|bumb|bw\-(n|u)|c55\/|capi|ccwa|cdm\-|cell|chtm|cldc|cmd\-|co(mp|nd)|craw|da(it|ll|ng)|dbte|dc\-s|devi|dica|dmob|do(c|p)o|ds(12|\-d)|el(49|ai)|em(l2|ul)|er(ic|k0)|esl8|ez([4-7]0|os|wa|ze)|fetc|fly(\-|_)|g1 u|g560|gene|gf\-5|g\-mo|go(\.w|od)|gr(ad|un)|haie|hcit|hd\-(m|p|t)|hei\-|hi(pt|ta)|hp( i|ip)|hs\-c|ht(c(\-| |_|a|g|p|s|t)|tp)|hu(aw|tc)|i\-(20|go|ma)|i230|iac( |\-|\/)|ibro|idea|ig01|ikom|im1k|inno|ipaq|iris|ja(t|v)a|jbro|jemu|jigs|kddi|keji|kgt( |\/)|klon|kpt |kwc\-|kyo(c|k)|le(no|xi)|lg( g|\/(k|l|u)|50|54|\-[a-w])|libw|lynx|m1\-w|m3ga|m50\/|ma(te|ui|xo)|mc(01|21|ca)|m\-cr|me(rc|ri)|mi(o8|oa|ts)|mmef|mo(01|02|bi|de|do|t(\-| |o|v)|zz)|mt(50|p1|v )|mwbp|mywa|n10[0-2]|n20[2-3]|n30(0|2)|n50(0|2|5)|n7(0(0|1)|10)|ne((c|m)\-|on|tf|wf|wg|wt)|nok(6|i)|nzph|o2im|op(ti|wv)|oran|owg1|p800|pan(a|d|t)|pdxg|pg(13|\-([1-8]|c))|phil|pire|pl(ay|uc)|pn\-2|po(ck|rt|se)|prox|psio|pt\-g|qa\-a|qc(07|12|21|32|60|\-[2-7]|i\-)|qtek|r380|r600|raks|rim9|ro(ve|zo)|s55\/|sa(ge|ma|mm|ms|ny|va)|sc(01|h\-|oo|p\-)|sdk\/|se(c(\-|0|1)|47|mc|nd|ri)|sgh\-|shar|sie(\-|m)|sk\-0|sl(45|id)|sm(al|ar|b3|it|t5)|so(ft|ny)|sp(01|h\-|v\-|v )|sy(01|mb)|t2(18|50)|t6(00|10|18)|ta(gt|lk)|tcl\-|tdg\-|tel(i|m)|tim\-|t\-mo|to(pl|sh)|ts(70|m\-|m3|m5)|tx\-9|up(\.b|g1|si)|utst|v400|v750|veri|vi(rg|te)|vk(40|5[0-3]|\-v)|vm40|voda|vulc|vx(52|53|60|61|70|80|81|83|85|98)|w3c(\-| )|webc|whit|wi(g |nc|nw)|wmlb|wonu|x700|yas\-|your|zeto|zte\-/i.test(a.substr(0, 4))) check = true
    })(navigator.userAgent || navigator.vendor || window.opera);
    return check;
}