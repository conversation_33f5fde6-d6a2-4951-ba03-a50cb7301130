// 'use strict';

// Polyfills

// Element.closest()
if (!Element.prototype.matches) {
  Element.prototype.matches = Element.prototype.msMatchesSelector ||
      Element.prototype.webkitMatchesSelector;
}

if (!Element.prototype.closest) {
  Element.prototype.closest = function(s) {
    var el = this;

    do {
      if (el.matches(s)) return el;
      el = el.parentElement || el.parentNode;
    } while (el !== null && el.nodeType === 1);
    return null;
  };
}
// Array.from()
// Pasos de producción de ECMA-262, Edición 6, 22.1.2.1
// Referencia: https://people.mozilla.org/~jorendorff/es6-draft.html#sec-array.from
if (!Array.from) {
  Array.from = (function () {
    var toStr = Object.prototype.toString;
    var isCallable = function (fn) {
      return typeof fn === 'function' || toStr.call(fn) === '[object Function]';
    };
    var toInteger = function (value) {
      var number = Number(value);
      if (isNaN(number)) { return 0; }
      if (number === 0 || !isFinite(number)) { return number; }
      return (number > 0 ? 1 : -1) * Math.floor(Math.abs(number));
    };
    var maxSafeInteger = Math.pow(2, 53) - 1;
    var toLength = function (value) {
      var len = toInteger(value);
      return Math.min(Math.max(len, 0), maxSafeInteger);
    };

    // La propiedad length del método from es 1.
    return function from(arrayLike/*, mapFn, thisArg */) {
      // 1. Deje a C ser el este valor.
      var C = this;

      // 2. Deje que los elementos sean ToObject(arrayLike).
      var items = Object(arrayLike);

      // 3. Retornar IfAbrupt(items).
      if (arrayLike == null) {
        throw new TypeError("Array.from requiere un objeto array-like - not null or undefined");
      }

      // 4. Si mapfn no está definida, entonces deja que sea false.
      var mapFn = arguments.length > 1 ? arguments[1] : void undefined;
      var T;
      if (typeof mapFn !== 'undefined') {
        // 5. si no
        // 5. a If IsCallable(mapfn) es false, lanza una excepción TypeError.
        if (!isCallable(mapFn)) {
          throw new TypeError('Array.from: si hay mapFn, el segundo argumento debe ser una función');
        }

        // 5. b. Si thisArg se suministró, deje que T sea thisArg; si no, deje que T esté indefinido.
        if (arguments.length > 2) {
          T = arguments[2];
        }
      }

      // 10. Let lenValue be Get(items, "length").
      // 11. Let len be ToLength(lenValue).
      var len = toLength(items.length);

      // 13. If Isvarructor(C) is true, then
      // 13. a. Let A be the result of calling the [[varruct]] internal method of C with an argument list containing the single item len.
      // 14. a. Else, Let A be ArrayCreate(len).
      var A = isCallable(C) ? Object(new C(len)) : new Array(len);

      // 16. Let k be 0.
      var k = 0;
      // 17. Repeat, while k < len… (also steps a - h)
      var kValue;
      while (k < len) {
        kValue = items[k];
        if (mapFn) {
          A[k] = typeof T === 'undefined' ? mapFn(kValue, k) : mapFn.call(T, kValue, k);
        } else {
          A[k] = kValue;
        }
        k += 1;
      }
      // 18. Let putStatus be Put(A, "length", len, true).
      A.length = len;
      // 20. Return A.
      return A;
    };
  }());
}

// Array.includes()
// https://tc39.github.io/ecma262/#sec-array.prototype.includes
if (!Array.prototype.includes) {
  Object.defineProperty(Array.prototype, 'includes', {
    value: function(searchElement, fromIndex) {

      if (this == null) {
        throw new TypeError('"this" es null o no está definido');
      }

      // 1. Dejar que O sea ? ToObject(this value).
      var o = Object(this);

      // 2. Dejar que len sea ? ToLength(? Get(O, "length")).
      var len = o.length >>> 0;

      // 3. Si len es 0, devuelve false.
      if (len === 0) {
        return false;
      }

      // 4. Dejar que n sea ? ToInteger(fromIndex).
      //    (Si fromIndex no está definido, este paso produce el valor 0.)
      var n = fromIndex | 0;

      // 5. Si n ≥ 0, entonces
      //  a. Dejar que k sea n.
      // 6. Else n < 0,
      //  a. Dejar que k sea len + n.
      //  b. Si k < 0, Dejar que k sea 0.
      var k = Math.max(n >= 0 ? n : len - Math.abs(n), 0);

      function sameValueZero(x, y) {
        return x === y || (typeof x === 'number' && typeof y === 'number' && isNaN(x) && isNaN(y));
      }

      // 7. Repite, mientras k < len
      while (k < len) {
        // a. Dejar que elementK sea el resultado de ? Get(O, ! ToString(k)).
        // b. Si SameValueZero(searchElement, elementK) es true, devuelve true.
        if (sameValueZero(o[k], searchElement)) {
          return true;
        }
        // c. Incrementa k por 1.
        k++;
      }

      // 8. Devuelve false
      return false;
    }
  });
}
// Remove Polyfill
// .remove()
// from:https://github.com/jserz/js_piece/blob/master/DOM/ChildNode/remove()/remove().md
(function (arr) {
  arr.forEach(function (item) {
    if (item.hasOwnProperty('remove')) {
      return;
    }
    Object.defineProperty(item, 'remove', {
      configurable: true,
      enumerable: true,
      writable: true,
      value: function remove() {
        if (this.parentNode !== null)
          this.parentNode.removeChild(this);
      }
    });
  });
})([Element.prototype, CharacterData.prototype, DocumentType.prototype]);
// Add and remove event listeners
if (!Element.prototype.addEventListener) {
  var oListeners = {};
  function runListeners(oEvent) {
    if (!oEvent) { oEvent = window.event; }
    for (var iLstId = 0, iElId = 0, oEvtListeners = oListeners[oEvent.type]; iElId < oEvtListeners.aEls.length; iElId++) {
      if (oEvtListeners.aEls[iElId] === this) {
        for (iLstId; iLstId < oEvtListeners.aEvts[iElId].length; iLstId++) { oEvtListeners.aEvts[iElId][iLstId].call(this, oEvent); }
        break;
      }
    }
  }
  Element.prototype.addEventListener = function (sEventType, fListener /*, useCapture (will be ignored!) */) {
    if (oListeners.hasOwnProperty(sEventType)) {
      var oEvtListeners = oListeners[sEventType];
      for (var nElIdx = -1, iElId = 0; iElId < oEvtListeners.aEls.length; iElId++) {
        if (oEvtListeners.aEls[iElId] === this) { nElIdx = iElId; break; }
      }
      if (nElIdx === -1) {
        oEvtListeners.aEls.push(this);
        oEvtListeners.aEvts.push([fListener]);
        this["on" + sEventType] = runListeners;
      } else {
        var aElListeners = oEvtListeners.aEvts[nElIdx];
        if (this["on" + sEventType] !== runListeners) {
          aElListeners.splice(0);
          this["on" + sEventType] = runListeners;
        }
        for (var iLstId = 0; iLstId < aElListeners.length; iLstId++) {
          if (aElListeners[iLstId] === fListener) { return; }
        }
        aElListeners.push(fListener);
      }
    } else {
      oListeners[sEventType] = { aEls: [this], aEvts: [ [fListener] ] };
      this["on" + sEventType] = runListeners;
    }
  };
  Element.prototype.removeEventListener = function (sEventType, fListener /*, useCapture (will be ignored!) */) {
    if (!oListeners.hasOwnProperty(sEventType)) { return; }
    var oEvtListeners = oListeners[sEventType];
    for (var nElIdx = -1, iElId = 0; iElId < oEvtListeners.aEls.length; iElId++) {
      if (oEvtListeners.aEls[iElId] === this) { nElIdx = iElId; break; }
    }
    if (nElIdx === -1) { return; }
    for (var iLstId = 0, aElListeners = oEvtListeners.aEvts[nElIdx]; iLstId < aElListeners.length; iLstId++) {
      if (aElListeners[iLstId] === fListener) { aElListeners.splice(iLstId, 1); }
    }
  };
}
// Array.prototype.indexOf()
if (!Array.prototype.indexOf) {
  Array.prototype.indexOf = function indexOf(member, startFrom) {
    /*
    En el modo no estricto, si la variable `this` es null o indefinida, entonces se establece
    en el objeto ventana. De lo contrario, `this` se convierte automáticamente en un objeto.
    En modo estricto, si la variable `this` es nula o indefinida, se lanza `TypeError`.
    */
    if (this == null) {
      throw new TypeError("Array.prototype.indexOf() - no se puede convertir `" + this + "` en objeto");
    }

    var
        index = isFinite(startFrom) ? Math.floor(startFrom) : 0,
        that = this instanceof Object ? this : new Object(this),
        length = isFinite(that.length) ? Math.floor(that.length) : 0;

    if (index >= length) {
      return -1;
    }

    if (index < 0) {
      index = Math.max(length + index, 0);
    }

    if (member === undefined) {
      /*
        Dado que `member` no está definido, las claves que no existan tendrán el valor de `same`
        como `member` y, por lo tanto, es necesario verificarlas.
      */
      do {
        if (index in that && that[index] === undefined) {
          return index;
        }
      } while (++index < length);
    } else {
      do {
        if (that[index] === member) {
          return index;
        }
      } while (++index < length);
    }

    return -1;
  };
}

/**
 * detect IEEdge
 * returns version of IE/Edge or false, if browser is not a Microsoft browser
 */
function detectIEEdge() {
  var ua = window.navigator.userAgent;

  var msie = ua.indexOf('MSIE ');
  if (msie > 0) {
    // IE 10 or older => return version number
    var ie10 = parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
    return true;
  }

  var trident = ua.indexOf('Trident/');
  if (trident > 0) {
    // IE 11 => return version number
    var rv = ua.indexOf('rv:');
    var ie11 = parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
    return true;
  }

  var edge = ua.indexOf('Edge/');
  if (edge > 0) {
    // Edge => return version number
    var Edge = parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
    return false;
  }

  // other browser
  return false;
}
// Golbal is Internet Explorer
var NG_ISIE = detectIEEdge();

/**
 * detect IEEdge Version
 * returns version of IE/Edge or false, if browser is not a Microsoft browser
 */
function detectIEEdgeVersion() {
  var ua = window.navigator.userAgent;

  var msie = ua.indexOf('MSIE ');
  if (msie > 0) {
    // IE 10 or older => return version number
    var ie10 = parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
    return 10;
  }

  var trident = ua.indexOf('Trident/');
  if (trident > 0) {
    // IE 11 => return version number
    var rv = ua.indexOf('rv:');
    var ie11 = parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
    return 11;
  }

  var edge = ua.indexOf('Edge/');
  if (edge > 0) {
    // Edge => return version number
    var Edge = parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
    return 'Edge';
  }

  // other browser
  return false;
}
// Global is Internet Explorer
var NG_ISIEVersion = detectIEEdgeVersion();


/**
 * detect Touch device
 */
function is_touch_device() {
  try {
    document.createEvent("TouchEvent");
    return true;
  } catch (e) {
    return false;
  }
}


// animation
function animate(options) {

  var start = performance.now();

  requestAnimationFrame(function animate(time) {

    var timeFraction = (time - start) / options.duration;
    if (timeFraction > 1) timeFraction = 1;


    var progress = options.timing(timeFraction)

    options.draw(progress);

    if (timeFraction < 1) {
      requestAnimationFrame(animate);
    }

  });
}

// IEAnimation helper
var L3offset = 0;
var L4offset = 0;

/****************************************************************
 ************************** CAPCALERA ****************************
 ****************************************************************/


//******************** Init Document *******************/
function _openBackdrop(){
  _closeBackdrop();
  var ngBackdrop = document.createElement("div");
  ngBackdrop.classList.add('NG-Modal_Backdrop');
  document.documentElement.appendChild(ngBackdrop);
}
function _closeBackdrop(){
  var ngBackdrop = document.querySelectorAll('.NG-Modal_Backdrop');

  ngBackdrop.forEach(function(ngBackdrop){
    ngBackdrop.remove();
  });
}

document.addEventListener("DOMContentLoaded", function () {
  var navbar = document.querySelector('.NG-navbar');
  var search = document.querySelector('.NG-search');
  var icon_menu = document.querySelector('.NG-header__icon');
  var icon_search = document.querySelector('.NG-header__icon2');
  // var rotateicon = document.querySelector('#rotate_icon');
  var menuIdioma =  document.querySelector('.js-NG-menu-idioma');
  var listIdioma =  document.querySelector('.NG-navbar__list-language');
  var $megaMenu =  document.querySelector('.NG-megamenu');
  var $body = document.querySelector('html');

  // code...

  var closeAllMenus = function(){
    L3offset = 0;
    L4offset = 0;
    megamenuIE.initIEAnimation(NG_ISIE);
    if (search != null) {
      search.classList.remove('NG-search--expand');
      search.setAttribute('aria-expanded', false);
    }
    if (icon_menu != null){
      icon_menu.classList.remove('NG-header__icon-close');
      icon_menu.setAttribute('aria-expanded', false);
    }
    if (icon_search != null){
      icon_search.classList.remove('NG-header__icon-close');
      icon_search.setAttribute('aria-expanded', false);
    }
    _closeBackdrop();

    if (navbar != null){
      navbar.classList.remove('NG-navbar-expand');
    }
    if (listIdioma != null){
      listIdioma.classList.remove('.NG-navbar__list-language--expanded')
    }
    if (menuIdioma != null){
      menuIdioma.classList.remove('NG-navbar__link--active');
      menuIdioma.setAttribute('aria-expanded', false);
    }
    if ($megaMenu != null){
      megamenu.closeAllMegamenus();
      megamenu.closeAllSubmenus($megaMenu);
      _maxHeightNavMobile(false);
    }
  }


  //left - toggle Burguer menu
  if (icon_menu != null) {
    icon_menu.addEventListener("click", function () {
      var expanded = icon_menu.getAttribute('aria-expanded') === "true";
      var open = false;
      // console.log(icon_menu.getAttribute('aria-expanded'));
      // icon_menu.setAttribute('aria-expanded', !expanded);
      if(!expanded) {
        closeAllMenus();
        // console.log("!expanded", expanded);
        icon_menu.classList.add('NG-header__icon-close');
        icon_menu.setAttribute('aria-expanded', true);
        navbar.classList.add('NG-navbar-expand');
        open = true;
        $body.classList.remove('NG-body-lock');
        $body.classList.add('NG-body-lock');
        _openBackdrop();
      } else {
        // console.log("expanded", expanded);
        icon_menu.classList.remove('NG-header__icon-close');
        icon_menu.setAttribute('aria-expanded', false);
        navbar.classList.remove('NG-navbar-expand');
        open = false;
        $body.classList.remove('NG-body-lock');
        _closeBackdrop();
      }
      _maxHeightNavMobile(open);

    });
    icon_menu.addEventListener("keyup", function(event) {
      // event.preventDefault();
      if (event.keyCode === 13) {
        icon_menu.click();
      }
    });

  }

  function _maxHeightNavMobile(open) {
    var upHeader = document.querySelector('.NG-header__container');
    var navBar = document.querySelector('.NG-navbar');
    var navBarMenu = document.querySelector('.js-navbar__menu');

    if (navBar != null && upHeader != null) {
      if(window.innerWidth < 1025) {
        var headerHeight = upHeader.querySelector('.NG-main').offsetHeight;
        if(open) {
          navBar.style.maxHeight = "calc(100vh - " + headerHeight + "px)";
          navBarMenu.style.height = "auto";
          navBarMenu.classList.remove("NG-navbar__menu--visible");
          navBarMenu.classList.add("NG-navbar__menu--visible");
        } else {
          navBar.style.maxHeight = "";
          navBarMenu.style.height = "";
          navBarMenu.classList.remove("NG-navbar__menu--visible");
        }
      }
    }
  }
  // right - Toggle Search
  if (icon_search != null) {
    icon_search.addEventListener("click", function () {
      var expanded = icon_search.getAttribute('aria-expanded') === "true";
      // console.log(icon_search.getAttribute('aria-expanded'));
      // icon_menu.setAttribute('aria-expanded', !expanded);
      if(!expanded) {
        closeAllMenus();
        search.classList.add('NG-search--expand');
        search.style.height = "350px";
        icon_search.classList.add('NG-header__icon-close');
        icon_search.setAttribute('aria-expanded', true);
        _openBackdrop();
        if(window.innerWidth < 1025) {
          $body.classList.remove('NG-body-lock');
          $body.classList.add('NG-body-lock');
        }

      } else {
        search.classList.remove('NG-search--expand');
        icon_search.classList.remove('NG-header__icon-close');
        icon_search.setAttribute('aria-expanded', false);
        $body.classList.remove('NG-body-lock');
        _closeBackdrop();
      }

    });

    document.addEventListener('click', function(event) {
      if(document.querySelector('.NG-search--expand') != null){
        var modal = document.querySelector('.NG-Modal_Backdrop');
        var isClickInside = modal.contains(event.target);
        if (isClickInside && window.innerWidth > 1024) {

          //the click was outside the specifiedElement, do something
          search.classList.remove('NG-search--expand');
          icon_search.classList.remove('NG-header__icon-close');
          icon_search.setAttribute('aria-expanded', false);
          $body.classList.remove('NG-body-lock');
          _closeBackdrop();
        }

      }
    });

    icon_search.addEventListener("keyup", function(event) {
      // event.preventDefault();
      if (event.keyCode === 13) {
        icon_search.click();
      }
    });

    // close on escape
    document.addEventListener('keydown', function(evt) {
      var expanded = icon_search.getAttribute('aria-expanded');
      evt = evt || window.event;
      var isEscape = false;
      if ("key" in evt) {
        isEscape = (evt.key === "Escape" || evt.key === "Esc");
      } else {
        isEscape = (evt.keyCode === 27);
      }
      if (isEscape) {
        if(expanded){
          search.classList.remove('NG-search--expand');
          icon_search.classList.remove('NG-header__icon-close');
          icon_search.setAttribute('aria-expanded', false);
          $body.classList.remove('NG-body-lock');
          _closeBackdrop();
        }
      }
    });
  }

  //Menu idioma
  if (menuIdioma != null) {
    menuIdioma.addEventListener("click", function () {
      var expanded = menuIdioma.getAttribute('aria-expanded') === "true";
      // console.log(menuIdioma.getAttribute('aria-expanded'));
      // icon_menu.setAttribute('aria-expanded', !expanded);
      if(!expanded) {
        listIdioma.classList.add('NG-navbar__list-language--expanded');
        menuIdioma.classList.add('NG-navbar__link--active');
        menuIdioma.setAttribute('aria-expanded', true);
      } else {
        listIdioma.classList.remove('NG-navbar__list-language--expanded');
        menuIdioma.classList.remove('NG-navbar__link--active');
        menuIdioma.setAttribute('aria-expanded', false);
      }

    });
    menuIdioma.addEventListener("keyup", function(event) {
      // event.preventDefault();
      if (event.keyCode === 13) {
        menuIdioma.click();
      }
    });

    // close on escape
    document.addEventListener('keydown', function(evt) {
      var expanded = menuIdioma.getAttribute('aria-expanded');
      evt = evt || window.event;
      var isEscape = false;
      if ("key" in evt) {
        isEscape = (evt.key === "Escape" || evt.key === "Esc");
      } else {
        isEscape = (evt.keyCode === 27);
      }
      if (isEscape) {
        if(expanded){
          listIdioma.classList.remove('NG-navbar__list-language--expanded');
          menuIdioma.classList.remove('NG-navbar__link--active');
          menuIdioma.setAttribute('aria-expanded', false);
        }
      }
    });

  }
  // Select Idioma
  var selectIdioma = function(idiomaItem){
    if (menuIdioma != null) {
      var text = idiomaItem.dataset.idioma;
      var idiomaText = document.getElementsByClassName('js-NG-idioma-text');
      var isAutomatic = idiomaItem.classList.contains('js-NG-idioma-auto');
      idiomaText[0].innerHTML = text;

      if(window.innerWidth < 1025){
        idiomaText[0].innerHTML = text;
      } else {
        var newText = shortIdioma(text);
        idiomaText[0].innerHTML = newText;
      }

      // close menu idioma
      if(!isAutomatic){
        if(listIdioma != undefined){listIdioma.classList.remove('NG-navbar__list-language--expanded');}
        if(menuIdioma != undefined){menuIdioma.classList.remove('NG-navbar__link--active');}
        if(menuIdioma != undefined){menuIdioma.setAttribute('aria-expanded', false);}
      } else {
      }
    }
  }


  var shortIdioma = function(text){
    var textShort = text.slice(0, 2);
    return textShort
  }

  if (listIdioma != null){
    var idiomalinks = listIdioma.querySelectorAll('.js-NG-idioma-item');
    idiomalinks.forEach(function(el){
      el.addEventListener("click", function(){selectIdioma(el)});
      el.addEventListener("keyup", function(event) {
        // event.preventDefault();
        if (event.keyCode === 13) {
          el.click();
        }
      });

    })
  }

  // Language Tooltip
  /**
   * Generates a tooltip for traduccio al vol language links
   * @param element anchor/link, contains the tooltip html code in the data-html attribute
   * @param top, absolute vertical position of the tooltip (in pixels)
   * @param left, absolute horizontal position of the tooltip (in pixels)
   * @param position {string} optional Relative position in reference to anchor (only possible value is "top")
   */
  var _createLanguageTooltip = function(element, top, left, position){
    var tooltipContent = element.getAttribute('data-html');

    var tooltip = document.createElement("div");
    tooltip.classList.add('NG-idioma-tooltip');
    if(position != undefined && position == "top"){
      tooltip.classList.add('NG-idioma-tooltip--' + position);
      tooltip.style.top = top - 10 + "px";
    } else {
      tooltip.style.top = top + 30 + "px";
    }
    tooltip.style.left = left + "px";
    tooltip.innerHTML = tooltipContent;
    // console.log(tooltip);
    //Add language tooltip
    var addedTooltip = document.body.appendChild(tooltip);

    //Set focus for accessibility
    addedTooltip.focus();
  }
  var _destroyLanguageTooltip = function(){
    var tooltips = document.querySelectorAll('.NG-idioma-tooltip');
    tooltips.forEach(function(tooltip){
      tooltip.remove();
    })
  }
  var languageTooltipMobile = function(){
    var idiomaTooltipItems = document.querySelectorAll('.js-NG-idioma-tooltip');
    idiomaTooltipItems.forEach(function(idiomaTooltipItem) {

      if(idiomaTooltipItem.getAttribute('data-html') != null){

        idiomaTooltipItem.addEventListener('click', function(ev){
          ev.preventDefault();
          var viewportOffset = idiomaTooltipItem.getBoundingClientRect();
          var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft,
              scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          // these are relative to the viewport, i.e. the window
          var top = viewportOffset.top + scrollTop;
          var left = viewportOffset.left + scrollLeft;
          var position = "";
          if((window.innerHeight - viewportOffset.top) < 150){
            position = "top";
          }
          _createLanguageTooltip(idiomaTooltipItem, top, left, position);
          return false;
        })
        idiomaTooltipItem.addEventListener('mouseleave', function(){
          _destroyLanguageTooltip();
        })
      }
    });
  }
  var languageTooltip = function(){
    // console.log('tooltip');
    var idiomaTooltipItems = document.querySelectorAll('.js-NG-idioma-tooltip');
    idiomaTooltipItems.forEach(function (idiomaTooltipItem) {
      if(idiomaTooltipItem.getAttribute('data-html') != null){
        idiomaTooltipItem.addEventListener('mouseenter', function(){
          var viewportOffset = idiomaTooltipItem.getBoundingClientRect();
          var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft,
              scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          // these are relative to the viewport, i.e. the window
          var top = viewportOffset.top + scrollTop;
          var left = viewportOffset.left + scrollLeft ;
          _createLanguageTooltip(idiomaTooltipItem, top, left);
        });
        idiomaTooltipItem.addEventListener('focusin', function(){
          var viewportOffset = idiomaTooltipItem.getBoundingClientRect();
          var scrollLeft = window.pageXOffset || document.documentElement.scrollLeft,
              scrollTop = window.pageYOffset || document.documentElement.scrollTop;
          // these are relative to the viewport, i.e. the window
          var top = viewportOffset.top + scrollTop;
          var left = viewportOffset.left + scrollLeft ;
          _createLanguageTooltip(idiomaTooltipItem, top, left);
        }, false);
        idiomaTooltipItem.addEventListener('mouseleave', function(){
          _destroyLanguageTooltip();
        });
        idiomaTooltipItem.addEventListener('focusout', function(){
          _destroyLanguageTooltip();
        });
      }
    });
  }

  // Init Idioma
  var initIdioma = function(){
    var selectedIdioma = document.getElementsByClassName('js-NG-idioma-text');
    var text = selectedIdioma[0].innerHTML;

    if(window.innerWidth < 1025){
      selectedIdioma[0].innerHTML = text;
    } else {
      var shortTxt = shortIdioma(text);
      selectedIdioma[0].innerHTML = shortTxt;
    }

    if(is_touch_device()){
      languageTooltipMobile();
    } else {
      languageTooltip();
    }
  }

  initIdioma();

  //close language menu on click outside

  window.onclick = function(event) {
    if(document.querySelector(".NG-navbar__list-language") != undefined){
      document.querySelector(".NG-navbar__list-language").classList.remove("NG-collapse--show");
    }
  }

});
//******************** END Init Document *******************/

//funcion para cambiar imagenes

function change_img(id, image1, image2) {
  imgsrc = document.getElementById(id).src;

  if (imgsrc.indexOf(image1) != -1) {
    document.getElementById("header_icon-image").src = image2;
  } else {
    document.getElementById("header_icon-image").src = image1;
  }
}


/****************************************************************
 ************************ CAPCALERA NAV **************************
 ****************************************************************/
// Navbar
var navbarCapcalera = {
  isLarge: function(){
    var navbar = document.querySelector('.js-navbar__menu');
    var hasClass = false;

    hasClass = navbar.classList.contains('NG-navbar__menu--isLarge');
    return hasClass;
  },
  ifLarge: function(){
    var navbar = document.querySelector('.js-navbar__menu');
    if(navbar != undefined){
      navbar.classList.remove('NG-navbar__menu--isLarge');
      var navbarWidth = navbar.offsetWidth - 30;
      var totalWidth = 0;
      var totalElements = navbar.children.length;
      var menuIteration = function(){
        totalWidth = 0;
        for (var index = 0; index < navbar.children.length; index++) {
          if(NG_ISIEVersion == 10 || NG_ISIEVersion == 11){
            totalWidth = totalWidth + (navbar.children[index].children[0].offsetWidth + 100)

          } else {
            // console.log((navbar.children[index].children[0].offsetWidth));
            totalWidth = totalWidth + (navbar.children[index].offsetWidth);
          }
        }
        // console.log(totalWidth, navbarWidth);
        return totalWidth;
      }

      totalWidth = menuIteration();

      if(totalWidth > navbarWidth){

        if((totalWidth - navbarWidth) > 0 && (totalWidth - navbarWidth) < (totalElements * 38)){
          navbar.classList.remove('NG-navbar__menu--isMedium');
          navbar.classList.add('NG-navbar__menu--isMedium');
          totalWidth = menuIteration();
          if(totalWidth > navbarWidth){
            navbar.classList.remove('NG-navbar__menu--isMedium');
            navbar.classList.remove('NG-navbar__menu--isLarge');
            navbar.classList.add('NG-navbar__menu--isLarge');
          }

        }else if(totalWidth > navbarWidth){
          navbar.classList.remove('NG-navbar__menu--isLarge');
          navbar.classList.add('NG-navbar__menu--isLarge');
        }
      }
      return false;

    }
  },
  hoverElement: function(){
    var navbar = document.querySelector('.js-navbar__menu');
    // console.log(navbar != undefined);
    if(navbar != undefined){
      for (var index = 0; index < navbar.children.length; index++) {
        navbar.children[index].addEventListener("mouseover", function(event){
          navbar.classList.remove('NG-navbar__menu--hover');
          navbar.classList.add('NG-navbar__menu--hover');
        });
        navbar.children[index].addEventListener("mouseout", function(event){
          navbar.classList.remove('NG-navbar__menu--hover');
        });
      }
    }
  },
  refresh: function(){
    navbarCapcalera.ifLarge();
  },
  init: function(){
    //navbarCapcalera.hoverElement();
    navbarCapcalera.ifLarge();
  }
}

// Megamenu
var megamenu = {
  openMegamenu: function(){

    var element = document.querySelectorAll('.js-has-megamenu');
    element.forEach(function(el){

      // var el = element[index];
      var btn = el.getElementsByClassName('NG-navbar__link');
      // console.log(btn, 'btn');
      btn[0].addEventListener('click', function(ev){
        ev.preventDefault();
        var expanded = btn[0].getAttribute('aria-expanded');
        if(expanded == "false"){
          megamenu.closeAllMegamenus();
          megamenu.closeAllSubmenus(el);
          el.classList.add('has-megamenu--expand');
          ariaExpandFn(btn[0], true);
          el.closest(".NG-navbar").classList.add('NG-navbar--expand');
          var wrapper = el.querySelectorAll('.NG-megamenu');
          megamenu.animateMegamenuIn(wrapper);
          // console.log("wrapper 0", wrapper[0]);
          // btn[0].closest('.js-navbar__menu').style.height = "";
          btn[0].closest('.js-navbar__menu').style.height = _navWrapperSize(wrapper[0]) != null ? _navWrapperSize(wrapper[0]) : "";
          this.closest('.js-navbar__menu').classList.remove('NG-navbar__menu--visible');
          if(window.innerWidth > 1024){
            _openBackdrop();
          }
          if(window.innerWidth < 1024){
            this.closest('.js-navbar__menu').scroll(0,0);
          }
        } else {
          var wrapper = el.querySelectorAll('.NG-megamenu');
          megamenu.animateMegamenuOut(wrapper);
          setTimeout(function() {
            ariaExpandFn(btn[0], false);
            _closeBackdrop();
            megamenu.closeAllMegamenus();
            megamenu.closeAllSubmenus(el);
          }, 500);
        }
      });
    });
  },
  closeAllMegamenus: function(){
    var elHasMegamenu = document.getElementsByClassName('js-has-megamenu');
    for (var i = 0; i < elHasMegamenu.length; i++) {
      var menuItem = elHasMegamenu[i];
      menuItem.closest('.js-navbar__menu').style.height = "auto";
      menuItem.classList.remove('has-megamenu--expand');
      var btn = menuItem.getElementsByClassName('NG-navbar__link');
      ariaExpandFn(btn[0], false);
      menuItem.closest(".NG-navbar").classList.remove('NG-navbar--expand');
      menuItem.querySelector('.NG-megamenu').style.maxHeight = 0 + 'px';
      menuItem.querySelector('.NG-megamenu').style.paddingTop = 0 + 'px';
      menuItem.querySelector('.NG-megamenu').style.paddingBottom = 0 + 'px';
    }
    return false;
  },
  clickOutside: function(){
    var specifiedElement = document.getElementsByClassName('js-navbar__menu');
    //I'm using "click" but it works with any event
    document.addEventListener('click', function(event) {
      if( window.innerWidth > 1024){
        var isClickInside = specifiedElement[0].contains(event.target);
        if(document.querySelector('.has-megamenu--expand') != null){
          if (!isClickInside) {
            //the click was outside the specifiedElement, do something
            megamenu.closeAllMegamenus();
            _closeBackdrop();
          }
        }
      }
    });
  },
  colseOnEscapeKey: function(){
    // close on escape
    document.addEventListener('keydown', function(evt) {
      // var expanded = dropwdown_btn.getAttribute('aria-expanded') === "true";
      evt = evt || window.event;
      var isEscape = false;
      if ("key" in evt) {
        isEscape = (evt.key === "Escape" || evt.key === "Esc");
      } else {
        isEscape = (evt.keyCode === 27);
      }
      if (isEscape) {
        if(document.querySelector('.has-megamenu--expand') != null){
          megamenu.closeAllMegamenus();
          _closeBackdrop();
        }
      }
    });
  },
  animateMegamenuIn: function($megamenu){
    function quad(timeFraction) {
      return Math.pow(timeFraction, 2)
    }
    animate({
      duration:300,
      timing: quad,
      draw: function(progress) {
        $megamenu[0].style.maxHeight = progress * 999 + 'px';
        if(window.innerWidth > 1024) {
          $megamenu[0].style.paddingTop = progress * 15 + 'px';
          $megamenu[0].style.paddingBottom = progress * 15 + 'px';
        }
      }
    });
    // $megamenu[0].style.height = "500px";

  },
  animateMegamenuOut: function($megamenu){
    function quad(timeFraction) {
      return Math.pow(timeFraction, 2)
    }
    animate({
      duration:300,
      timing: quad,
      draw: function(progress) {
        $megamenu[0].style.maxHeight = progress * 0 + 'px';
        if(window.innerWidth > 1024) {
          $megamenu[0].style.paddingTop = progress * 0 + 'px';
          $megamenu[0].style.paddingBottom = progress * 0 + 'px';
        }
      }
    });
    // $megamenu[0].style.height = "0px";
  },
  activeSubmenu: function(){
    var element = document.querySelectorAll('.js-has-megamenu');
    for (var index = 0; index < element.length; index++) {

      var btn = element[index].querySelectorAll('.js-NG-megamenu__nav-submenu');
      for (var j = 0; j < btn.length; ++j) {
        btn[j].addEventListener('click', _onClickSubmenu, true );
        btn[j].addEventListener("keyup", function(event) {
          if (event.keyCode === 13) {
            this.click();
          }
        });
      }

    }
  },
  closeAllSubmenus: function(el){
    var btn = el.querySelectorAll('.js-NG-megamenu__nav-submenu');
    el.closest('.js-navbar__menu').style.height = "";
    el.closest('.js-navbar__menu').classList.remove('NG-navbar__menu--visible');
    // open description
    megamenu._openDescription(el);
    for (var j = 0; j < btn.length; ++j) {
      btn[j].classList.remove('NG-megamenu__nav-submenu--active');
      ariaExpandFn(btn[j], false);
      btn[j].closest(".NG-megamenu__nav-item").classList.remove('NG-megamenu__nav-item--active');
    }
  },
  closeSubmenus: function(thisSubmenus){

    var navItem = thisSubmenus.closest(".NG-megamenu__nav-item")
    var siblings = Array.prototype.filter.call(navItem.parentNode.children, function(child){
      return child !== navItem;
    });


    for (var k = 0; k < siblings.length; ++k) {
      var children = siblings[k].querySelectorAll('.js-NG-megamenu__nav-submenu');

      for (var l = 0; l < children.length; ++l) {
        children[l].classList.remove('NG-megamenu__nav-submenu--active');
        ariaExpandFn(children[l], false);
        children[l].closest(".NG-megamenu__nav-item").classList.remove('NG-megamenu__nav-item--active');
      }
      // console.log(siblings[k].children[0], "l");

      siblings[k].classList.remove('NG-megamenu__nav-item--active');
      siblings[k].children[0].classList.remove('NG-megamenu__nav-submenu--active');
    }
  },
  goBackSubmenus: function(){
    var goBackBtn = document.querySelectorAll('.js-NG-megamenu__nav-link-back');
    for (var index = 0; index < goBackBtn.length; ++index){
      goBackBtn[index].addEventListener('click', function(){
        // console.log('test', this.closest('.NG-megamenu__nav-item--active'));

        var navItemActive = this.closest('.NG-megamenu__nav-item--active');
        // console.log(navItemActive);
        if(navItemActive === null){
          // First Level
          navItemActive = this.closest('.has-megamenu--expand');
          var submenuBase = navItemActive.querySelectorAll('.NG-navbar__link');
          var navItem = submenuBase[0].closest(".js-has-megamenu")
          var noSiblings = Array.prototype.filter.call(navItem.parentNode.children, function(child){
            return child == navItem;
          });

          var parentWrapper =  this.closest('.NG-megamenu__nav-wrapper');
          this.closest('.js-navbar__menu').style.height = "auto";
          this.closest('.js-navbar__menu').classList.remove('NG-navbar__menu--visible');
          this.closest('.js-navbar__menu').classList.add('NG-navbar__menu--visible');

          // this.closest('.js-navbar__menu').style.height = _navWrapperSize(parentWrapper);
          // console.log('first level', this.closest('.NG-megamenu'));

          for (var k = 0; k < noSiblings.length; ++k) {
            var children = noSiblings[k].querySelectorAll('.js-NG-megamenu__nav-submenu');

            // console.log(noSiblings[k].children[0], "l");
            noSiblings[k].classList.remove('has-megamenu--expand');
            noSiblings[k].children[0].setAttribute("aria-expanded", "false");
          }

        } else {
          // Rest of levels
          var submenuBase = navItemActive.querySelectorAll('.js-NG-megamenu__nav-submenu');
          var navItem = submenuBase[0].closest(".NG-megamenu__nav-item")
          var noSiblings = Array.prototype.filter.call(navItem.parentNode.children, function(child){
            return child == navItem;
          });
          var parentWrapper =  this.closest('.NG-megamenu__nav-item').closest('.NG-megamenu__nav-wrapper');
          // this.closest('.js-navbar__menu').style.height = "";
          this.closest('.js-navbar__menu').style.height = _navWrapperSize(parentWrapper) != null ? _navWrapperSize(parentWrapper) : "";
          this.closest('.js-navbar__menu').classList.remove('NG-navbar__menu--visible');
          // console.log('rest level', this.closest('.NG-megamenu__nav-item').closest('.NG-megamenu__nav-wrapper'));

          for (var k = 0; k < noSiblings.length; ++k) {
            var children = noSiblings[k].querySelectorAll('.js-NG-megamenu__nav-submenu');

            for (var l = 0; l < children.length; ++l) {
              children[l].classList.remove('NG-megamenu__nav-submenu--active');
              children[l].setAttribute("aria-expanded", "false");
              children[l].closest(".NG-megamenu__nav-item").classList.remove('NG-megamenu__nav-item--active');
            }
            // console.log(noSiblings[k].children[0], "l");

            noSiblings[k].classList.remove('NG-megamenu__nav-item--active');
            noSiblings[k].children[0].classList.remove('NG-megamenu__nav-submenu--active');
          }
        }

      })
    }

  },
  goBackSubmenusFirstLevel: function(){
    var goBackBtn = document.querySelectorAll('.js-NG-megamenu__nav-link-close');
    for (var index = 0; index < goBackBtn.length; ++index){
      goBackBtn[index].addEventListener('click', function(){
        var menu = document.getElementsByClassName('NG-navbar__link');
        megamenu.closeAllSubmenus(menu);
      });
    }
  },
  limitLevel: function(isTablet){
    var topLevelWrapper = document.querySelectorAll('.js-NG-top-level');
    if(topLevelWrapper.length > 0){
      for (var i = 0; i < topLevelWrapper.length; i++) {
        var wrapper = topLevelWrapper[i];
        var el = wrapper.querySelectorAll('.js-NG-megamenu__nav-submenu');


        for (var index = 0; index < el.length; index++){
          // console.log(el[index], index);
          var link = el[index].getAttribute('data-link');

          if(isTablet){
            if (!el[index].classList.contains('NG-megamenu__nav-submenu')) {
              el[index].classList.add("NG-megamenu__nav-submenu");
            }
            // el[index].addEventListener('click', _onClickSubmenu, true);
            el[index].setAttribute("aria-expanded", "false");
            el[index].setAttribute("role", "button");
            el[index].removeAttribute("href");
          } else {
            // el[index].removeEventListener('click', _onClickSubmenu, true);
            el[index].classList.remove("NG-megamenu__nav-submenu");
            el[index].removeAttribute("role");
            el[index].removeAttribute("aria-expanded");
            el[index].setAttribute("href", link);
          }
        }
      }
    }
  },
  _openDescription: function(el){

    var descriptionWrapper = el.querySelectorAll(".js-NG-hide-description");
    descriptionWrapper[0].classList.remove("js-NG-hide-description--close");
  },
  _closeDescription: function(el){
    var descriptionWrapper = el.querySelectorAll(".js-NG-hide-description");
    if (!descriptionWrapper[0].classList.contains('js-NG-hide-description--close')) {
      descriptionWrapper[0].classList.add("js-NG-hide-description--close");
    }
  },
  simplebar: function(){
    if( NG_ISIEVersion != 10 ){
      Array.prototype.forEach.call(
          document.querySelectorAll('.js-NG-simplebar'),
          function(el){
            new SimpleBar(el);
            SimpleBar.removeObserver();
          }
      );
    }
  },
  init: function(){
    var hasMegamenu = document.querySelectorAll('.js-has-megamenu');
    var isTablet = window.innerWidth < 1025;
    var isMobile = window.innerWidth < 768;
    megamenu.limitLevel(isTablet);

    megamenu.openMegamenu();
    megamenu.activeSubmenu();
    megamenu.clickOutside();
    megamenu.colseOnEscapeKey();


    megamenu.goBackSubmenus();
    megamenu.goBackSubmenusFirstLevel();
    if(!isTablet){
      megamenu.simplebar();
    }
  },
  refreshResize: function(){
    var isTablet = window.innerWidth < 1025;
    megamenu.limitLevel(isTablet);
  }
}

var megamenuIE = {
  positionSubnivel: function(ie, listElActive){
    if(ie){
      var children = listElActive.parentNode.childNodes;
      var menuWrapper;
      for (var i = 0; i < children.length; i++) {
        if (children[i].classList != undefined &&
            children[i].classList.contains('NG-megamenu__nav-wrapper') ) {
          menuWrapper = children[i];
          break;
        }
      }

      if (menuWrapper != undefined){
        var level = menuWrapper.getAttribute('data-level');

        switch (level) {
          case "2":
            megamenuIE.setPosition(menuWrapper, 1);
            break;
          case "3": // foo es 0, por lo tanto se cumple la condición y se ejecutara el siguiente bloque
            megamenuIE.setPosition(menuWrapper, 2);
            break;
          default:
            // console.log('default '+ level);
        }
      }
    }
  },
  initIEAnimation: function(ie){
    if(ie){
      var menuWrapper = document.querySelectorAll('.NG-megamenu__nav-wrapper')[0];

      if (menuWrapper != undefined){
        megamenuIE.setIEAnimation(menuWrapper);
      }
    }
  },
  setPosition: function(wrapper, offsetBy){
    var megamenuWrapper = wrapper.closest('.js-NG-megamenu-wrapper');
    var height = megamenuWrapper.clientHeight;
    var rect = megamenuWrapper.getBoundingClientRect();
    // console.log("wrapper", wrapper);

    wrapper.style.top = rect.top + "px";
    // console.log(rect.left);
    wrapper.style.left = rect.left + (380 * offsetBy) + "px";
    wrapper.style.maxHeight = height + "px";
    window.addEventListener('scroll', function(){
      rect = megamenuWrapper.getBoundingClientRect();
      wrapper.style.top = rect.top + "px";
    })
  },
  setIEAnimation: function(wrapper){
    var megamenuWrapper = document.querySelectorAll('.NG-header__image')[0];
    var rect = megamenuWrapper.getBoundingClientRect();
    L3offset = rect.left + (380 * 1) + "px";
    L4offset = rect.left + (380 * 2) + "px";
    // console.log("left", rect.left);

    // set animation
    var ieStyleAnimation = document.createElement('style');
    ieStyleAnimation.type = 'text/css';
    var keyFrames = '\
        @keyframes leftL3 {\
            0% {\
                left: A_DYNAMIC_VALUE_L3_0;\
            }\
            100% {\
                left: A_DYNAMIC_VALUE_L3;\
            }\
        }\
        @keyframes leftL4 {\
            0% {\
                left: A_DYNAMIC_VALUE_L4_0;\
            }\
            100% {\
                left: A_DYNAMIC_VALUE_L4;\
            }\
        }';
    ieStyleAnimation.innerHTML = keyFrames.replace(/A_DYNAMIC_VALUE_L3/g, L3offset)
        .replace(/A_DYNAMIC_VALUE_L4/g, L4offset)
        .replace(/A_DYNAMIC_VALUE_L3_0/g, rect.left)
        .replace(/A_DYNAMIC_VALUE_L4_0/g, rect.left);
    document.getElementsByTagName('head')[0].appendChild(ieStyleAnimation);
  }
}

// Navbar Position and Height
function setNavbarPosition(){
  var upHeader = document.querySelector('.NG-header__container');
  var navBar = document.querySelector('.NG-navbar');

  if (navBar != null && upHeader != null) {
    if(window.innerWidth > 1024){
      navBar.style.top = "";
    } else {
      var headerHeight = upHeader.querySelector('.NG-main').offsetHeight;
      navBar.style.top = (headerHeight - 1) + "px";
    }
  }
}

// Main menu displacement
var mainMenuDisplacement = {
  mainMenu: function(){
    var menu = document.getElementsByClassName('js-NG-navbar__menu');
    return menu;
  },
  desplacement: function(){

    var desktopWrapper = document.getElementsByClassName('NG-navbar__menu-desktop--wrapper');
    var mobileWrapper = document.getElementsByClassName('NG-navbar__menu-mobile--wrapper');
    var menu = mainMenuDisplacement.mainMenu();

    var w = window.innerWidth;
    if(w < 1025){
      var menuDetached = menu[0];
      menu[0].parentNode.removeChild(menu[0]);
      mobileWrapper[0].appendChild(menuDetached);
    } else {
      var menuDetached = menu[0];
      menu[0].parentNode.removeChild(menu[0]);
      desktopWrapper[0].appendChild(menuDetached);
    }

    //Remove hide-mobile class to show elements
    desktopWrapper[0].classList.remove("hide-mobile");
  }
}

// Helper functions
// Accessibility
var ariaExpandFn = function(e, setExpand){
  if(setExpand == null){
    var isExpanded = e.getAttribute("aria-expanded");
    if (isExpanded == "true") {
      isExpanded = "false"
    } else {
      isExpanded = "true"
    }
    e.setAttribute("aria-expanded", isExpanded);
  } else {
    e.setAttribute("aria-expanded", setExpand);
  }
}

function _onClickSubmenu(){
  var _this = this;

  megamenu.closeSubmenus(_this);
  var el = this.closest('.js-has-megamenu');
  var wrapper = this.closest(".NG-megamenu__nav-item").querySelectorAll('.NG-megamenu__nav-wrapper')[0];
  // close description;
  megamenu._closeDescription(el);
  this.classList.add('NG-megamenu__nav-submenu--active');
  ariaExpandFn(this, true);
  this.closest(".NG-megamenu__nav-item").classList.add('NG-megamenu__nav-item--active');
  // console.log(this.closest(".NG-megamenu__nav-item").querySelectorAll('.NG-megamenu__nav-wrapper')[0]);
  this.closest(".NG-megamenu__nav-item").querySelectorAll('.NG-megamenu__nav-wrapper')[0].scrollTop = 0;
  // this.closest('.js-navbar__menu').style.height = "";
  this.closest('.js-navbar__menu').style.height = _navWrapperSize(wrapper) != null ? _navWrapperSize(wrapper) : "";
  this.closest('.js-navbar__menu').classList.remove('NG-navbar__menu--visible');
  // console.log(_this);

  megamenuIE.positionSubnivel(NG_ISIE, this);
}

function _navWrapperSize(navWrapper){
  // console.log('navWrapper', navWrapper);
  if(window.innerWidth < 1025){
    var selfNavTop = navWrapper.querySelectorAll('.NG-megamenu__nav-self--top')[0];
    var listItems = navWrapper.querySelectorAll('.NG-megamenu__nav')[0];
    var listItemsChildren = listItems.children;
    var totalHeight = 0;

    listItemsChildren.forEach(function(element){
      totalHeight = totalHeight + element.offsetHeight;
    })
    totalHeight = totalHeight + selfNavTop.offsetHeight;
    return totalHeight+'px';
  } else {
    return "";
  }

}

var capcaleraSticky = {
  lastScrollTop : 0,
  /*
    Limits the rate at which a function is executed
    @param {Function}   func    The function to call
  */
  debounce: function(func) {
    var wait = 20;          //Time between calls in milliseconds
    var immediate = true;   //If true the function is called immediately after the wait period

    var timeout;
    return function() {
      var context = this, args = arguments;
      var later = function() {
        timeout = null;
        if (!immediate) func.apply(context, args);
      };
      var callNow = immediate && !timeout;
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
      if (callNow) func.apply(context, args);
    };
  },
  /*
    Sets the height of the header container based on its contents
    to avoid height issues when its positioning is fixed
  */
  initCapcaleraHeader: function(){
    var headerContainerHeight = document.querySelector('.NG-header__wrapper').offsetHeight +
        document.querySelector('.NG-main').offsetHeight;

    document.querySelector('.NG-header__container').style.height = headerContainerHeight;
    document.querySelector('.NG-header__container').setAttribute("style", "height:" + headerContainerHeight + "px; position:relative;");
  },
  /*
    Sets the capcalera header to fixed or relative depending on top position relative to the viewport
    Set to fixed when capcalera header top position is 0 and relative otherwise
  */
  /*stickyHeaderMobile: function(){
    if(window.innerWidth <= 1024){
      var scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      var headerWrapper = document.querySelector('.NG-header__container');
      var headerViewportY = headerWrapper.getBoundingClientRect().y;

      if (scrollTop > capcaleraSticky.lastScrollTop){
        //downscroll (elements go up)

        if(headerViewportY <= 0) {
          //Fix header at the top of the viewport
          capcaleraSticky._headerFixed();
        }
      } else {
        //upscroll (elements go up)
        if(headerViewportY > 0) {
          //Relative if the header surpasses the top (for cookie consent)
          capcaleraSticky._headerRelative();
        } else {
          //Fixed before the header reaches the top
          capcaleraSticky._headerFixed();
        }
      }

      capcaleraSticky.lastScrollTop = scrollTop <= 0 ? 0 : scrollTop; // For Mobile or negative scrolling
    } else {
      //innerWidth > 1024
      capcaleraSticky._headerRelative();
    }
  },
  _headerRelative : function() {
    document.querySelector(".NG-header__container").classList.remove("NG-header__container--fixed");
    document.querySelector(".NG-header__container").classList.add("NG-header__container--relative");
  },
  _headerFixed : function() {
    document.querySelector(".NG-header__container").classList.remove("NG-header__container--relative");
    document.querySelector(".NG-header__container").classList.add("NG-header__container--fixed");
  },*/
  /*
      Apply a specific offset vertically when scrolling
      @param {number} offset Vertical scroll offset (in pixels)
  */
  offsetScrollCapcalera: function() {
    var offset = capcaleraSticky.stickyHeaderOffset();

    if (location.hash.length !== 0) {
      window.scrollTo(window.scrollX, window.scrollY - offset);
    }
  },
  /*
    Calculates scroll offset to apply so the sticky header
    doesn't partially hide the element when scrolling to it
  */
  stickyHeaderOffset: function() {
    var headerOffset = 0;
    var heightToOffsetFactor = window.innerWidth > 1024 ? 0.6 : 1;
    var headerHeight = 0;

    /*
    //COOKIE CONSENT IS NOT FIXED
    //Check if cookies are set and get height
    if (document.querySelector('.cookieConsentWrapper')) {
        headerHeight += document.querySelector('.cookieConsentWrapper').offsetHeight;
    }*/

    //Calculate header height for desktop/mobile
    if(window.innerWidth > 1024){
        if (document.querySelector('.secondary-header')) {
            headerHeight += document.querySelector('.secondary-header').offsetHeight;
        }
    } else {
        if (document.querySelector('.NG-main')) {
            headerHeight += document.querySelector('.NG-main').offsetHeight;
        }
        if (document.querySelector('.NG-departament')) {
            headerHeight += document.querySelector('.NG-departament').offsetHeight;
        }
    }

    headerOffset = heightToOffsetFactor * headerHeight;
    return headerOffset;
  }
};


function ready() {
  if (document.readyState != 'loading'){

  } else {
    document.addEventListener('DOMContentLoaded', function(){
      navbarCapcalera.init();
      megamenu.init();
      mainMenuDisplacement.desplacement();
      setNavbarPosition();
      megamenuIE.initIEAnimation(NG_ISIE);
      capcaleraSticky.initCapcaleraHeader();

      window.addEventListener('resize', function(){
        if(window.innerWidth > 1024){
          navbarCapcalera.refresh();
        }

        mainMenuDisplacement.desplacement();
        megamenu.refreshResize();
        setNavbarPosition();
        //capcaleraSticky.debounce(capcaleraSticky.stickyHeaderMobile());
      });

      window.addEventListener("scroll", function(){
        //capcaleraSticky.debounce(capcaleraSticky.stickyHeaderMobile());
      });

      //Offset scroll when anchor with href starting with # is clicked
      $(document).on('click', 'a[href^="#"]', function(event) {
          window.setTimeout(function() {
              capcaleraSticky.offsetScrollCapcalera();
          }, 0);
      });

      //Offset scroll when hash is present in the url
      window.setTimeout(function(){
        if(window.location.href.indexOf("#") > 0){
          capcaleraSticky.offsetScrollCapcalera();
        }
      }, 0);
    });
  }
};

ready();

/****************************************************************/

/*--- INICI TranslateAlVol---*/
function googleTranslateElementInit() {
  new google.translate.TranslateElement(
      {
        pageLanguage: 'ca',
        layout: google.translate.TranslateElement.FloatPosition.TOP_LEFT,
        includedLanguages: availableLocalesTranslate
      },
      'google_translate_element');
}

function triggerHtmlEvent(element, eventName) {
  var event;
  if (document.createEvent) {
    event = document.createEvent('HTMLEvents');
    event.initEvent(eventName, true, true);
    element.dispatchEvent(event);
  } else {
    event = document.createEventObject();
    event.eventType = eventName;
    element.fireEvent('on' + event.eventType, event);
  }
}

function findGetParameter(parameterName) {
  var result = null,
      tmp = [];
  location.search
      .substr(1)
      .split("&")
      .forEach(function (item) {
        tmp = item.split("=");
        if (tmp[0] === parameterName) result = decodeURIComponent(tmp[1]);
      });
  return result;
}


function createCookie(name, value, days, domain) {
  if (days) {
    var date = new Date();
    date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
    var expires = "; expires=" + date.toGMTString();
  } else {
    var expires = "";
  }
  document.cookie = name + "=" + value + expires + "; domain=" + domain + "; path=/";
}

function eraseCookie(name, domain) {
  createCookie(name, "", -1, domain);
}

function getCookie(name) {
  function escape(s) { return s.replace(/([.*+?\^${}()|\[\]\/\\])/g, '\\$1');
  };
  var match = document.cookie.match(RegExp('(?:^|;\\s*)' + escape(name) + '=([^;]*)'));
  return match ? match[1] : null;
}

function changeLangButtons(){
  if(getCookie('googtrans')){
    var langs = getCookie('googtrans').split("/");
    if($("ul.idioma li a.lang-"+langs[2]) || $("header .idiomes a.lang-"+langs[2])){
      var langButton = $("ul.idioma li a.lang-"+langs[2]).length > 0 ? $("ul.idioma li a.lang-"+langs[2]) : $("header .idiomes a.lang-"+langs[2]);
      langButton.removeClass();
      langButton.removeAttr("data-lang");
      if(window.innerWidth > 768){
        langButton.removeAttr("data-content");
        langButton.removeAttr("data-toggle");
        langButton.removeAttr("data-html");
        langButton.removeAttr("data-placement");
        langButton.popover("disable");
      }

      // Update the text of the current language
      document.querySelector(".js-NG-idioma-text").innerText = langs[2];

      var localeToDesc = { ca: 'Català', es: 'Castellano', en:'English', fr:'Français', de:'Deutsch' , pt:'Portugues', it:'Italiano' };
      // Update text from language link to dropdown
      langButton.text(localeToDesc[langs[1]]);
      langButton.addClass('notranslate');
      langButton.attr('href', location.href.replace(location.hash,""));
      $("ul.idioma li a").not(".t-automatica").not(".condicionsTranslateAlVol").click(function(){
        deleteGTransCookies();
        if(!isNaN(detectIE())){
          location.hash = "";
          location.href = langButton.attr('href');
        }
      });

    }
  } else {
    //changeLangButtons();
  }
}


function deleteGTransCookies(){
  hostnameParts = window.location.hostname.split(".");
  hostnameTmp="";
  for(var i = hostnameParts.length-1; i>-1; i--){
    hostnameTmp = hostnameParts[i] + hostnameTmp;
    eraseCookie("googtrans", hostnameTmp);
    hostnameTmp = "." + hostnameTmp;
    eraseCookie("googtrans", hostnameTmp);
  }
  eraseCookie("googtrans", "."+window.location.hostname);
  eraseCookie("googtrans", "");
}
function setGTransCookies(value){
  hostnameParts = window.location.hostname.split(".");
  hostnameTmp="";
  for(var i = hostnameParts.length-1; i>-1; i--){
    hostnameTmp = hostnameParts[i] + hostnameTmp;
    createCookie("googtrans", value, 1, hostnameTmp);
    hostnameTmp = "." + hostnameTmp;
    createCookie("googtrans", value, 1, hostnameTmp);
  }
  eraseCookie("googtrans", value, 1, "."+window.location.hostname);
  eraseCookie("googtrans", value, 1, "");
}

function detectIE() {
  var ua = window.navigator.userAgent;

  var msie = ua.indexOf('MSIE ');
  if (msie > 0) {
    // IE 10 or older => return version number
    return parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
  }

  var trident = ua.indexOf('Trident/');
  if (trident > 0) {
    // IE 11 => return version number
    var rv = ua.indexOf('rv:');
    return parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
  }

  var edge = ua.indexOf('Edge/');
  if (edge > 0) {
    // Edge (IE 12+) => return version number
    return parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
  }

  // other browser
  return false;
}


/*--- FI TranslateAlVol---*/


$(window).ready(function() {

  /*--- INICI TranslateAlVol---*/

  jQuery('.lang-select').click(function(e) {
    var theLang = jQuery(this).attr('data-lang');
    jQuery('.goog-te-combo').val(theLang);

    window.location = jQuery(this).attr('href');
    location.reload(true);
  });

  /*--- FI TranslateAlVol---*/

});


$(window).on('load', function(){
  //FIX IE
  if($("ul.idioma li a.t-automatica")){
    if(!isNaN(detectIE())){
      $("ul.idioma li a.t-automatica").unbind("click");

      //Gestió del click pels links de traducció al vol excepte l'occità
      $("ul.idioma li a.t-automatica").not(".lang-oc").click(function(){
        //Actualitzar la cookie googtrans
        deleteGTransCookies();
        console.log("Update googtrans cookie to: /ca/"+$(this).attr('lang'));
        setGTransCookies("/ca/"+$(this).attr('lang'));
        window.location = $(this).attr("href");
        location.reload(true);
      });

      //Gestió del click pels link de traducció al vol en occità
      $("ul.idioma li a.t-automatica.lang-oc").click(function(){
        window.location.href = $(this).attr("href");
      });
    }
  }
  if($("a.condicionsTranslateAlVol")){
    $("a.condicionsTranslateAlVol").unbind('click');
  }

  changeLangButtons();
});

(function($) {
  'use strict';

  $.fn.succinct = function(options) {
    var settings = $.extend({size: 240,
      omission: '...',
      ignore: true
    }, options);

    return this.each(function() {
      var textDefault,
          textTruncated,
          elements = $(this),
          regex    = /[!-\/:-@\[-`{-~]$/,
          init     = function() {
            elements.each(function() {
              textDefault = $(this).html();

              if (textDefault.length > settings.size) {
                textTruncated = $.trim(textDefault)
                    .substring(0, settings.size)
                    .split(' ')
                    .slice(0, -1)
                    .join(' ');

                if (settings.ignore) {
                  textTruncated = textTruncated.replace(regex, '');
                }
                $(this).html(textTruncated + settings.omission);
              }
            });
          };
      init();
    });
  };
})(jQuery);