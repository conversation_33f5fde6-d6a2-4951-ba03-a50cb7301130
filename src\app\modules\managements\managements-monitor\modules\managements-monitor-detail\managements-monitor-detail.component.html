<app-detail-page
	[pageTitle]="'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.DETAIL.PAGE_TITLE'"
	[statesTableTranslation]="'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.STATES'"
	[detailCardTitle]="'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.DETAIL.CARD.TITLE'"

	[procedureData]="management"
	[idEntity]="idEntity"
	[idTramit]="idEntity"
	[idReceipt]="idReceipt"

	[stateChangeFirstDate]="management?.dataAlta"
	[stateChanges]="stateChanges"
	[stateIcon]="stateIcon"
	[stateLabel]="stateLabel"
	[detailCardFields]="detailCardFields"
	[detailTabInput]="detailTabInput"

	[currentIndex]="currentIndex"
	[totalRows]="totalRows"
	[backUrl]="navigateBackUrl"

	(getProcedureList)="getManagementList()"
	(getProcedure)="getManagement()"
	(showErrors)="onShowErrors()"

	(incrementIndex)="currentIndex = currentIndex + 1"
	(decrementIndex)="currentIndex = currentIndex - 1"
>
</app-detail-page>
