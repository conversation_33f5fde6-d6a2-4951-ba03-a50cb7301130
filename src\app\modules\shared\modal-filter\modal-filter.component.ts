import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
	PtMessageI,
	PtSelectCardModel,
} from 'pt-ui-components-mf-lib';
import {
	FilterModalInput,
} from './models/modal-filter.model';
import { TranslateService } from '@ngx-translate/core';
import { FilterFieldType, FilterFieldsData } from '../filter-inputs';

@Component({
	selector: 'app-modal-filter',
	templateUrl: './modal-filter.component.html'
})
export class ModalFilterComponent implements OnInit {

	@Input() modalInput: FilterModalInput;

	@Output() modalOutput: EventEmitter<any> = new EventEmitter<any>();

	// Options
	stateOptions: PtSelectCardModel[] = [];

	// Filter
	filterForm: FormGroup;
	fieldRows: FilterFieldsData[][] = [[]];

	// Other
	confirmationData: PtMessageI;
	filterDataLoaded: Promise<boolean>;

	// Enums
	FilterFieldType = FilterFieldType;

	constructor(
		private fb: FormBuilder,
		private activeModal: NgbActiveModal,
		private translateService: TranslateService
	) { }

	ngOnInit(): void {
		this.setUpModalData();

		this.setupFilterFieldsData();

		this.setForm(this.modalInput.formValue);
	}

	private setUpModalData(): void {
		this.confirmationData = {
			severity: null,
			title: null,
			closableFn: this.closeModal.bind(this)
		};
	}

	private setupFilterFieldsData () {
		this.fieldRows = this.modalInput.formFieldsData.map(row => {
			return row.map(fieldData => {
				return {
					...fieldData,
					options: fieldData?.options || this.setOptionsList(fieldData?.optionsValue, fieldData?.optionsLabelTranslations)
				}
			})
		})
	}

	private setOptionsList = (enumValues?: [string,string][], translations?: string): PtSelectCardModel[] | null => {
		if(!enumValues) return null;

		let options = [];

		enumValues.forEach(value => {
			const label = translations ? this.translateService.instant(`${translations}.${value[0]}`) : value[0];
			options.push(
				{ id: value[1], label:  label}
			)
		});

		options.sort(this.sortOptionsFn);

		return options;
	}

	private sortOptionsFn = (a:PtSelectCardModel, b:PtSelectCardModel) => {
		if (a.label > b.label) return 1

		if (b.label > a.label) return -1

		return 0;
	}

	private setForm = (data: any): void => {
		this.filterForm = this.fb.group(
			this.setupFormGroup(data),
			{
				validators: this.modalInput.formGroupValidations
			}
		);
	}

	private setupFormGroup(data: any): {[key: string]: any} {
		let fields = {};

		[].concat(...this.modalInput.formFieldsData).forEach((field: FilterFieldsData) => {
			if(field.fieldType !== FilterFieldType.keyValue) {
				fields[field.id] = [data?.[field.id], field.validations];
			} else {
				fields[field.id] = this.fb.group({});

				for(let i = 0; i < field.numOptions; i++){
					fields[field.id].addControl(field.id + '_key_' + i, this.fb.control(data?.[field.id]?.[i]?.key, field.validations));
					fields[field.id].addControl(field.id + '_value_' + i, this.fb.control(data?.[field.id]?.[i]?.value, field.validations));
				}
			}
		});

		return fields;
	}

	clearFilter = (): void => {
		this.filterForm.reset(this.modalInput.formResetValue);
	}

	search(){
		let filterData = this.filterForm.value;

		[].concat(...this.modalInput.formFieldsData).forEach((field: FilterFieldsData) => {
			if(field.fieldType === FilterFieldType.keyValue) {
				const group = this.filterForm.controls[field.id] as FormGroup;
				const keys = Object.keys(group.controls).filter(control => control.includes('key_'));
				const values = Object.keys(group.controls).filter(control => control.includes('value_'));

				filterData[field.id] = keys.filter((key, index) => {
					return this.isNotEmpty(group.controls[key].value) && this.isNotEmpty(group.controls[values[index]].value);
				})
				.map((key) => {
					const keyIndex = keys.indexOf(key); 
					
					return { key: group.controls[key].value, value: group.controls[values[keyIndex]].value };
				})
			}
		});
		this.modalOutput.emit(this.filterForm.value);
		this.activeModal.close();
	}

	private isNotEmpty(value: any): boolean {
		if (value === null || value === undefined) return false;
		if (typeof value === 'string' && value.trim() === '') return false;
		if (Array.isArray(value) && value.length === 0) return false;
		if (typeof value === 'object' && Object.keys(value).length === 0) return false;
		return true;
	}

	closeModal(){
		this.activeModal.close();
	}

}
