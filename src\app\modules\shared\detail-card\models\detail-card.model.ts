export enum DetailFieldType {
  text = 'text',
  link = 'link',
  date = 'date',
  number = 'number',
  translate = 'translate',
  map = 'map',
  icon = 'icon',
	state = 'state',
}

export interface DetailCardData {
  id: string,
  fieldType: DetailFieldType,
  fieldLabel: string,
  fieldNumberMode?: string
  fieldContainerClass?: string,
  fieldValueTranslation?: string,
  fieldMap?: Map<string,string>
	fieldValue?: string,
}
