export enum ExportColumnType {
  text = 'text',
  date = 'date',
  toReview = 'toReview',
  presentationState = 'presentationState',
  translation = 'translation',
  textToBoolean = 'textToBoolean',
  stateChanges = 'stateChanges',
  errors = 'errors',
  array = 'array',
  boolean = 'boolean'
}
export type ExportColumnTypeT = keyof typeof ExportColumnType;
export type ExportColumnTypeValues<ExportColumnType> = ExportColumnType[keyof ExportColumnType];

export interface ExportColumnsData {
  id: string,
  columnTitle: string,
  columnType: ExportColumnTypeT,
  translation?: string,
  attr?: string,
  arrayPos?: 0 | -1; // -1 == last position
}