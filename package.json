{"name": "pt-portal-tracking-gw", "version": "0.182.0-snapshot", "publishConfig": {"registry": "https://slmaven.indra.es/nexus/repository/ATCMP_Npm/"}, "scripts": {"ng": "ng", "start": "ng serve --configuration loc --port 4215", "start:loc": "ng serve --configuration loc --port 4215", "start:dev": "ng serve --configuration dev --port 4215 --base-href /tracking", "build": "ng build", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "build:prod": "ng build --prod --base-href /tracking/"}, "private": false, "dependencies": {"@angular-builders/custom-webpack": "11.1.1", "@angular-extensions/elements": "11.0.1", "@angular/animations": "11.2.4", "@angular/cdk": "11.2.4", "@angular/common": "11.2.4", "@angular/compiler": "11.2.4", "@angular/core": "11.2.4", "@angular/elements": "11.2.4", "@angular/forms": "11.2.4", "@angular/localize": "11.2.4", "@angular/platform-browser": "11.2.4", "@angular/platform-browser-dynamic": "11.2.4", "@angular/router": "11.2.4", "@googlemaps/js-api-loader": "1.11.3", "@ng-bootstrap/ng-bootstrap": "9.0.2", "@ngx-translate/core": "13.0.0", "@ngx-translate/http-loader": "6.0.0", "@webcomponents/custom-elements": "1.4.3", "@webcomponents/webcomponentsjs": "2.5.0", "bootstrap": "4.6.0", "concat": "1.0.3", "crypto-js": "4.1.1", "document-register-element": "1.14.10", "file-saver": "2.0.5", "fs-extra": "9.1.0", "ngx-cookie-service": "11.0.2", "ngx-translate-multi-http-loader": "3.0.0", "primeicons": "4.1.0", "primeng": "11.4.5", "pt-ui-components-mf-lib": "0.203.0", "quill": "1.3.7", "rxjs": "6.6.0", "tslib": "2.0.0", "xlsx": "0.17.0", "zone.js": "0.11.3"}, "devDependencies": {"@angular-devkit/build-angular": "0.1102.3", "@angular/cli": "11.2.3", "@angular/compiler-cli": "11.2.4", "@types/jasmine": "3.6.0", "@types/node": "12.11.1", "codelyzer": "6.0.0", "jasmine-core": "3.6.0", "jasmine-spec-reporter": "5.0.0", "karma": "6.1.0", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.0.3", "karma-jasmine": "4.0.0", "karma-jasmine-html-reporter": "1.5.0", "protractor": "7.0.0", "ts-node": "8.3.0", "tslint": "6.1.0", "typescript": "4.1.5"}}