.footer-social {
    background-color: #F5F5F5;
}

.footer-social .footer-social__wrapper {
    width: 95%;
    margin:0 auto;
}

.footer-social .footer-social__wrapper__list {
    display: flex;
    padding-left: 0;
}

.footer-social .footer-social__wrapper .footer-social__text {
    text-align: center;
    max-width: 100%;
    margin-left: auto;
    margin-right: auto;
    margin-top: 0;
}

.footer-social .footer-social__wrapper .footer-social__text .footer-social__text-title {
    font-size: 14px;
    color: #333;
    font-family: OpenSans_Regular;
    margin-bottom: 12px;
}

.footer-social .footer-social__wrapper .footer-social__list {
    display: inline-flex;
    width: 100%;
    justify-content: center;
    margin-bottom: 22px;
}

.footer-social .footer-social__wrapper .footer-social__list .footer-social__list-item {
    margin-right: 10px;
}

.footer-social .footer-social_border {
    background-color:transparent;
    height: 3px;
    width: 100%;
}

.footer-social .footer-social_border .footer-social_border--line {
    display: block;
    border-top: 1px solid #ddd;
    padding-top: 3px;
    margin: 0 15px;
}

@media (max-width: 1025px){
    .footer-social .footer-social_border {
        margin: auto;

    }
    
    .footer-social .footer-social_border .footer-social_border--line {
        margin: 0 auto;
    }
    .footer-social .footer-social__wrapper .footer-social__list{
        margin-bottom: 11px;
    }
}

@media (min-width: 1025px) {
    .footer-social__list {
        position: relative;
        top: 13px;
    }

    .footer-social .footer-social__wrapper .footer-social__text .footer-social__text-title {
        margin-bottom: 0;
    }

    .footer-social .footer-social__wrapper .footer-social__text {
        max-width: 100%;
        margin:auto;
    }
}
