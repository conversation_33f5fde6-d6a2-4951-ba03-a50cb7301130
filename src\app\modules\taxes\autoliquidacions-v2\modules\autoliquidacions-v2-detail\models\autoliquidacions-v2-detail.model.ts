import { LazyLoadEvent } from "primeng/api";
import { ISelfAssessmentV2Filter, CanviEstat } from "../../../models/autoliquidacions-v2.model";
import { iCommonError } from "pt-ui-components-mf-lib";
import { DetailCardData, DetailFieldType } from "src/app/modules/shared/detail-card";
import { TabData, TabType } from "src/app/modules/shared/detail-tabs";

export interface ISelfAssessmentV2Detail {
	idAutoliquidacio: string,
	idTramit: string,
	numJustificant: string,
	impost: string,
	model: string,
	periode: string,
	exercici: string,
	declaracioExempcio: boolean,
	baseImposableTotal: number,
	dataAlta: Date,
	dataModificacio: Date,
	presentador: string,
	subjectePassiu: string,
	errors: iCommonError[],
	tipus: string,
	idioma: string,
	estat: string,
	canviEstat: CanviEstat,
	totalIngressar: number,
  idAgrupacio: string
}

export interface IDetailData {
	filterValue: Partial<ISelfAssessmentV2Filter>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idSelfAssessment?: string
}

export enum DetailCardFields {
  estat = "estat",
  impost = "impost",
  tipus = "tipus",
  nomPresentador = "nomPresentador",
  nifPresentador = "nifPresentador",
  nomSubjectePassiu = "nomSubjectePassiu",
  nifSubjectePassiu = "nifSubjectePassiu",
  model = "model",
  dataAlta = "dataAlta",
  dataModificacio = "dataModificacio",
  idAutoliquidacio = "idAutoliquidacio",
  numJustificant = "numJustificant",
  periode = "periode",
  exercici = "exercici",
  idAgrupacio = "idAgrupacio"
}
export type DetailCardFieldsT = keyof typeof DetailCardFields;

const DETAIL_CARD_TRANSLATIONS = "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.CARD.FIELDS";
export const DETAIL_CARD_FORM_DATA:DetailCardData[] = [
  {
    id: DetailCardFields.idAutoliquidacio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idAutoliquidacio}`
  },
  {
    id: DetailCardFields.numJustificant,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.numJustificant}`
  },
  {
    id: DetailCardFields.estat,
    fieldType: DetailFieldType.state,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.estat}`
  },
  {
    id: DetailCardFields.impost,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.impost}`
  },
  {
    id: DetailCardFields.tipus,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.tipus}`
  },
  {
    id: DetailCardFields.nomPresentador,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomPresentador}`
  },
  {
    id: DetailCardFields.nifPresentador,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nifPresentador}`
  },
  {
    id: DetailCardFields.nomSubjectePassiu,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomSubjectePassiu}`
  },
  {
    id: DetailCardFields.nifSubjectePassiu,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nifSubjectePassiu}`
  },
  {
    id: DetailCardFields.model,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.model}`
  },
  {
    id: DetailCardFields.dataAlta,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataAlta}`
  },
  {
    id: DetailCardFields.dataModificacio,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataModificacio}`
  },
  {
    id: DetailCardFields.periode,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.periode}`
  },
  {
    id: DetailCardFields.exercici,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.exercici}`
  },
  {
    id: DetailCardFields.idAgrupacio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idAgrupacio}`
  }
]

export type DetailTabsModel = {
  [K in TabType]?: string[];
}

export const DETAIL_TABS_MODELS:DetailTabsModel  = {
  [TabType.validations]: ["600","651","652","653"],
  [TabType.grouping]: ["600","651","652","653"],
  [TabType.notifications]: ["600","651","652","653"],
  [TabType.procedures]: ["520", "540", "550", "560", "910", "920","940", "950", "042", "980", "990"],
}

export const TypesDeclaration = {
  TRIBUT: 'TRIBUT',
  DECINF: 'DECINF'
} as const;

const DETAIL_TABS_TRANSLATIONS = "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.TABS"
export const DETAIL_TAB_DATA: TabData[] = [
  {
    tabType: TabType.documents,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
  },
  {
    tabType: TabType.validations,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.VALIDATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.grouping,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.GROUPING`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.payments,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.PAYMENTS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.notifications,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.NOTIFICATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.presentations,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.PRESENTATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.procedures,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TRAMITACIONS`,
    tabRetryButtonClass: "p-button-primary"
  }
]
