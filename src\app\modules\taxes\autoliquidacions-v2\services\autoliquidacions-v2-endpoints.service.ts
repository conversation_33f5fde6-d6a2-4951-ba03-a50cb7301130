import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { SelfAssessmentV2Request } from '../models/autoliquidacions-v2.model';
import { SelfAssessmentV2Response } from '../models/autoliquidacions-v2-endpoints.model';

@Injectable({
	providedIn: 'root'
})
export class AutoliquidacionsV2EndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search autoliquidacions by criteria
	getSelfAssessmentList(criteria?: SelfAssessmentV2Request | object): Observable<SelfAssessmentV2Response> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: "/tracking/v2/autoliquidacio/llistat",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}
}