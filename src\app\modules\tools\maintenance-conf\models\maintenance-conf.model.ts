import { PtTableIconTemplate } from "pt-ui-components-mf-lib";
import { ConfResponseContent } from "./maintenance-conf-endpoint.model";

export enum ConfContextEnum {
	EL_MEU_ESPAI_ATC = "EL_MEU_ESPAI_ATC",
	CITA_PREVIA = "CITA_PREVIA",
	ESTADES_TURISTIQUES = "ESTADES_TURISTIQUES",
	TRANSMISSIONS_PATRIMONIALS = "TRANSMISSIONS_PATRIMONIALS",
	SIGNATURA_INSPECCIO = "SIGNATURA_INSPECCIO",
	APORTAR_DOCUMENTACIO = 'APORTAR_DOCUMENTACIO',
	DEVOLUCIO_INGRESSOS = "DEVOLUCIO_INGRESSOS",
	PAGAMENT_LIQUIDACIONS = "PAGAMENT_LIQUIDACIONS",
	RECURS_REPOSICIO = "RECURS_REPOSICIO",
	RECURS = "RECURS",
	CSV = "CSV"
}
export type ConfContextT = keyof typeof ConfContextEnum;
export type ConfContextValues<ConfContextEnum> = ConfContextEnum[keyof ConfContextEnum];

export enum ConfModalModeEnum {
	FILTER = "FILTER",
	CREATE_CONF = "CREATE",
	UPDATE_CONF = "UPDATE",
	DELETE_CONF = "DELETE"
}
export type ConfModalModeT = keyof typeof ConfModalModeEnum;
export type ConfModalModeValues<ConfModalModeEnum> = ConfModalModeEnum[keyof ConfModalModeEnum];

export enum ConfSateEnum {
	ACTIVE = "ACTIVE",
	IN_MAINTENANCE = "IN_MAINTENANCE",
	SCHEDULED_MAINTENANCE = "SCHEDULED_MAINTENANCE"
}
export type ConfSateEnumT = keyof typeof ConfSateEnum;
export type ConfSateEnumValues<ConfSateEnum> = ConfSateEnum[keyof ConfSateEnum];

export interface ConfFilters {
	aplicacio?: ConfContextEnum;
  mantenimentInici?: number;
	mantenimentFinal?: number;
}

export interface ConfModalInput {
	title: string;
	mode: ConfModalModeEnum;
	maintenance?: ConfRow | ConfFilters;
}

export interface ConfRow extends ConfResponseContent {
  stateRow?: PtTableIconTemplate;
  nouCapcaleraRow?: PtTableIconTemplate;
}