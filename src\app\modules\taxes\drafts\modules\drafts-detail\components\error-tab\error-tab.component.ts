import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { CommonError, PtTableColumn, PtTableColumnTemplate } from 'pt-ui-components-mf-lib';

@Component({
  selector: 'app-error-tab',
  templateUrl: './error-tab.component.html',
  styleUrls: ['./error-tab.component.sass']
})
export class ErrorTabComponent implements OnInit {

  @Input() set rows(value: CommonError[]) {
    this.tableRows = this.mapRows(value);
  }

  // Table
	tableColumns: PtTableColumn[] = [];
	tableRows: CommonError[] = [];

  constructor(private translateService: TranslateService) { }

  ngOnInit(): void {
    this.tableColumns = this.setTableColumns();
  }

  private setTableColumns(): PtTableColumn[] {
    return [
      {
        id: 'trackingId',
        label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.TRACKING',
        isResizable: false,
        isSortable: true,
        width: '15%'
      },
      {
        id: 'technicalCode',
        label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.CODE',
        isResizable: false,
        isSortable: true,
        width: '13%'
      },
      {
        id: 'date',
        label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.DATE',
        isResizable: false,
        isSortable: true,
        template: PtTableColumnTemplate.date,
        options: { dateFormat: 'dd/MM/yyyy', alignLeftColumn: true},
        width: '13%'
      },
      {
        id: 'technicalDescription',
        label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.DESCRIPTION',
        isResizable: false,
        template: PtTableColumnTemplate.innerHTML,
        isSortable: true,
        width: '27%'
      },
      {
        id: 'stackTrace',
        label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.STACKTRACE',
        isResizable: false,
        isSortable: true,
        width: '27%'
      }
    ];
  }

  mapRows = (data: CommonError[]): CommonError[] => {
		let rows: CommonError[] = [];

		if (data?.length > 0) {
			// Get translations
			const errorsTranslations: object = this.translateService.instant(`UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS`);

			rows = data.map((error) => {
				const row: CommonError = {
					code: error.code,
					date: error.date,
					type: error.type,
					description: errorsTranslations[error.code] || error.description || '',
					technicalCode: error.technicalCode || 'Error',
					technicalDescription: errorsTranslations[error.technicalCode] || error.technicalDescription || '',
					trackingId: error.trackingId || '',
					stackTrace: error.stackTrace || ''
				};
				return row;
			});
		}

		return rows;
	}
}
