$(document).ready(function(){
  $('.linkSubMenu').on("click", function(e){
    if($(this).next('.subMenu').css("display")=="block"){
      $(this).removeClass();
      $(this).find('i').removeClass().addClass('fa fa-angle-down');
    }
    else{
      $(this).addClass('blueBlock');
      $(this).find('i').removeClass().addClass('fa fa-angle-up');
    }
    $(this).next('.subMenu').toggle();
    e.stopPropagation();
    e.preventDefault();
  });

  $('.linkThreeMenu i').on("click", function(e){
    if($(this).parent('.linkThreeMenu').next('.threeMenu').css("display")=="block"){
      $(this).parent('.linkThreeMenu').removeClass('blueBlock');
      $(this).removeClass().addClass('fa fa-angle-down');
    }
    else{
      $(this).parent('.linkThreeMenu').addClass('blueBlock');
      $(this).removeClass().addClass('fa fa-angle-up');
    }
    $(this).parent('.linkThreeMenu').next('.threeMenu').toggle();
    e.stopPropagation();
    e.preventDefault();
  });

  /*$('.closeSubMenu').on("click", function(e){
    $(this).parents('.subMenu').toggle();
    e.stopPropagation();
    e.preventDefault();
  });*/
  $('.blockDespleInfo .hiperDesple').click(function(event) {
    if($(this).parent("h3").next(".contentInfo").css("display")=="block"){
      $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
    }
    else{
      $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
    }
    $(this).parent("h3").next(".contentInfo").slideToggle(400);
    return false;
  });

  $('.fpca_preguntesFrequents .hiperDesple').click(function(event) {
    if($(this).parents(".panel-heading").next(".panel-collapse").css("display")=="block"){
      $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
    }
    else{
      $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
    }
    $(this).parents(".panel-heading").next(".panel-collapse").slideToggle(400);
    return false;
  });
  
  //Funcionalitat twitter
	$(".eventLinkedIn").each(function(){
		try{
			$(this).bind('click', function(){
				width=575;
				height=400;
				left=($(window).width()-width)/2;
				top=($(window).height()-height)/2;
				url="https://www.linkedin.com/shareArticle?mini=true&url="+document.URL + "&title=" +encodeURI(document.title);
				opts="status=1"+",width="+width+",height="+height+",top="+top+",left="+left;
				window.open(url,"LinkedIn",opts);	
			});

		}
		catch(e)
		{
			if(console)
				console.log("Problema carga LinkedIn: "+e);
		}
	});

  if ($(window).width() <= 767){
    $('.buttonMenu').on("click", function(e){
      if($(this).parents('.headerTop').find('.navigationMenuMobile').css("display")=="block"){
        $(this).css('background' , '#666460');
      }
      else{
        $(this).css('background' , '#0092dc');
      }
      $(this).parents('.headerTop').find('.navigationMenuMobile').toggle();
      e.stopPropagation();
      e.preventDefault();
    });

    $('.subMenuFooter').on("click", function(e){
      if($('.blockCenter ').css("display")=="block"){
        $(this).find('i').removeClass().addClass('fa fa-angle-down');
      }
      else{
        $(this).find('i').removeClass().addClass('fa fa-angle-up');
      }
      $('.blockCenter ').toggle();
      e.stopPropagation();
      e.preventDefault();
    });
    $('.clickSubMenu i').on("click", function(e){
      if($(this).parent(".clickSubMenu").next('ul').css("display")=="block"){
        $(this).removeClass().addClass('fa fa-angle-down');
      }
      else{
        $(this).removeClass().addClass('fa fa-angle-up');
      }
      $(this).parent(".clickSubMenu").next('ul').toggle();
      e.stopPropagation();
      e.preventDefault();
    });




    $('.buttonSearchMobil').on("click", function(e){
      if($('.searchMobil').css('display')=="block"){
        $(this).css('background', '#666460');
      }
      else{
        $(this).css('background', 'rgb(0, 146, 220)');
      }
      $('.searchMobil').toggle();
      e.stopPropagation();
      e.preventDefault();
    });

    $('.titleToogle').click(function(event) {
      if($(this).next("ul").css("display")=="block"){
        $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
        $(this).css("border-bottom" , "1px solid #ddd");
      }
      else{
        $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
        $(this).css("border-bottom" , "none");
      }
      $(this).next('ul').slideToggle(400);
      return false;
    });

    $('.titleSection').click(function(event) {
      if($(this).next("div").css("display")=="block"){
        $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
      }
      else{
        $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
      }
      $(this).next('div').slideToggle(400);
      return false;
    });

    $('.hiperTabs').click(function(event) {
      if($(this).next("div").css("display")=="block"){
        $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
        $(this).removeClass('active');
      }
      else{
        $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
        $(this).addClass('active');
      }
      $(this).next('div').slideToggle(400);
      return false;
    });
    $('.hiperHomeMobil').click(function(event) {
      if($(this).next("div").css("display")=="block"){
        $(this).find("i").removeClass('fa-angle-up').addClass('fa-angle-down');
        $(this).addClass('borderRadiusOnly');
      }
      else{
        $(this).find("i").removeClass('fa-angle-down').addClass('fa-angle-up');
        $(this).removeClass('borderRadiusOnly');
      }
      $(this).next('div').slideToggle(400);
      return false;
    });

  }

    var movDere=0;
  $(".scrollMoveRight").click(function(event) {
    var lastLi=$(this).parent('.navScroll').width() - $(this).prev('.nav-tabs').width();
    var leftUl= parseInt($(this).prev('.nav-tabs').css('left'));
    $(".scrollMoveLeft").css('display', 'table');
    if(leftUl<=0 &&  leftUl > (lastLi + 100)){
      $(this).prev('.nav-tabs').animate( {
       'left': '-=100'
     }, 500);
    }
    else {
      $(this).prev('.nav-tabs').animate( {
       'left': lastLi
     }, 500);
      $(this).css('display', 'none');
    }
    return false;
  });


  var movRight=0;
  $(".scrollMoveLeft").click(function(event) {
    var leftUl= parseInt($(this).next('.nav-tabs').css('left'));
    $(".scrollMoveRight").css('display', 'table');

    if(leftUl < -99){
      $(this).next('.nav-tabs').animate( {
       'left': '+=100'
     }, 500);
    }
    else {
      $(this).next('.nav-tabs').animate( {
       'left': 0
     }, 500);
    }
    if(leftUl >= -100){
      $(this).css('display', 'none');
    }
    return false;
  });

  $(".breadcrumbsModalitat ul li a").click(function(event) {
    $('.breadcrumbsModalitat').find('h2').html();
    $('.breadcrumbsModalitat').find('h2').html($(this).text());
    console.log($('.breadcrumbsModalitat').parents('ul').find('h2'));
  });
  


});


$( window ).load(function() {
  // Run code

  var contador=1;
  var prox=1 ;
  var height=0;
  $(".blockListSingle").each(function(index, el) {
    if(contador<=3){
      $(this).addClass('blockSingle'+ prox + '');
      contador++;
      height2=parseInt($(this).css("height")) ;
      if(height <  height2){
        height=parseInt($(this).css("height")) ;
      }
      $(".blockSingle"+ prox).css("height" , height + "px");
    }
    else{
      contador=2;
      prox++;
      height=parseInt($(this).css("height") ) ;
      $(this).addClass('blockSingle'+ prox);
      $(".blockSingle"+ prox).css("height" , height + "px");
    }
  });
  var cont=1;
  var next=1 ;
  var heightNews=0;
  $(".listBlockNews li").each(function(index, el) {
    if(cont<=4){
      $(this).addClass('newsList'+ next + '');
      cont++;
      heightNews2=parseInt($(this).css("height")) ;
      if(heightNews <  heightNews2){
        heightNews=parseInt($(this).css("height")) ;
      }
      $(".newsList"+ next).css("height" , heightNews + "px");
    }
    else{
      cont=2;
      next++;
      heightNews=parseInt($(this).css("height") ) ;
      $(this).addClass('newsList'+ next);
      $(".newsList"+ next).css("height" , heightNews + "px");
    }
  });

  if($(window).width() > 767){
      var widthUl=0;
      var widthLi=0;
      $(".navScroll ul li").each(function() {
        widthLi=$(this).outerWidth() * 1.01;
        widthUl=widthUl+Math.ceil(widthLi);

      });
	  $(".navScroll ul").width(widthUl);
    }

function scrollInit(widthUl){
  var containerWidth = $(".navScroll ul").parent().width();
  if ( widthUl < containerWidth || $(window).width() < 767){
    $(".scrollMoveRight,.scrollMoveLeft" ).css('display', 'none');
  }
};
scrollInit(widthUl);

});

$(window).resize(function(event) {
  if($(window).width() > 767)
    {
      if($(".navScroll ul").width()<=$(".navScroll").width()){
        $(".navScroll").children('a').css('display', 'none');
      }
      else{
        $(".navScroll").children('.scrollMoveRight').css('display', 'table');
      }
    } else if($(window).width() < 767){
      $(".scrollMoveRight,.scrollMoveLeft" ).css('display', 'none');
    }

});


/********************* Normailize Menu ***************************/
$(window).ready(function(){
var navWidth = {
  titleSize: function (list){
    var txt = 0;

      txt = $(list).children('a').text().trim();
      // console.log(txt + ' ' + txt.length);
      return txt.length;


  },
  getWeight: function (list){
    var title = navWidth.titleSize(list);
    if ($(list).hasClass("hidden-xs")) {
      // console.log(this);
      $(list).attr('weight', '.8');
    } else if (title > 12){
      $(list).attr('weight', '.6');
    }else if (title <= 12){
      $(list).attr('weight', '.4');
    }
  },
  weights: 0,
  getWeights: function (list){
      var weight = $(list).attr('weight');
      navWidth.weights += parseFloat(weight);
      return navWidth.weights;
      // console.log("weights ="+navWidth.weights);
  },
  width: 0,
  setPercents: function setPercents(list){
      var weight = $(list).attr('weight');
      width = (((weight/ navWidth.weights) *100).toFixed(2) );
      // console.log(weight +' & '+ width);
      $(list).css("width", width + '%');
      // console.log((width * 100).toFixed(2));
  }

}

var menuNav = $('#navigation > .container > ul > li');

function normailizeMenuNav (list, callback){
  $(list).each(function(index, el) {
    navWidth.getWeight(this);
    navWidth.getWeights(this);
  });

  $(list).each(function(index, el) {
    navWidth.setPercents(this);
  });
  
  callback(list);
}

/***************************** Match Height ********************************/

  function matchHeight(element){
    // apply maxHeight
    // Get an array of all element heights
    var elementHeights = $(element).map(function() {
      return $(this).innerHeight();
    }).get();

    // Math.max takes a variable number of arguments
    // `apply` is equivalent to passing each height as an argument
    var maxHeight = Math.max.apply(null, elementHeights);

    // Set each height to the max height
    $(element).css('height',maxHeight);
    // console.log(maxHeight);
  }

  /*CALL NORMALIZE MENU W/ matchHeight*/
  normailizeMenuNav(menuNav, matchHeight);

  /*Info relacionada*/
  if ($(window).width() > 767){
    matchHeight('.infoContact > ul > li');
  }

});
