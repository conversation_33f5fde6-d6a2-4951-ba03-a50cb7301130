import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalWarningMsgComponent } from './modal-warning-msg.component';

describe('ModalWarningMsgComponent', () => {
  let component: ModalWarningMsgComponent;
  let fixture: ComponentFixture<ModalWarningMsgComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalWarningMsgComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalWarningMsgComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
