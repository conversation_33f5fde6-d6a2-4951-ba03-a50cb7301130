import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PagamentsComponent } from './pagaments.component';
import { LazyElementsModule } from '@angular-extensions/elements';
import { Routes, RouterModule } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: '',
		component: PagamentsComponent,
		canActivate: [AuthGuardService],
		data: {
			title: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_PAGAMENTS.PAGE_TITLE',
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
			isElementVisible: false
		},
	}
];

@NgModule({
  declarations: [PagamentsComponent],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	]
})

export class PagamentsModule { }
