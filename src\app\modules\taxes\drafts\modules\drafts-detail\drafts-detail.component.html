<app-detail-page
	[pageTitle]="'MODULE_TRACKING.MODULE_DRAFTS.DETAIL.PAGE_TITLE'"
	[statesTableTranslation]="'MODULE_TRACKING.MODULE_DRAFTS.STATES'"
	[deleteButtonLabel]="'MODULE_TRACKING.MODULE_DRAFTS.DETAIL.BUTTONS.DELETE_DRAFT'"
	[detailCardTitle]="'MODULE_TRACKING.MODULE_DRAFTS.DETAIL.CARD.TITLE'"

	[procedureData]="draft"
	[idEntity]="idDraft"
	[idTramit]="idDraft"
	[idReceipt]="idReceipt"

	[stateChangeFirstDate]="draft?.dataAlta"
	[stateChanges]="stateChanges"
	[stateIcon]="stateIcon"
	[stateLabel]="stateLabel"
	[detailCardFields]="detailCardFields"
	[detailTabInput]="detailTabInput"
	[customTabsContent]="{
		errorTab:errorTab,
		selfAssessmentTab:selfAssessmentTab
	}"
	[currentIndex]="currentIndex"
	[totalRows]="totalRows"
	[backUrl]="navigateBackUrl"

	[showDeleteButton]="showDeleteButton"
	[deleteAllowRoles]="deleteCopyAllowRoles"
	[customButtonsTemplate]="copyDraftButton"

	(getProcedureList)="getSelfAssessmentList()"
	(getProcedure)="getSelfAssessment()"
	(showErrors)="onShowErrors()"
	(showDeleteModal)="deleteModal()"

	(incrementIndex)="currentIndex = currentIndex + 1"
	(decrementIndex)="currentIndex = currentIndex - 1"
>
</app-detail-page>

<ng-template #errorTab>
	<app-error-tab [rows]="draft.warnings || []"></app-error-tab>
</ng-template>

<ng-template #selfAssessmentTab>
	<app-self-assessment-tab [idDraft]="draft.id"></app-self-assessment-tab>
</ng-template>

<ng-template #copyDraftButton>
	<pt-button
		[allowRoles]="deleteCopyAllowRoles"
		[_label]="'MODULE_TRACKING.MODULE_DRAFTS.DETAIL.BUTTONS.COPY_DRAFT'"
		_icon="sli2-docs"
		_type="button"
		_class="p-button-primary"
		(_clickFn)="copyDraft()"
	>
	</pt-button>
</ng-template>
