import { Component, OnDestroy, OnInit, Input } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem } from 'primeng/api';
import { PtAuthService, PtUser, UserRoles } from 'pt-ui-components-mf-lib';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
	AppRoutes,
	DraftsRoutes,
	ManagementRoutes,
	ManagementsMonitorRoutes,
	MySpaceRoutes,
	SelfAssessmentV1Routes,
	TaxesRoutes,
	ToolsRoutes,
	UserRoutes,
} from '../models/config.model';

@Component({
	selector: 'app-navbar',
	templateUrl: './navbar.component.html',
	styleUrls: ['./navbar.component.sass'],
})
export class NavbarComponent implements OnInit, OnDestroy {
	data: PtUser;
	menuItems: MenuItem[] = [];
	rol: string;
	environment: string;

	private unsubscribe: Subject<void> = new Subject();

	constructor(
		private auth: PtAuthService,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		this.data = this.auth.getSessionStorageUser();
		this.environment = this.data.environment;
		
		this.setupRolTranslation();

		this.setUpMenu();
	}

	ngOnDestroy(): void {
		this.unsubscribe.next();
		this.unsubscribe.complete();
	}

	setupRolTranslation() {
		this.translateService.get('MODULE_TRACKING.ROLES').subscribe((res) => {
			this.rol = res[this.data.rol];
		});
	}

	setUpMenu() {
		this.translateService
			.get('MODULE_TRACKING.NAVBAR')
			.pipe(takeUntil(this.unsubscribe))
			.subscribe((res) => {
				this.menuItems = [
					{
						label: res.TAXES,
						icon: 'sli2-bars',
						items: [
							{
								label: res.SELF_ASSESSMENT,
								routerLink: [
									'/' +
										AppRoutes.TAXES.concat(
											'/',
											TaxesRoutes.SELF_ASSESSMENT_V1,
											'/',
											SelfAssessmentV1Routes.TABLE
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN ||
									this.data.rol === UserRoles.READ_ONLY,
							},
							{
								label: res.SELF_ASSESSMENT_V2,
								routerLink: [
									'/' +
										AppRoutes.TAXES.concat(
											'/',
											TaxesRoutes.SELF_ASSESSMENT_V2,
											'/',
											SelfAssessmentV1Routes.TABLE
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN ||
									this.data.rol === UserRoles.READ_ONLY,
							},
							{
								label: res.DRAFTS,
								routerLink: [
									'/' +
										AppRoutes.TAXES.concat(
											'/',
											TaxesRoutes.DRAFTS,
											'/',
											DraftsRoutes.TABLE
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN ||
									this.data.rol === UserRoles.READ_ONLY,
							},
						],
						visible:
							this.data.rol === UserRoles.ADMIN ||
							this.data.rol === UserRoles.TECHNICIAN ||
							this.data.rol === UserRoles.READ_ONLY,
					},
					{
						label: res.MANAGEMENT,
						icon: 'sli2-bars',
						items: [
							{
								label: res.TEARC_MONITOR,
								routerLink: ['/' + AppRoutes.RESOLUCIONS],
								routerLinkActiveOptions: { exact: true },
								visible:
									(this.data.rol === UserRoles.ADMIN ||
										this.data.rol ===
											UserRoles.TECHNICIAN) &&
									this.data.environment !== 'pro',
							},
							{
								label: res.PAYMENT_SETTLEMENT,
								routerLink: ['/' + AppRoutes.LIQUIDACIONS],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.MANAGEMENTS_MONITOR,
								routerLink: [
									'/' +
										AppRoutes.MANAGEMENTS.concat(
											'/',
											ManagementRoutes.MONITOR,
											'/',
											ManagementsMonitorRoutes.TABLE
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.PAGAMENTS,
								routerLink: ['/' + AppRoutes.PAGAMENTS
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
						],
						visible:
							this.data.rol === UserRoles.ADMIN ||
							this.data.rol === UserRoles.TECHNICIAN,
					},
					{
						label: res.USERS,
						icon: 'sli2-bars',
						items: [
							{
								label: res.USER,
								routerLink: ['/' + AppRoutes.USERS_TABLE],
								routerLinkActiveOptions: { exact: true },
								visible: this.data.rol === UserRoles.ADMIN,
							},
							{
								label: res.SIMULATED_LOGIN,
								routerLink: [
									'/' +
										AppRoutes.USERS.concat(
											'/',
											UserRoutes.SIMULATED_LOGIN
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN ||
									this.data.rol === UserRoles.INSPECTOR,
							},
						],
						visible:
							this.data.rol === UserRoles.ADMIN ||
							this.data.rol === UserRoles.TECHNICIAN ||
							this.data.rol === UserRoles.INSPECTOR,
					},
					{
						label: res.AREA_PRIVADA,
						icon: 'sli2-bars',
						items: [
							{
								label: res.AUTOLIQUIDACIONS,
								routerLink: [
									'/' +
									AppRoutes.MY_SPACE_CONF.concat(
										'/',
										MySpaceRoutes.SELF_ASSESSMENT
									)
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.GESTIONS,
								routerLink: [
									'/' +
									AppRoutes.MY_SPACE_CONF.concat(
										'/',
										MySpaceRoutes.GESTIONS
									)
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.PAGAMENTS,
								routerLink: [
									'/' +
									AppRoutes.MY_SPACE_CONF.concat(
										'/',
										MySpaceRoutes.PAGAMENTS
									)
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
						],
						visible:
							this.data.rol === UserRoles.ADMIN ||
							this.data.rol === UserRoles.TECHNICIAN,
					},
					{
						label: res.TOOLS,
						icon: 'sli2-bars',
						items: [
							{
								label: res.SARCAT,
								routerLink: [
									'/' + AppRoutes.SARCAT_MIDDLEWARE
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.NOTIFICATIONS,
								routerLink: [
									'/' +
										AppRoutes.TOOLS.concat(
											'/',
											ToolsRoutes.NOTICES_CONF
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.MAINTENANCE_CONF_SEU,
								url: this.getConfigSeuUrl(),
								target: '_blank',
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.MAINTENANCE_CONF_SEU2,
								routerLink: [
									'/' +
										AppRoutes.TOOLS.concat(
											'/',
											ToolsRoutes.MAINTENANCE_CONF
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.NIF_ENCRYPTION,
								routerLink: [
									'/' +
										AppRoutes.TOOLS.concat(
											'/',
											ToolsRoutes.NIF_ENCRYPTION
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.TECHNICIAN,
							},
							{
								label: res.EXPEDIENTES,
								routerLink: [
									'/' +
										AppRoutes.TOOLS.concat(
											'/',
											ToolsRoutes.EXPEDIENTES
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN ||
									this.data.rol === UserRoles.INSPECTOR,
							},
							{
								label: res.BIOMETRIC_SIGNATURE,
								routerLink: [
									'/' +
										AppRoutes.TOOLS.concat(
											'/',
											ToolsRoutes.BIOMETRIC_SIGNATURE
										),
								],
								routerLinkActiveOptions: { exact: true },
								visible:
									this.data.rol === UserRoles.ADMIN
							},
						],
						visible:
							this.data.rol === UserRoles.ADMIN ||
							this.data.rol === UserRoles.TECHNICIAN ||
							this.data.rol === UserRoles.READ_ONLY ||
							this.data.rol === UserRoles.INSPECTOR ||
							this.data.rol === UserRoles.SUPER_ADMIN,
					},
				];
			});
	}

	private getConfigSeuUrl(): string {
		if(!this.environment) return '';

		if(this.environment === 'dev') return 'https://integracio.seu2.atc.intranet.gencat.cat/validador/dashboard/';
		if(this.environment === 'int') return 'https://integracio.seu2.atc.intranet.gencat.cat/validador/dashboard/';
		if(this.environment === 'pre') return 'https://preproduccio.seu2.atc.intranet.gencat.cat/validador/dashboard/';
		if(this.environment === 'pro') return 'https://seu2.atc.gencat.cat/validador/dashboard/';
	}
}
