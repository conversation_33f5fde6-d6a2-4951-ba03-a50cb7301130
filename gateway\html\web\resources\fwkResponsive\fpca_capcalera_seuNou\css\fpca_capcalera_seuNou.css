.NG-header {
    /* Rtl support */ }
.NG-header [data-simplebar] {
    position: relative;
    flex-direction: column;
    flex-wrap: wrap;
    justify-content: flex-start;
    align-content: flex-start;
    align-items: flex-start; }
.NG-header .simplebar-wrapper {
    overflow: hidden;
    width: inherit;
    height: inherit;
    max-width: inherit;
    max-height: inherit; }
.NG-header .simplebar-mask {
    direction: inherit;
    position: absolute;
    overflow: hidden;
    padding: 0;
    margin: 0;
    left: 0;
    top: 0;
    bottom: 0;
    right: 0;
    width: auto !important;
    height: auto !important;
    z-index: auto !important; }
.NG-header .simplebar-offset {
    direction: inherit !important;
    box-sizing: inherit !important;
    resize: none !important;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0 !important;
    padding: 0;
    margin: 0;
    -webkit-overflow-scrolling: touch; }
.NG-header .simplebar-content-wrapper {
    direction: inherit;
    box-sizing: border-box !important;
    position: relative;
    display: block;
    height: 100%;
    /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */
    width: auto;
    max-width: 100%;
    /* Not required for horizontal scroll to trigger */
    max-height: 100%;
    /* Needed for vertical scroll to trigger */
    scrollbar-width: none;
    -ms-overflow-style: none; }
.NG-header .simplebar-content-wrapper::-webkit-scrollbar,
.NG-header .simplebar-hide-scrollbar::-webkit-scrollbar {
    width: 0;
    height: 0; }
.NG-header .simplebar-content:before,
.NG-header .simplebar-content:after {
    content: ' ';
    display: table; }
.NG-header .simplebar-placeholder {
    max-height: 100%;
    max-width: 100%;
    width: 100%;
    pointer-events: none; }
.NG-header .simplebar-height-auto-observer-wrapper {
    box-sizing: inherit !important;
    height: 100%;
    width: 100%;
    max-width: 1px;
    position: relative;
    float: left;
    max-height: 1px;
    overflow: hidden;
    z-index: -1;
    padding: 0;
    margin: 0;
    pointer-events: none;
    flex-grow: inherit;
    flex-shrink: 0;
    flex-basis: 0; }
.NG-header .simplebar-height-auto-observer {
    box-sizing: inherit;
    display: block;
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    height: 1000%;
    width: 1000%;
    min-height: 1px;
    min-width: 1px;
    overflow: hidden;
    pointer-events: none;
    z-index: -1; }
.NG-header .simplebar-track {
    z-index: 1;
    position: absolute;
    right: 0;
    bottom: 0;
    pointer-events: none;
    overflow: hidden; }
.NG-header [data-simplebar].simplebar-dragging .simplebar-content {
    pointer-events: none;
    user-select: none;
    -webkit-user-select: none; }
.NG-header [data-simplebar].simplebar-dragging .simplebar-track {
    pointer-events: all; }
.NG-header .simplebar-scrollbar {
    position: absolute;
    left: 0;
    right: 0;
    min-height: 10px; }
.NG-header .simplebar-scrollbar:before {
    position: absolute;
    content: '';
    background: black;
    border-radius: 7px;
    left: 2px;
    right: 2px;
    opacity: 0;
    transition: opacity 0.2s linear; }
.NG-header .simplebar-scrollbar.simplebar-visible:before {
    /* When hovered, remove all transitions from drag handle */
    opacity: 0.5;
    transition: opacity 0s linear; }
.NG-header .simplebar-track.simplebar-vertical {
    top: 0;
    width: 11px; }
.NG-header .simplebar-track.simplebar-vertical .simplebar-scrollbar:before {
    top: 2px;
    bottom: 2px; }
.NG-header .simplebar-track.simplebar-horizontal {
    left: 0;
    height: 11px; }
.NG-header .simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {
    height: 100%;
    left: 2px;
    right: 2px; }
.NG-header .simplebar-track.simplebar-horizontal .simplebar-scrollbar {
    right: auto;
    left: 0;
    top: 2px;
    height: 7px;
    min-height: 0;
    min-width: 10px;
    width: auto; }
.NG-header [data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {
    right: auto;
    left: 0; }
.NG-header .hs-dummy-scrollbar-size {
    direction: rtl;
    position: fixed;
    opacity: 0;
    visibility: hidden;
    height: 500px;
    width: 500px;
    overflow-y: hidden;
    overflow-x: scroll; }
.NG-header .simplebar-hide-scrollbar {
    position: fixed;
    left: 0;
    visibility: hidden;
    overflow-y: scroll;
    scrollbar-width: none;
    -ms-overflow-style: none; }

/******** RESET ********/
ul {
    list-style: none; }

/******** GRID ********/
.NG-container {
    display: flex;
    flex-wrap: wrap; }

.NG-container {
    max-width: 1100px;
    margin: 0 auto;
    position: relative; }

.NG-row-flex {
    display: flex;
    width: 100%;
    flex: 1; }

.NG-row-flex--vertical {
    flex-direction: column;
    width: auto; }

.NG-row-flex--space-between {
    justify-content: space-between; }

.NG-row-flex--full-height {
    height: 100%;
    flex: 1; }

.NG-row-flex--align-center {
    align-items: center;
    justify-content: center; }

.NG-col--fullwidth {
    width: 100%;
    flex: 1; }

.NG-col {
    position: relative;
    display: flex;
    align-self: stretch; }

.NG-bottom-stick {
    margin-top: auto; }

.NG--pd15 {
    padding-left: 15px;
    padding-right: 15px; }

.NG--flex45 {
    flex: .45; }

.NG--flex66 {
    flex: .66; }

.NG--flex75 {
    flex: .75; }

.NG-row-flex-row-reverse {
    flex-flow: column-reverse;
    width: 100%; }

/*** ANIMATION ***/
@keyframes closeCollapse {
    0% {
        max-height: 100vh; }
    20% {
        max-height: 80vh; }
    40% {
        max-height: 60vh; }
    60% {
        max-height: 40vh; }
    80% {
        max-height: 20vh; }
    100% {
        max-height: 0vh; } }

@keyframes openCollapse {
    0% {
        max-height: 0vh; }
    20% {
        max-height: 20vh; }
    40% {
        max-height: 40vh; }
    60% {
        max-height: 60vh; }
    80% {
        max-height: 80vh; }
    100% {
        max-height: 100vh; } }
/*Se cambia el nombre de la variable fadeIn a fadeInSeu por incompatibilidades*/
@keyframes fadeInSeu {
    0% {
        opacity: 0; }
    20% {
        opacity: 0.1; }
    40% {
        opacity: 0.2; }
    60% {
        opacity: 0.3; }
    80% {
        opacity: 0.4; }
    100% {
        opacity: 0.5; } }

/* Desktop*/
@media (min-width: 1025px) {
    .NG-container {
        padding-left: 0;
        padding-right: 0;
        max-width: 1170px;
        margin-right: auto;
        margin-left: auto;
        display: block; }
    .NG-row-flex--vertical-lg {
        flex-direction: column;
        width: auto; }
    .NG-row-flex-row-reverse-lg {
        flex-flow: column-reverse;
        width: 100%; }
    .NG-col--fullwidth-lg {
        width: 100%;
        flex: 1; } }

/* Tablet*/
@media (max-width: 1024px) {
    .NG-row-flex-row-reverse-md {
        flex-flow: column-reverse;
        width: 100%; }
    .NG-row-flex--vertical-md {
        flex-direction: column; }
    .NG-row-flex--align-center-md {
        align-items: center;
        justify-content: center; }
    .NG-col--fullwidth-md {
        width: 100%;
        flex: 1; } }

/* Mobile*/
@media (max-width: 767px) {
    .NG-header__image {
        display: inline-block;
        max-width: 90px;
        position: relative; }
    .NG-row-flex-row-reverse-xs {
        flex-flow: column-reverse;
        width: 100%; }
    .NG-row-flex--vertical-xs {
        flex-direction: column; }
    .NG-row-flex--vertical-reverse-xs {
        flex-direction: column-reverse; }
    .NG-row-flex--align-center-xs {
        align-items: center;
        justify-content: center; }
    .NG-col--fullwidth-xs {
        width: 100%; }
    .NG--flex75 {
        flex: none; } }

/******** GRID END ********/
/*utilities*/
.NG-list--horizontal {
    display: flex; }

@media (max-width: 1024px) {
    .NG-show-lg {
        display: none; } }

.NG-body-lock {
    overflow-y: hidden;
    overflow-x: hidden;
    max-height: 100vh; }

/***** Menu navbar menu base *****/
.NG-navbar__menu {
    margin: 0;
    padding: 0;
    list-style-type: none;
    background-color: #F5F5F5; }

.NG-navbar__list {
    list-style-type: none;
    display: flex;
    justify-content: center;
    align-items: center; }

.NG-navbar__link {
    display: block;
    width: 100%;
    text-decoration: none;
    color: #3a3a3a;
    padding: 12px 5px 12px 33px;
    border-bottom: 1px solid #ddd;
    font-family: "OpenSans_Regular"; }

/* tablet*/
@media (max-width: 1024px) {
    .NG-navbar__list--vertical-sm {
        flex-direction: column; } }

/* desktop*/
@media (min-width: 1025px) {
    .NG-navbar__menu {
        display: flex;
        background-color: white;
        content: ""; }
    .NG-navbar__menu {
        position: relative;
        background-color: transparent; }
    .NG-navbar__menu .NG-navbar__link {
        font-family: "OpenSans_Semibold";
        padding-left: 50px;
        padding-right: 50px;
        font-size:16px;}
    .NG-navbar__link {
        border-bottom: 0; } }

/***** Menu nav base END *****/
/*Commons*/
input:focus::-webkit-input-placeholder {
    color: transparent !important; }

input:focus::-moz-placeholder {
    color: transparent !important; }

input:focus:-moz-placeholder {
    color: transparent !important; }

.NG-header a:focus, .NG-header button:focus, .NG-header input:focus {
    outline: thin dotted;
    outline-offset: -2px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(168, 27, 141, 0.6); }
.NG-header__wrapper .NG-buttonsSearch .NG-buttonsSearch__Title:focus{
box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(21, 117, 174, 0.6);
}

.NG-header .NG-header__icon:focus, .NG-header .NG-header__icon2:focus,.NG-header .NG-inputSearch__button:focus{
color:#F5F5F5;
box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(21, 117, 174, 0.6);
}
.header_fixed {
    position: fixed !important;
    width: 100%;
    background-color: #333; }

.header_hidden_animation {
    animation-name: fade_capcalera_down;
    animation-duration: 1.2s;
    animation-timing-function: ease-in; }

.header_fixed_animation {
    animation-name: fade_capcalera;
    animation-duration: 1.2s;
    animation-timing-function: ease-out; }

.transition_header {
    -webkit-transition: margin-bottom 0.1s ease;
    -moz-transition: margin-bottom 0.1s ease;
    transition: margin-bottom 0.165s ease; }

.NG-menu__wrapper_title2 .NG__redsocial_item .NG__redsocial__item_link:hover {
    webkit-transform: scale(1.2);
    transform: scale(1.2, 1.2);
    -ms-transform: scale(1.2, 1.2);
    -webkit-transform: scale(1.2, 1.2);
    filter: drop-shadow(0px 5px 5px rgba(0, 0, 0, 0.2)) drop-shadow(0px 8px 10px rgba(0, 0, 0, 0.14)); }

.NG-navbar__list--white {
    background-color: white;
    align-items: stretch; }

@media (min-width: 1199px) {
    .NG-navbar__list--white .NG-navbar__link {
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        /*overflow: hidden;*/
        white-space: initial;
        height: 65px;
        text-align: center; } }

.NG-header__section_title {
    color: white;
    font-size: 17px;
    margin-top: 12px; }

.NG-header__section_title_separator {
    margin-left: 22px;
    margin-right: 22px; }

.NG-header-logo {
    background-color: #f8f8f8;
}

.NG-header-logo .NG-container {
    padding-left: 15px;
    padding-right: 15px;
}


.NG-header-logo-wrapper{
    background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/group-14.svg");
    background-repeat: no-repeat;
    background-position: right;
    padding: 15px 0;
    width: 100%;
    background-size: cover;
}
@media (max-width: 1024px) {
    .NG-header-logo-wrapper{
        background-position: 35%;
    }
    .NG-header-logo .NG-container{
        padding-right:0;
    }
}

.NG-header-logo-wrapper-image {
    position: relative;
    display: inline-block;
}

.NG-header-logo-wrapper-image:after {
    content: '';
    height: 33px;
    background-color: #a81b8d;
    position: absolute;
    bottom: 0;
    width: 1px;
    margin-left: 12px;
    margin-right: 12px;
}

.NG-header-logo-wrapper-image h1 {
    position: absolute;
    font-size: 16px;
    top: 65%;
    left: 100%;
    display: block;
    width: auto;
    min-width: 300px;
    text-align: left;
    margin-left: 25px;
}

@media (max-width: 576px) {
    .NG-header-logo-wrapper {
        background-image: none;
    }
    .NG-header-logo-wrapper{
        padding: 11.5px 0;
    }
    .NG-header-logo {
        background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/group-14.svg");
        background-repeat: no-repeat;
        background-position: 40%;
        padding:0;
        width: 100%;
        background-color: transparent;
    }

    .NG-header-logo-wrapper-image img {
        width: 104px;
        height: 37px;
    }

    .NG-header-logo-wrapper-image h1 {
        font-size: 14px;
    }

    .NG-header-logo-wrapper-image:after {
        height: 16px;
    }
}

@keyframes fade_capcalera {
    from {
        top: -100px;
        max-height: 0;
        opacity: 0; }
    to {
        top: 0;
        max-height: 300px;
        opacity: 1; } }

@keyframes fade_capcalera_down {
    from {
        top: 0;
        max-height: 300px;
        opacity: 1; }
    to {
        top: -100px;
        max-height: 0;
        opacity: 0; } }

.NG-header {
    position: relative;
    width: 100%;
    background-color: #333;
    z-index: 999; }

.secondary-header {
    display: none;
    background-color: #333;
    width: 100%; }
.secondary-header .NG-row-flex-row-reverse {
    min-height: 48px; }

.secondary-header .NG-header__section_title a,
.secondary-header .NG-header__section_title a:hover {
    color: white; }

.NG-header__image {
    display: inline-block;
    max-width: 103px;
    position: relative; }

.NG-header__logo {
    margin: auto;
    position: relative; }
.NG-header__logo a:focus {
    outline: thin dotted;
    outline-offset: -2px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(192, 0, 0, 0.6); }

.NG-header__icon {
    display: block;
    position: relative;
    top: 8px;
    left: 0; }

.NG-header__icon {
    background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/menu-w.svg");
    background-repeat: no-repeat;
    width: 24px;
    height: 24px; }

.NG-header__icon2 {
    background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/search-w.svg");
    background-repeat: no-repeat;
    width: 24px;
    height: 24px;
    cursor: pointer; }

.NG-header__icon-close {
    background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/close-w.svg");
    background-repeat: no-repeat;
    width: 24px;
    height: 24px; }

.NG-header__icon2 {
    display: block;
    position: relative;
    top: 8px;
    right: 0; }

.NG-main {
    background-color: #333; }

.NG-navbar__list--border {
    border-top: solid 1px #999999; }

.NG-navbar__image {
    margin-right: 12px; }

.NG-navbar__list--white {
    background-color: white;
    align-items: stretch; }
.NG-navbar__list--white .NG-navbar__link {
    display: flex;
    align-items: center; }

.NG-navbar__image--float-right {
    float: right;
    clear: both; }

.NG-navbar__link--noborder {
    border-bottom: 0; }

.NG-search {
    float: none;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.15s ease-out; }

.NG-search__wrapper {
    background-color: #F5F5F5;
    padding: 15px 0 45px 0; }

.NG-search__title {
    font-family: "OpenSans_Bold";
    font-size: 18px;
    text-align: center;
    margin-top: 16px;
    margin-bottom: 32px;
    color: #424242; }

.NG-inputSearch--wrapper {
    display: flex;
    position: relative;
    justify-content: center;
    max-width: 92%;
    margin: 0 auto;
    margin-bottom: 32px; }

.NG-inputSearch__input {
    padding: 9px;
    border: 1px solid #ddd;
    width: 100%;
    margin-left: 10px; }

.NG-inputSearch__input::placeholder {
    font-family: "OpenSans_Semibold";
    font-size: 13px;
    color: #7d7d7d; }

.NG-inputSearch__button {
          width: 43px;
          height: 43px;
          padding: 7px;
          border-radius: 2px;
          border: solid 1px #1575ae;
          background: #1575ae;}
.NG-inputSearch__button:hover {
    background-color: #0b4a6f;
     border: solid 1px #000;}

.NG-buttons--wrapper {
    max-width: 1200px;
    margin: 0 auto;
    text-align: center;
    padding: 0px 50px;
    line-height: 1.5em;
    margin-bottom: 32px; }

.NG-buttonsSearch {
    font-size: 26px;
    background-color: transparent;
   border: solid 1px #1575ae;
    border-radius: 20px;
    display: inline-block;
    margin-bottom: 8px;
    margin-right: 4px; }

.NG-buttonsSearch:hover {
   background-color: rgba(21, 117, 174, 0.1);
    cursor: pointer; }

.NG-buttonsSearch__Title {
    font-size: 10px;
    position: relative;
    font-family: "OpenSans_Regular";
    font-weight: normal;
    color: #333;
    text-align: center;
    text-decoration: none;
    padding: 4px 16px;
    display: inline-block; }

.NG-autoCompleteCapcalera_list {
    position: absolute;
    z-index: 999999999;
    transition: all 0.1s ease-in-out;
    -webkit-transition: all 0.1s ease-in-out;
    background: #fff;
    -moz-transition: all 0.1s ease-in-out;
    -ms-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out; }
.NG-autoCompleteCapcalera_list.autocomplete-suggestions {
    /* core styles should not be changed */
    display: none;
    max-height: 254px;
    overflow: hidden;
    overflow-y: auto;
    box-sizing: border-box;
    height:300px;
    padding: 0px;
    margin: 0;
    float: left;
    background-color: white;
    position: absolute;
    top: 44px !important;
    left: 11px !important;
    right: 11px !important;
    bottom: 0px !important;
    z-index: 2000;}
.NG-autoCompleteCapcalera_list .autocomplete-suggestion {
    cursor: pointer;
    padding: 5px 10px;
    color: #333;
    font-size: 13px;
    font-weight: 600;
    font-family: "OpenSans_Regular";
    margin: 10px 0px 10px 10px;}
.NG-autoCompleteCapcalera_list .autocomplete-suggestion:hover {
    text-decoration: underline; }
.NG-autoCompleteCapcalera_list .autocomplete-suggestion.selected {
    cursor: pointer;
    text-decoration: underline; }
.NG-autoCompleteCapcalera_list .autocomplete-suggestion b {
    font-weight: 700; }

.NG-navbar__list-language .NG-block__content_label:hover,
.NG-navbar__list-language .NG-block__content_label:focus {
    text-decoration: underline;
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important; }

.NG-nom-idioma-letter {
    margin-left: 5px; }

.NG-idioma-tooltip {
    position: fixed;
    max-width: 276px;
    padding: 1px;
    text-align: left;
    white-space: normal;
    background-color: #fff;
    background-clip: padding-box;
    border-radius: 6px;
    -webkit-box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
    z-index: 9999999999999;
    transform: translateX(-40%);
    -webkit-transform: translateX(-40%);
    -moz-transform: translateX(-40%);
    -ms-transform: translateX(-40%);
    -o-transform: translateX(-40%); }
@media (max-width: 1024px) {
    .NG-header__container--relative .NG-search--expand {
        height: auto !important;
        overflow-y:auto;
    }
    .NG-idioma-tooltip {
        transform: translateX(0);
        -webkit-transform: translateX(0);
        -moz-transform: translateX(0);
        -ms-transform: translateX(0);
        -o-transform: translateX(0); }
    .NG-idioma-tooltip--top {
        transform: translateY(-100%);
        -webkit-transform: translateY(-100%);
        -moz-transform: translateY(-100%);
        -ms-transform: translateY(-100%);
        -o-transform: translateY(-100%); }
    .NG-idioma-tooltip--top .NG-idioma-tooltip__arrow {
        top: 100%;
        transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        -ms-transform: rotate(180deg);
        -o-transform: rotate(180deg); } }
.NG-idioma-tooltip img {
    max-width: 100%; }
.NG-idioma-tooltip__arrow {
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    top: -10px;
    left: -webkit-calc(50% - 10px);
    left: calc(50% - 10px);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #ccc; }
.NG-idioma-tooltip__arrow:after {
    content: " ";
    position: absolute;
    display: block;
    width: 0;
    height: 0;
    top: 1px;
    left: -webkit-calc(50% - 10px);
    left: calc(50% - 10px);
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #fff; }
@media (max-width: 1024px) {
    .NG-idioma-tooltip__arrow {
        left: -webkit-calc(14% - 10px);
        left: calc(14% - 10px); }
    .NG-idioma-tooltip__arrow:after {
        left: -webkit-calc(14% - 10px);
        left: calc(14% - 10px); } }
.NG-idioma-tooltip__content {
    padding: 9px 14px;
    min-width: 200px;
    min-height: 15px; }
.NG-idioma-tooltip__btn {
    background: #C00000;
    color: #ffffff;
    display: none;
    padding: 3px 9px;
    margin-bottom: 0;
    font-size: .875em;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    cursor: pointer;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    -o-user-select: none;
    user-select: none;
    background-image: none;
    border: 1px solid transparent;
    border-radius: 4px; }
@media (hover: none) and (pointer: coarse) {
    .NG-idioma-tooltip__btn {
        display: block;
        margin: auto;
        margin-top: 10px; } }
@media (max-width: 1024px) {
    .NG-idioma-tooltip__btn {
        display: block;
        margin: auto;
        margin-top: 10px; } }

.NG-buttonsSearch__Title:hover,
.NG-buttonsSearch__Title:focus {
    text-shadow: 0 0 .15px #333, 0 0 .15px #333;
    color: #333333;
}

.NG-block__content {
    padding: 6px 36px;
    position: relative; }

.NG-block__content-checkbox-label {
    font-weight: normal; }

.NG-block__content_label {
    font-family: 'OpenSans_Regular';
    font-weight: normal; }
.NG-block__content_label:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important;
    text-decoration: underline; }

.NG-block__content_label:active {
    font-family: 'OpenSans_Bold'; }

.NG-search {
    position: absolute;
    top: 45px;
    z-index: 27;
    width: 100%;
    height: 0px; }

@media (max-width: 1024px) {
    .hide-mobile {
        display: none; }

    .NG-search {
        height: 0; } }

.NG-navbar__down,
.NG__breadcrumb__item_link {
    display: none; }

.NG-navbar__list-language {
    display: none;
    max-height: 0px;
    overflow: hidden;
    max-height: 0;
    flex: 0 0 0;
    margin: 0;
    padding: 0; }
.NG-navbar__list-language--expanded {
    display: block;
    max-height: 100%;
    flex: 0 0 100%;
    animation-name: openCollapse;
    animation-duration: 800ms;
    animation-timing-function: cubic-bezier(0.23, -0.03, 0.33, 0.98);
    animation-fill-mode: forwards; }

@media (max-width: 1024px) {
    .NG-navbar__list-language {
        align-self: flex-start; }
    .NG-navbar__list-language--expanded {
        display: block;
        overflow: hidden auto;
        max-height: 210px !important;
        flex: auto;
        width: 100%;
        height: auto;
        position: relative;}
    .NG-navbar__list-language .NG-block__content {
        padding: 6px 36px 6px 68px; }
    .NG-header__logo {
        bottom: 14px;
        left: 24px; } }

/*iPhone fix language nav*/
@media only screen
and (max-width: 768px)
and (-webkit-device-pixel-ratio: 2),
only screen
and (max-width: 768px)
and (-webkit-device-pixel-ratio: 3){
    .NG-navbar__list-language--expanded{
        height: 110px;
    }
}

/* tablet*/
@media (min-width: 768px) and (max-width: 1024px) {
    .NG-navbar {
        max-width: 65%; }
    .NG-container {
        border-right: 1px solid #ddd; }
    .NG-inputSearch--wrapper {
        margin: 0 auto;
        margin-bottom: 16px; } }

/* desktop*/
@media (min-width: 1025px) {
    .NG-navbar .NG-navbar__menu:last-child {
        display: none; }
    .NG-header__container {
        z-index: 2;
        transition: all .8s ease;
        position: sticky;
        height:auto !important;}
    .secondary-header {
        display: block;
        position: fixed;
        top: 0;
        z-index: 0; }
    .NG-search__title {
        font-size: 22px;
        margin-top: 29px; }
    .NG-block__content_label {
        margin-left: 0; }
    .NG-block__content--traduccio {
        border-top: 1px solid #DDDDDD; }
    .NG-navbar__menu .NG-navbar__link--black {
        display: block;
        padding: 14px 59px;
        line-height: 1.2em; }
    .NG-navbar__menu--invert {
        margin: 0;
        padding: 0;
        padding-left: 10px;
        color: #ffffff; }
    .NG-buttonsSearch__Title {
        font-size: 14px; }
    .NG-buttonsSearch {
        margin-right: 8px; }
    .NG-buttonsSearch__Title:hover {
        text-decoration: none !important; }
    .NG-navbar__menu--invert .NG-navbar__link {
        color: white;
        font-size: 14px;
        padding: 12px 25px;
        display: block; }
    .NG-navbar__menu--invert .NG-navbar__link:focus {
        outline: none !important;
        outline-offset: 0 !important;
        box-shadow: none !important;
        text-decoration: underline; }
    .NG-navbar__list--border {
        border-top: 0; }
    .NG-navbar {
        display: block;
        max-height: 600px; }
    .NG-idioma {
        display: none; }
    .NG-nom-idioma-letter {
        text-transform: uppercase;
        position: relative;
        top: 2px; }
    .NG-navbar-textlenguage {
        display: none; }
    .NG-inputSearch--wrapper {
        max-width: 550px; }
    .NG-block__content {
        padding: 6px 16px; }
    .NG-block__content--active {
        background-color: #f5f5f5; }
    .NG-block__content--active .NG-block__content_label {
        text-decoration: underline;
        cursor: default; }
    .NG-block__content_label {
        font-size: 12px;
        font-family: "OpenSans_Semibold";
        cursor: pointer;
        margin-bottom: 0; }
    .NG-block__content_label:hover,
    .NG-block__content_label:focus {
        text-decoration: underline;
        outline: thin dotted;
        outline-offset: -2px;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(192, 0, 0, 0.6); }
    .NG-navbar__menu .NG-navbar__list:after {
        content: '|';
        z-index: 1;
        width:5px;
        align-self: center;
        color: #dddddd; }
    .NG-navbar__menu .NG-navbar__list:last-of-type:after {
        content: ''; }
    .NG-navbar__menu--invert .NG-navbar__list:after,
    .NG-navbar__menu--invert .NG-navbar__list:last-of-type:after {
        content: '|';
        color: #ddd; }
    .NG-navbar__image {
        display: none; }
    .NG-navbar__image--area-privada {
        background-image: url("https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/la-meva-carpeta-w.svg");
        background-repeat: no-repeat;
        background-position-y: center;
        background-size: 15px; }
    .NG-navbar__list-language {
        position: absolute;
        z-index: 2;
        top: 100%;
        right: -28px;
        color: #3a3a3a;
        background-color: #fff;
        box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.3);
        min-width: 144px;
        z-index: 99; }
    .NG-header__icon {
        display: none; }
    .NG-header__icon2 {
        top: 11px;
        margin-left: 26px; }
    .NG-header__icon2:hover {
        border-bottom: 1px solid #FFFFFF; }
    .NG-header__icon2:focus {
        outline: thin dotted;
        outline-offset: -2px;
        box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(192, 0, 0, 0.6); }
    .block__content-radio {
        display: none; }
    .NG-search {
        bottom: 46px;
        min-height: auto; }
    .NG-navbar__down,
    .NG__breadcrumb__item_link {
        display: inline; }
    .NG-navbar__down {
        transform: rotate(0);
        -webkit-transform: rotate(0);
        -moz-transform: rotate(0);
        -ms-transform: rotate(0);
        -o-transform: rotate(0);
        transition: transform 300ms ease;
        -webkit-transition: transform 300ms ease;
        -moz-transition: transform 300ms ease;
        -ms-transition: transform 300ms ease;
        -o-transition: transform 300ms ease; }
    .NG-navbar__link--active .NG-navbar__down {
        transform: rotate(-180deg);
        -webkit-transform: rotate(-180deg);
        -moz-transform: rotate(-180deg);
        -ms-transform: rotate(-180deg);
        -o-transform: rotate(-180deg); } }

/*utilities*/
.NG-search {
    animation-name: closeCollapse;
    animation-duration: 1000ms;
    animation-timing-function: ease-out;
    animation-fill-mode: backwards;
    max-height: 0;
    overflow: hidden;
    transition: max-height 1s  ease-in-out;
    -webkit-transition: max-height 600ms  ease-in-out;
    -moz-transition: max-height 600ms  ease-in-out;
    -ms-transition: max-height 600ms  ease-in-out;
    -o-transition: max-height 600ms  ease-in-out; }

.NG-search--expand {
    max-height: 350px;
    /*display: block;
    animation-name: openCollapse;
    animation-duration: 1000ms;
    animation-timing-function: ease-in;
    animation-fill-mode: forwards;
    overflow: revert;*/}
@media (max-height: 1024px) {
    .NG-search--expand {
        max-height: 75vh; } }

.NG-collapse {
    display: none;
    max-height: 0px;
    overflow: hidden; }

.NG-collapse-expand {
    display: block;
    animation-name: openCollapse;
    animation-duration: 300ms;
    animation-timing-function: ease-in;
    animation-fill-mode: forwards; }

.NG-navbar__link--active {
    font-family: "OpenSans_Bold"; }
.NG-navbar__link--active .NG-rotate-image {
    transform: rotate(180deg);
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg); }

.NG-rotate-image {
    transition: all .300ms ease;
    -webkit-transition: all .300ms ease;
    -moz-transition: all .300ms ease;
    -ms-transition: all .300ms ease;
    -o-transition: all .300ms ease; }

.NG-rotate {
    transform: rotate(180deg); }

.NG-none {
    display: none; }

.NG-pd--0 {
    padding: 0; }

.NG-block {
    display: block; }

.NG-Modal_Backdrop {
    /*Se cambia el Z-index a 0 desde 1040*/
    z-index: 0;
    background-color: #000;
    opacity: 0;
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    animation-name: fadeInSeu;
    animation-duration: 400ms;
    animation-delay: 300ms;
    animation-timing-function: ease;
    animation-fill-mode: forwards; }

@media (max-width: 1024px) {
    .NG-header__container--relative {
        padding-top: 0;
        height: 100%!important;
    }
    .NG-header__container--fixed {
        height: 100% !important;
        padding-top: 0;
    }

    .NG-header__container--fixed .NG-main {
        position: fixed;
        top: 0;
        width: 100%;
        background-color: #333333;
        z-index: 2; }

    .NG-header__container--fixed .NG-header__wrapper {
        position: fixed;
        top: 49px;
        width: 100%;
        z-index: 2; }

    .NG-header__container--fixed .NG-navbar,
    .NG-header__container--fixed .NG-search {
        position: fixed; } }

.NG-header__container--fixed .NG-search {
    height: auto !important;
    overflow-y:auto;
}

.NG-departament__background-wrapper {
    background-color: #404040; }

.NG-departament__title {
    font-family: 'OpenSans_Regular', Arial, Helvetica, sans-serif;
    font-size: 20px;
    line-height: 36px;
    color: #FFFFFF;
    margin: 0; }
.NG-departament__title > a {
    color: #ffffff;
    text-align: center;
}
.NG-departament__title > a:hover {
    color: #FFFFFF;
    text-decoration: none !important;
    background-image: linear-gradient(to bottom, #fff, #fff);
    background-position: 0 88%;
    background-repeat: repeat-x;
    background-size: 1px 1px; }
.NG-departament__title > a:focus {
    color: #FFFFFF;
    text-decoration: none !important;
    background-image: linear-gradient(to bottom, #fff, #fff);
    background-position: 0 88%;
    background-repeat: repeat-x;
    background-size: 1px 1px;
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important; }

@media (max-width: 767px) {
    .NG-departament__title {
        font-size: 14px;
        line-height: 1.5;
    }

    .NG-departament__title > a:hover {
        background-image: none;
    }
}

.NG-departament__background-wrapper--top .NG-departament__title {
    padding-top: 8px;
    padding-bottom: 8px; }

.NG-departament__background-wrapper--bottom .NG-departament__title {
    font-family: 'OpenSans_Semibold', Arial, Helvetica, sans-serif;
    font-size: 24px;
    padding-top: 0;
    padding-bottom: 12px; }
@media (max-width: 768px) {
    .NG-departament__background-wrapper--bottom .NG-departament__title {
        font-size: 14px;
        padding-top: 9px;
        padding-bottom: 9px; } }

.NG-departament--microsite-hasimage {
    position: relative; }
.NG-departament--microsite-hasimage .NG-departament__image-wrapper {
    position: relative;
    width: 100%;
    height: 400px;
    overflow: hidden; }
.NG-departament--microsite-hasimage .NG-departament__image {
    position: absolute;
    min-width: 100%;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%); }
.NG-departament--microsite-hasimage .NG-departament__background-wrapper--bottom {
    background-color: rgba(51, 51, 51, 0.8);
    position: absolute;
    width: 100%;
    bottom: 0; }

@keyframes leftL3 {
    0% {
        left: 760px; }
    100% {
        left: 380px; } }

@keyframes leftL4 {
    0% {
        left: 100%; }
    100% {
        left: 760px; } }

@keyframes submenuMobile {
    0% {
        left: -100%; }
    100% {
        left: 0; } }

.NG-navbar {
    background-color: #FFFFFF;
    float: none;
    max-height: 0;
    transition: max-height 600ms cubic-bezier(0.71, 0.02, 0.88, 0.68);
    -webkit-transition: max-height 600ms cubic-bezier(0.71, 0.02, 0.88, 0.68);
    -moz-transition: max-height 600ms cubic-bezier(0.71, 0.02, 0.88, 0.68);
    -ms-transition: max-height 600ms cubic-bezier(0.71, 0.02, 0.88, 0.68);
    -o-transition: max-height 600ms cubic-bezier(0.71, 0.02, 0.88, 0.68);
    margin-top: 1px;
    min-height: auto;
    width: 100%;
    /*tablet*/ }
@media (min-width: 768px) and (max-width: 1024px) {
    .NG-navbar {
        max-width: 65%; } }
@media (max-width: 1024px) {
    .NG-navbar {
        overflow-x: hidden;
        overflow-y: auto;
        position: absolute;
        border-top: none; }
    .NG-navbar .js-has-megamenu > .NG-navbar__link {
        cursor: pointer;
        display: block;
        position: relative;
        padding-right: 30px; }
    .NG-navbar .js-has-megamenu > .NG-navbar__link::after {
        content: "";
        width: 24px;
        height: 24px;
        background-image: url(https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/chevron-right-thin-b.svg);
        position: absolute;
        right: 33px; } }

.NG-navbar__link {
    cursor: pointer;
    position: relative; }

.NG-navbar .NG-navbar__menu:last-child {
    display: block; }

@media (max-width: 1024px) {
    .NG-navbar__menu {
        overflow: hidden;
        position: relative;
        background: #fff;
        z-index: 99;
        max-height: -webkit-calc(40vh + 80px);
        max-height: calc(40vh + 80px);
        transition: all 300ms ease;
        -webkit-transition: all 300ms ease;
        -moz-transition: all 300ms ease;
        -ms-transition: all 300ms ease;
        -o-transition: all 300ms ease; }
    .NG-navbar__menu--visible {
        overflow: hidden auto; } }

@media (min-width: 1025px) {
    .NG-navbar__menu {
        width: 100%;
        padding-left: 15px;
        padding-right: 15px; } }

.NG-navbar__menu-mobile--wrapper .NG-navbar__menu {
    background: #f5f5f5; }

.NG-navbar-expand {
    max-height: 560px; }
@media (max-width: 1024px) {
    .NG-navbar-expand {
        max-height: 100vh; } }

.NG-megamenu {
    position: absolute;
    top: 100%;
    z-index: 99;
    padding: 15px 0;
    background: white;
    width: -webkit-calc(100% - 30px);
    width: calc(100% - 30px);
    height: 0;
    left: 50%;
    border-top: 1px solid #ddd;
    overflow: hidden;
    display: none;
    transform: translateX(-50%);
    -webkit-transform: translateX(-50%);
    -moz-transform: translateX(-50%);
    -ms-transform: translateX(-50%);
    -o-transform: translateX(-50%);
    transition: all ease 300ms;
    -webkit-transition: all ease 300ms;
    -moz-transition: all ease 300ms;
    -ms-transition: all ease 300ms;
    -o-transition: all ease 300ms; }
.NG-megamenu .NG-megamenu__nav-submenu:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important; }
@media (max-width: 1024px) {
    .NG-megamenu {
        display: flex;
        visibility: hidden;
        top: 0;
        height: 100%;
        width: 100%;
        padding: 0;
        left: 150%;
        z-index: 1;
        transition: left 300ms ease;
        -webkit-transition: left 300ms ease;
        -moz-transition: left 300ms ease;
        -ms-transition: left 300ms ease;
        -o-transition: left 300ms ease; }
    .NG-megamenu .NG-col {
        width: 100%; } }

@media (max-width: 1024px) {
    .has-megamenu--expand .NG-megamenu {
        visibility: visible; }
    .NG-megamenu__nav {
        max-height: 40vh;
        overflow: hidden auto; } }

.NG-megamenu__nav {
    padding-left: 0;
    background-color: #fff; }

.NG-megamenu__nav-wrapper {
    min-height: 200px; }
.NG-megamenu__nav-wrapper:after {
    height: 100%;
    content: "";
    position: absolute;
    width: 1px;
    top: 50%;
    right: 3px;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    background-color: #ddd; }
@media (max-width: 1024px) {
    .NG-megamenu__nav-wrapper:after {
        display: none; } }
.NG-megamenu__nav-wrapper .NG-navbar__list--selected > a:before,
.NG-megamenu__nav-wrapper .NG-navbar__list--selected > button:before {
    content: "";
    position: absolute;
    width: 4px;
    height: 18px;
    top: 0;
    left: -20px;
    background: rgba(192, 0, 0, 0.5);
    border-radius: 15px;
}
@media (min-width: 1025px) {
    .NG-megamenu__nav-wrapper {
        min-height: 0;
        position: relative;
        padding-left: 0;
        z-index: 1;
        min-width: 380px; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper {
        position: absolute;
        display: none;
        z-index: 1;
        background-color: #fff; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper {
        z-index: -1; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper:after {
        display: none; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper {
        z-index: -1;
        display: none !important; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active {
        background-color: #F5F5F5; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: block;
        position: fixed;
        left: 380px;
        top: 15px;
        height: calc(100% - 30px);
        padding-left: 0;
        z-index: -1;
        animation-name: leftL3;
        animation-duration: 0.4s;
        animation-timing-function: ease-in;
        animation-fill-mode: forwards; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        position: fixed;
        left: 760px;
        top: 15px;
        height: calc(100% - 30px);
        padding-left: 0;
        z-index: -1;
        animation-name: leftL4;
        animation-duration: 0.3s;
        animation-timing-function: ease-in;
        animation-fill-mode: forwards; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item {
        padding: 14px 20px;
        width: calc(100% - 30px);
        width: -webkit-calc(100% - 30px);
        max-width: 380px;
        margin-left: auto;
        margin-right: auto; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item > a {
        position: relative; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item > a:hover,
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item > a:focus {
        font-family: "OpenSans_Semibold";
        color: #333333;
        text-decoration: none !important;
        outline: none !important;
        outline-offset: 0 !important;
        box-shadow: none !important; } }
@media (max-width: 1024px) {
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper {
        visibility: hidden;
        position: absolute;
        position: fixed;
        left: 150%;
        transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -webkit-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -moz-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -ms-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -o-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98); }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item > a {
        position: relative;
        display:inline-block;
        width: 100%;
    }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item > a:focus {
        text-decoration: underline;
        outline: none !important;
        outline-offset: 0 !important;
        box-shadow: none !important; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-wrapper {
        display: flex;
        visibility: visible; }
    .NG-megamenu__nav-wrapper {
        height: auto;
        min-height: 0;
        position: relative;
        padding-left: 0;
        background-color: #FFFFFF;
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        z-index: 1;
        width: 100vw;
        max-width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-wrapper {
        position: absolute;
        position: fixed;
        left: 150%;
        transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -webkit-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -moz-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -ms-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98);
        -o-transition: left 600ms cubic-bezier(0.6, 0.01, 0.33, 0.98); }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-wrapper {
        display: flex;
        visibility: visible; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: flex;
        visibility: visible;
        position: fixed;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        height: 100%;
        padding-left: 0;
        width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: flex;
        visibility: visible;
        position: fixed;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        height: 100%;
        padding-left: 0;
        width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: flex;
        visibility: visible;
        position: fixed;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        height: 100%;
        padding-left: 0;
        width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: flex;
        visibility: visible;
        position: fixed;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        height: 100%;
        padding-left: 0;
        width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav-wrapper {
        background-color: #FFFFFF;
        display: flex;
        visibility: visible;
        position: fixed;
        flex-direction: column;
        justify-content: flex-start;
        left: 0;
        top: 0;
        height: 100%;
        padding-left: 0;
        width: 100%; }
    .NG-megamenu__nav-wrapper .NG-megamenu__nav-item {
        padding: 14px 32px; } }

.NG-megamenu__nav-submenu {
    cursor: pointer;
    display: block;
    position: relative;
    padding-right: 30px; }
.NG-megamenu__nav-submenu--active {
    font-family: "OpenSans_Semibold"; }

.NG-megamenu__nav-submenu::after {
    content: "";
    width: 24px;
    height: 24px;
    background-image: url(https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/chevron-right-thin-b.svg);
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%); }

.NG-megamenu__img-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-left: 6px solid #FFFFFF;
    border-right: 6px solid #FFFFFF; }

.NG-megamenu__img-centered {
    position: absolute;
    left: 50%;
    top: 50%;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    transform: translate(-50%, -50%);
    -webkit-transform: translate(-50%, -50%);
    -moz-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    -o-transform: translate(-50%, -50%); }

.NG-megamenu__description {
    padding: 6px;
    flex: 1; }
.NG-megamenu__description-wrapper {
    padding: 15px;
    width: 100%;
    background-color: #f5f5f5; }

.NG-megamenu__description-title {
    font-family: "OpenSans_Semibold", Arial, Helvetica, sans-serif;
    font-size: 18px;
    margin-bottom: 12px; }

@media (min-width: 1025px) {
    .NG-megamenu__nav-self--wrapper-mobile {
        display: none; } }

.NG-megamenu__nav-self--wrapper-desktop .NG-megamenu__nav-self {
    margin-top: 30px;
    margin-bottom: 0;
    padding: 0;
    width: 100%;
    display: flex;
    flex-direction: row-reverse; }

@media (max-width: 1024px) {
    .NG-megamenu__nav-self--wrapper-desktop {
        display: none; } }

.NG-megamenu__nav-self {
    padding: 14px 32px 14px 33px;
    margin-bottom: 0; }
.NG-megamenu__nav-self .NG-megamenu__nav-link-self {
    font-family: "OpenSans_Semibold",Arial, Helvetica, sans-serif;
    color: #a81b8d; }
/*.NG-megamenu__nav-self .NG-megamenu__nav-link-self img{display:none;}*/
.NG-megamenu__nav-self .NG-megamenu__nav-link-self:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important;
    text-decoration: underline; }
@media (min-width: 1025px) {
    .NG-megamenu__nav-self .NG-megamenu__nav-link-back {
        display: none; } }

.NG-megamenu__nav-self--top {
    padding: 14px 32px 14px 33px;
    display: block;
    width: 100%;
    background-color: #f5f5f5;
    padding: 12px 5px 12px 33px;
    border-bottom: 1px solid #ddd; }
.NG-megamenu__nav-self--top .NG-megamenu__nav-self-wrapper{
    margin-bottom: 0;
}
.NG-megamenu__nav-self--top .NG-megamenu__nav-link-self {
    font-family: "OpenSans_Bold", Arial, Helvetica, sans-serif;
    font-size: 18px;
    margin-left: 24px;
    color: #333333;
    display: block; }
.NG-megamenu__nav-self--top .NG-megamenu__nav-link-self:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important;
    text-decoration: underline; }
@media (min-width: 1025px) {
    .NG-megamenu__nav-self--top {
        display: none; } }
@media (max-width: 1024px) {
    .NG-megamenu__nav-self--top .NG-megamenu__nav-link-self > img,
    .NG-megamenu__nav-self--top .NG-megamenu__nav-link-self > span {
        display: none; } }
button.NG-megamenu__nav-link-back, button.NG-navbar__link{
    border: none;
    background: none;
    text-align: left;
}
button.NG-megamenu__nav-submenu{
    width: 100%;
    text-align: left;
    border: none;
    background: none;
    padding: 0 2em 0 0;
}
button.NG-megamenu__nav-submenu:hover {
    font-family:"OpenSans_Semibold";
}
.NG-megamenu__nav-link-back {
    padding: 0;
    position: absolute;
    left: 5px; }
.NG-megamenu__nav-link-back:after {
    content: "";
    background-image: url(https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/img/chevron-left-b.svg);
    width: 24px;
    height: 24px;
    position: relative;
    display: inline-block;
    transform: rotate(90deg);
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -ms-transform: rotate(90deg);
    -o-transform: rotate(90deg); }
.NG-megamenu__nav-link-back:hover .NG-megamenu__nav-link-back-trapezium, .NG-megamenu__nav-link-back:active .NG-megamenu__nav-link-back-trapezium, .NG-megamenu__nav-link-back:focus .NG-megamenu__nav-link-back-trapezium {
    border-top: 49px solid #ddd;
    border-left: 0px solid transparent;
    border-right: 10px solid transparent;
    height: 0;
    width: 40px;
    position: absolute;
    top: -13px;
    left: -6px;
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important; }
.NG-megamenu__nav-link-back:hover .NG-megamenu__nav-link-back-trapezium:after, .NG-megamenu__nav-link-back:active .NG-megamenu__nav-link-back-trapezium:after, .NG-megamenu__nav-link-back:focus .NG-megamenu__nav-link-back-trapezium:after {
    display: none; }

.NG-megamenu__nav-link-back-trapezium:after {
    content: "";
    width: 57px;
    height: 33px;
    border-bottom: 1px solid #ccc;
    position: absolute;
    top: -33px;
    left: -13px;
    transform: translateY(22px) translateX(-1px) rotate(-76deg);
    -webkit-transform: translateY(22px) translateX(-1px) rotate(-76deg);
    -moz-transform: translateY(22px) translateX(-1px) rotate(-76deg);
    -ms-transform: translateY(22px) translateX(-1px) rotate(-76deg);
    -o-transform: translateY(22px) translateX(-1px) rotate(-76deg); }

@media (min-width: 1025px) {
    .NG-navbar .NG-departament {
        display: none; } }

@media (max-width: 1024px) {
    .NG-navbar .NG-departament__title {
        font-size: 16px;
        padding-top: 10px; }
    .NG-navbar .NG-departament__background-wrapper--bottom .NG-departament__title {
        padding-top: 0;
        padding-bottom: 10px; } }

.NG-navbar__list--has-megamenu:hover .NG-megamenu {
    display: block; }

.has-megamenu--expand .NG-megamenu {
    display: block;
    height: 500px; }
@media (max-width: 1024px) {
    .has-megamenu--expand .NG-megamenu {
        left: 50%; } }

@media (min-width: 1025px) {
    .NG-megamenu__nav-item--active > .NG-megamenu__nav {
        background-color: #FFFFFF;
        display: block;
        position: fixed;
        left: 380px;
        top: 0;
        height: 100%;
        padding-left: 0;
        z-index: 1; }
    .NG-megamenu__nav-item--active .NG-megamenu__nav-item--active > .NG-megamenu__nav {
        background-color: #FFFFFF;
        display: block;
        position: fixed;
        left: 760px;
        top: 50%;
        height: 100%;
        padding-left: 0;
        z-index: 1;
        transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%); }
    .js-NG-hide-description {
        overflow: hidden;
        max-width: 100%;
        transition: max-width 500ms ease-out;
        -webkit-transition: max-width 500ms ease-out;
        -moz-transition: max-width 500ms ease-out;
        -ms-transition: max-width 500ms ease-out;
        -o-transition: max-width 500ms ease-out; }
    .js-NG-hide-description--close {
        max-width: 0; } }

/*mobile*/
@media (max-width: 1024px) {
    .NG-navbar__menu > .NG-navbar__list--white.NG-navbar__list--active > .NG-navbar__link:before {
        content: "";
        position: absolute;
        width: 4px;
        height: 18px;
        top: 50%;
        left: 16px;
        background-color: #a81b8d;
        opacity: 0.5;
        border-radius: 15px;
        transform: translateY(-50%);
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%); } }

/* desktop*/
@media (min-width: 1025px) {
    .NG-navbar .NG-navbar__menu:last-child {
        display: none; }
    .NG-navbar {
        display: block;
        max-height: 600px;
        border-bottom: 1px solid #DDDDDD;
        border-top: 0;
        margin-top: 0;
    }
    .NG-navbar__link {
        position: relative;
        /*white-space: nowrap;*/ }
    .NG-navbar__list--white.NG-navbar__list--active .NG-navbar__link:after {
        position: absolute;
        content: "";
        background-color: #D38DC6;
        width: 74px;
        height: 4px;
        bottom: -2.7px;
        border-radius: 4px;
        z-index: 25;
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%); }
    .NG-navbar__list--white.has-megamenu--expand .NG-navbar__link:after {
        position: absolute;
        content: "";
        background-color: #a81b8d;
        width: 74px;
        height: 4px;
        bottom: -2px;
        border-radius: 4px;
        z-index: 25;
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%); }
    .NG-navbar__list--white .NG-navbar__link:hover:after,
    .NG-navbar__list--white .NG-navbar__link:focus:after {
        position: absolute;
        content: "";
        background-color: #a81b8d;
        width: 74px;
        height: 4px;
        bottom: -2.7px;
        border-radius: 4px;
        z-index: 25;
        left: 50%;
        transform: translateX(-50%);
        -webkit-transform: translateX(-50%);
        -moz-transform: translateX(-50%);
        -ms-transform: translateX(-50%);
        -o-transform: translateX(-50%); } }

@media (min-width: 1025px) {
    .NG-navbar__menu--isMedium button.NG-navbar__link.NG-navbar__link--noborder,
    .NG-navbar__menu--isMedium a.NG-navbar__link.NG-navbar__link--noborder {
        padding-left: 20px;
        padding-right: 20px; } }

@media (min-width: 1199px) {
    .NG-navbar__menu button.NG-navbar__link.NG-navbar__link--noborder,
    .NG-navbar__menu a.NG-navbar__link.NG-navbar__link--noborder {
        padding: 12px 20px;

    } }

nav.NG-navbar{
    margin-left: 0px;
}

/*IE11*/
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .NG-header__container{
        position:relative;
    }
    .NG-navbar__list{
        display:inline-flex;
    }
    .NG-navbar{
        margin-top: 0;
    }
}


