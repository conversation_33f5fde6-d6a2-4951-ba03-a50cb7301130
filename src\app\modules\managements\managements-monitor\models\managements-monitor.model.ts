import { LazyLoadEvent } from 'primeng/api';
import { PtTableLinkTemplate, iCommonError } from 'pt-ui-components-mf-lib';

export const MANAGEMENTS_MONITOR_FILTER_STORAGE = 'managements-monitor-filter-storage';
export const MANAGEMENTS_MONITOR_DETAIL_STORAGE = 'managements-monitor-detail-storage';

export enum States {
	GENERAT="GENERAT",
	TRAMITAT="TRAMITAT",
	TRAMITANT="TRAMITANT",
	CONSOLIDAT="CONSOLIDAT",
	CONSOLIDANT="CONSOLIDANT",
	TRAMITACIO_ERROR="TRAMITACIO_ERROR",
	CONSOLIDACIO_ERROR="CONSOLIDACIO_ERROR",
	ERROR="ERROR",
	ESBORRANY="ESBORRANY",
	PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
}
export type StatesT = keyof typeof States;
export type StatesValues<States> = States[keyof States];

export interface IManagementsMonitor {
	id:string,
	idTramitacio:string,
	estat:string,
	subjectePassiuNif:string,
	subjectePassiuNom:string,
	presentadorNif:string,
	presentadorNom:string,
	origen:string,
	tramit: string,
	dadesAddicionals:string,
	dataModificacio: Date,
	dataAlta: Date,
	errors:iCommonError[],
	canviEstat: CanviEstat[],
	idGestio: string
}

export interface ManagementsMonitorRow extends IManagementsMonitor {
	stateRow: PtTableLinkTemplate;
}

export interface CanviEstat {
	estatAnterior: string,
	estat: string,
	dataCanvi: Date
}

/**
 * Autoliquidacions: search filters
 */
export interface ManagementsMonitorFilter {
	dateFromForm: Date;
	dateToForm: Date;
	dateFrom: number;
	dateTo: number;
	state:string;
	subjectePassiuNif:string;
	presentadorNif:string;
	tipus: TramitType;
	idTramitacio: string;
	dadesAddicionals: string;
}

export class ManagementsMonitorRequest {
	filter: Partial<ManagementsMonitorFilter>;
	options: LazyLoadEvent;

	constructor(params: Partial<ManagementsMonitorFilter>, options: LazyLoadEvent) {
		const df = new Date(params.dateFromForm);
		const dt = new Date(params.dateToForm);
		const from: Date = new Date(df.getFullYear(), df.getMonth(), df.getDate(), 0, 0, 0);
		const to: Date = new Date(dt.getFullYear(), dt.getMonth(), dt.getDate() + 1, 0, 0, 0);

		this.filter = {
			...params,
			dateFrom: from.getTime(),
			dateTo: to.getTime(),
		}
		this.options = options
	}
}

export enum OriginOptions {
	GAUDI = "GAUDI",
	E_SPRIU = "E_SPRIU",
	DALI = "DALI",
	PADOCT = "PADOCT",
	MIRO = "MIRO"
}

export enum TramitType {
	//IIIMA = 'IIIMA',
	//ITPAJ = 'ITPAJ',
	//IGEC = 'IGEC',
	//DONACIONS = 'DONACIONS',
	//IANP = 'IANP',
	//ISBEE = 'ISBEE',
	//TFJA = 'TFJA',
	//ASSEGURANCES = 'ASSEGURANCES',
	ALLEGACIO_CO2 = 'ALLEGACIO_CO2',
	RECURS = 'RECURS',
	RECURS_REPOSICIO_CO2 = 'RECURS_REPOSICIO_CO2',
	REA_CO2 = 'REA_CO2',
	AVISOS = 'AVISOS',
	REA = 'REA',
	DOMICILIACIO = 'DOMICILIACIO',
	CANVI_ADRECA = 'CANVI_ADRECA',
	ALTA_NE = 'ALTA_NE',
	MOD_NE = 'MOD_NE',
	EXPEDIENT_CSV = 'EXPEDIENT_CSV',
	DEVOLUCIO_INGRESSOS = 'DEVOLUCIO_INGRESSOS',
	SIGNATURA_INSPECCIO = 'SIGNATURA_INSPECCIO',
	APORTAR_DOCUMENTACIO = 'APORTAR_DOCUMENTACIO',
	AJORNAMENT_FRACCIONAMENT = 'AJORNAMENT_FRACCIONAMENT',
}
