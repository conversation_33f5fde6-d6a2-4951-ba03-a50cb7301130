import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { BiometricSignatureDetailComponent } from './biometric-signature-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { DetailPageModule } from '../../../../shared/detail-page';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { BioDocumentsTabComponent } from './components/bio-documents-tab/bio-documents-tab.component';

const routes: Routes = [
	{
		path: '',
    component: BiometricSignatureDetailComponent,
		data: {
			title: ''
		}
	}
];

@NgModule({
	declarations: [
		BiometricSignatureDetailComponent,
		BioDocumentsTabComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule,
		DetailPageModule,
    RouterModule.forChild(routes)
	]
})
export class BiometricSignatureDetailModule { }
