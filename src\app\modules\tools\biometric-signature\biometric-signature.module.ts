import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { BiometricSignatureTableComponent } from './modules/biometric-signature-table/biometric-signature-table.component';
import { RouterModule, Routes } from '@angular/router';
import { ModalFilterModule } from 'src/app/modules/shared/modal-filter';
import { BiometricSignatureComponent } from './biometric-signature.component';
import { MetadadesModalComponent } from './components';

const routes: Routes = [
	{
		path: '',
    component: BiometricSignatureComponent,
		data: {
			title: 'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.PAGE_TITLE',
		}
	}
];

@NgModule({
	declarations: [
		BiometricSignatureComponent,
		BiometricSignatureTableComponent,
		MetadadesModalComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		ModalFilterModule,
		TranslateModule.forChild(),
		RouterModule.forChild(routes),
		PtUiComponentsMfLibModule
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class BiometricSignatureModule { }
