<!doctype html>
<html class="atc_integracio" xml:lang="ca-ES" lang="ca-ES">
 <head> 
  <meta charset="UTF-8"> 
  <meta http-equiv="X-UA-Compatible" content="IE=edge"> 
  <meta name="viewport" content="width=device-width, initial-scale=1.0"> 
  <meta name="WT.z_idioma" content="ca"> 
  <!-- CHATBOT ATC --> 
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no"> 
  <meta name="theme-color" content="#000000"> 
  <!----> 
  <title>Agència Tributària de Catalunya</title> 
  <meta name="theme-color" content="#000000"> 
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"> 
  <link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet"> 
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/3.5.2/animate.min.css"> 
  <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons"> 
  <link rel="shortcut icon" type="image/x-icon" href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/img/favicon.ico"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/css/lib/font-awesome.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/css/gencat.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/css/main.css" rel="stylesheet" type="text/css"> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/js/lib/jquery-1.11.3.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/js/lib/fontawesome-markers.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/js/functions.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/js/moment.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsives/common/js/master.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsives/common/js/entorn_config.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsives/common/js/generic_responsive.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsives/common/js/master.responsive.js" type="text/javascript"></script> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_xarxesSocials_seu/css/fpca_peu_xarxesSocials_seu.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_promocional_seu/css/fpca_peu_promocional_seu.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_contacte_seu/css/fpca_peu_contacte_seu.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_ambFinancamentDe_seu/css/fpca_peu_ambFinancamentDe_seu.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_seuNou/css/fpca_peu_seuNou.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/css/fpca_capcalera_seuNou.css" rel="stylesheet" type="text/css"> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/capcaleraNou/common/css/simplebar.css" rel="stylesheet" type="text/css"> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_xarxesSocials_seu/js/fpca_peu_xarxesSocials_seu.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_promocional_seu/js/fpca_peu_promocional_seu.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_contacte_seu/js/fpca_peu_contacte_seu.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_ambFinancamentDe_seu/js/fpca_peu_ambFinancamentDe_seu.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_seuNou/js/fpca_peu_seuNou.js" type="text/javascript"></script> 
  <!-- script src="https://atc.gencat.cat/web/resources/fwkResponsive/fpca_capcalera_seuNou/js/fpca_capcalera.js" type="text/javascript"></script --> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/capcaleraNou/common/js/simplebar.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/capcaleraNou/common/js/autoComplete.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/capcaleraNou/common/js/algoliasearch.min.js" type="text/javascript"></script> 
  <script src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/capcaleraNou/common/js/autoComplete.js" type="text/javascript"></script> 
  <script>window.twttr = (function(d, s, id) {
  var js, fjs = d.getElementsByTagName(s)[0],
    t = window.twttr || {};
  if (d.getElementById(id)) return t;
  js = d.createElement(s);
  js.id = id;
  js.src = "https://platform.twitter.com/widgets.js";
  fjs.parentNode.insertBefore(js, fjs);
 
  t._e = [];
  t.ready = function(f) {
    t._e.push(f);
  };
 
  return t;
}(document, "script", "twitter-wjs"));</script> 
  <link href="https://atc.gencat.cat/web/resources/fwkResponsives/common/css/print.css" rel="stylesheet" type="text/css"> 
  <!--/* Portal Tributari */--> 
  <base href="/ca/"> 
  <link rel="stylesheet" href="/Style%20Library/atc/pt-styles.css"> 
  <!--script type="text/javascript" src="/Style%20Library/atc/pt-index.js"></script--> 
  <!-- Webcomponent: Dispatcher --> 
  <link rel="stylesheet" href="/mf/pt-dispatcher-mf/styles.css"> 
  <script src="/mf/pt-dispatcher-mf/mf-dispatcher.js" type="text/javascript"></script> 

    <!-- Webcomponent: Cabecera -->
    <link rel="stylesheet" href="/mf/se-capcalera-mf/styles.css">
    <script src="/mf/se-capcalera-mf/se-capcalera.js" type="text/javascript"></script>


  <!-- Google recaptcha --> 
  <script src="https://www.google.com/recaptcha/enterprise.js?render=explicit&onload=initRecaptcha" async defer></script> 
  <!-- Google maps --> 
  <script type="text/javascript" src="https://maps.google.com/maps/api/js?key=AIzaSyCzKW2j6ndy1JRg2ZV10jAQK-fnrX8BFAU"></script> 
  <!--/* end Portal Tributari */--> 
  <script type="text/javascript">
(function(window, document, dataLayerName, id) {
window[dataLayerName]=window[dataLayerName]||[],window[dataLayerName].push({start:(new Date).getTime(),event:"stg.start"});var scripts=document.getElementsByTagName('script')[0],tags=document.createElement('script');
function stgCreateCookie(a,b,c){var d="";if(c){var e=new Date;e.setTime(e.getTime()+24*c*60*60*1e3),d="; expires="+e.toUTCString();f="; SameSite=Strict"}document.cookie=a+"="+b+d+f+"; path=/"}
var isStgDebug=(window.location.href.match("stg_debug")||document.cookie.match("stg_debug"))&&!window.location.href.match("stg_disable_debug");stgCreateCookie("stg_debug",isStgDebug?1:"",isStgDebug?14:-1);
var qP=[];dataLayerName!=="dataLayer"&&qP.push("data_layer_name="+dataLayerName),isStgDebug&&qP.push("stg_debug");var qPString=qP.length>0?("?"+qP.join("&")):"";
tags.async=!0,tags.src="https://gencat.containers.piwik.pro/"+id+".js"+qPString,scripts.parentNode.insertBefore(tags,scripts);
!function(a,n,i){a[n]=a[n]||{};for(var c=0;c<i.length;c++)!function(i){a[n][i]=a[n][i]||{},a[n][i].api=a[n][i].api||function(){var a=[].slice.call(arguments,0);"string"==typeof a[0]&&window[dataLayerName].push({event:n+"."+i+":"+a[0],parameters:[].slice.call(arguments,1)})}}(i[c])}(window,"ppms",["tm","cm"]);
})(window, document, 'dataLayer', 'eb50131d-1482-4c39-a0fa-835d5e4dacd6');
</script> 
  <script>
	if(PluginGencat != null){
		PluginGencat.externalurl = "https://atc.gencat.cat";
		PluginGencat.serviceuri = "https://atc.gencat.cat";
	}
</script> 
  <!--fw_geco_head--> 
  <script>!function(e){var n="https://s.go-mpulse.net/boomerang/";if("False"=="True")e.BOOMR_config=e.BOOMR_config||{},e.BOOMR_config.PageParams=e.BOOMR_config.PageParams||{},e.BOOMR_config.PageParams.pci=!0,n="https://s2.go-mpulse.net/boomerang/";if(window.BOOMR_API_key="BM5WD-23TA6-4USBP-SRKUP-9PUHQ",function(){function e(){if(!r){var e=document.createElement("script");e.id="boomr-scr-as",e.src=window.BOOMR.url,e.async=!0,o.appendChild(e),r=!0}}function t(e){r=!0;var n,t,a,i,d=document,O=window;if(window.BOOMR.snippetMethod=e?"if":"i",t=function(e,n){var t=d.createElement("script");t.id=n||"boomr-if-as",t.src=window.BOOMR.url,BOOMR_lstart=(new Date).getTime(),e=e||d.body,e.appendChild(t)},!window.addEventListener&&window.attachEvent&&navigator.userAgent.match(/MSIE [67]\./))return window.BOOMR.snippetMethod="s",void t(o,"boomr-async");a=document.createElement("IFRAME"),a.src="about:blank",a.title="",a.role="presentation",a.loading="eager",i=(a.frameElement||a).style,i.width=0,i.height=0,i.border=0,i.display="none",o.appendChild(a);try{O=a.contentWindow,d=O.document.open()}catch(_){n=document.domain,a.src="javascript:var d=document.open();d.domain='"+n+"';void 0;",O=a.contentWindow,d=O.document.open()}if(n)d._boomrl=function(){this.domain=n,t()},d.write("<bo"+"dy onload='document._boomrl();'>");else if(O._boomrl=function(){t()},O.addEventListener)O.addEventListener("load",O._boomrl,!1);else if(O.attachEvent)O.attachEvent("onload",O._boomrl);d.close()}function a(e){window.BOOMR_onload=e&&e.timeStamp||(new Date).getTime()}if(!window.BOOMR||!window.BOOMR.version&&!window.BOOMR.snippetExecuted){window.BOOMR=window.BOOMR||{},window.BOOMR.snippetStart=(new Date).getTime(),window.BOOMR.snippetExecuted=!0,window.BOOMR.snippetVersion=14,window.BOOMR.url=n+"BM5WD-23TA6-4USBP-SRKUP-9PUHQ";var i=document.currentScript||document.getElementsByTagName("script")[0],o=i.parentNode,r=!1,d=document.createElement("link");if(d.relList&&"function"==typeof d.relList.supports&&d.relList.supports("preload")&&"as"in d)window.BOOMR.snippetMethod="p",d.href=window.BOOMR.url,d.rel="preload",d.as="script",d.addEventListener("load",e),d.addEventListener("error",function(){t(!0)}),setTimeout(function(){if(!r)t(!0)},3e3),BOOMR_lstart=(new Date).getTime(),o.appendChild(d);else t(!1);if(window.addEventListener)window.addEventListener("load",a,!1);else if(window.attachEvent)window.attachEvent("onload",a)}}(),"".length>0)if(e&&"performance"in e&&e.performance&&"function"==typeof e.performance.setResourceTimingBufferSize)e.performance.setResourceTimingBufferSize();!function(){if(BOOMR=e.BOOMR||{},BOOMR.plugins=BOOMR.plugins||{},!BOOMR.plugins.AK){var n=""=="true"?1:0,t="",a="rcigouaxer6lszzllu6q-f-1579d1d11-clientnsv4-s.akamaihd.net",i="false"=="true"?2:1,o={"ak.v":"39","ak.cp":"537041","ak.ai":parseInt("349618",10),"ak.ol":"0","ak.cr":92,"ak.ipv":4,"ak.proto":"http/1.1","ak.rid":"19aa1dc0","ak.r":46154,"ak.a2":n,"ak.m":"b","ak.n":"essl","ak.bpcip":"*************","ak.cport":5002,"ak.gh":"**********","ak.quicv":"","ak.tlsv":"tls1.3","ak.0rtt":"","ak.0rtt.ed":"","ak.csrc":"-","ak.acc":"reno","ak.t":"1730895165","ak.ak":"hOBiQwZUYzCg5VSAfCLimQ==hhMHNGYOMwcs/70kt9mvQeS2B0UlqDP15mGBg7m9QO/07m53w4zmPpFNF6hOCrfwK+7qrbwJffjCZIpTTABE5FKeH34FhjfKVKp/mLe7QrVK3m2QcFOKXWHVq4mAEl+80+XTRg+v0VSdE3Sf9VFFzczbfNGiJHNbYV+eSqx+S85TCTHF0JEw8inJwiptfQ78qfeNJ07S9nIO7/pexFuVtfbfUZpdsxu7RhtdzvoDyFGxmZPmM9+/eGwtHL7Y18KGxU9EZrV+ZMJMJHVSXH1kil+wpaPgYmseTJIM4f7pANp1IifhVy66HrroTkm5Wrz8AJMeEjetl9+SxeCOoqnNec/y9CIwO7hdc+qL91uddcI70a15gUH1hR5IB+qH8/WPgMcFYYWbDDP77VXXDXCDNAkDhNw53hufCscKrnrvk+A=","ak.pv":"33","ak.dpoabenc":"","ak.tf":i};if(""!==t)o["ak.ruds"]=t;var r={i:!1,av:function(n){var t="http.initiator";if(n&&(!n[t]||"spa_hard"===n[t]))o["ak.feo"]=void 0!==e.aFeoApplied?1:0,BOOMR.addVar(o)},rv:function(){var e=["ak.bpcip","ak.cport","ak.cr","ak.csrc","ak.gh","ak.ipv","ak.m","ak.n","ak.ol","ak.proto","ak.quicv","ak.tlsv","ak.0rtt","ak.0rtt.ed","ak.r","ak.acc","ak.t","ak.tf"];BOOMR.removeVar(e)}};BOOMR.plugins.AK={akVars:o,akDNSPreFetchDomain:a,init:function(){if(!r.i){var e=BOOMR.subscribe;e("before_beacon",r.av,null,null),e("onbeacon",r.rv,null,null),r.i=!0}return this},is_complete:function(){return!0}}}}()}(window);</script> 
 </head> 
 <body> 
  <script type="text/javascript">

        
        getBrowserName();
        function getBrowserName() {
            var name = "Unknown";
            if(navigator.userAgent.indexOf("Android")!=-1){
                name = "Android";
            }
            else if(navigator.userAgent.indexOf("Safari")!=-1){
                name = "Safari";
            }
            return name;   
        }
        if( getBrowserName() == "Safari"&& navigator.userAgent.indexOf("5.1")!=-1 ){
            var element = document.getElementsByTagName("body")[0];
            element.classList.add("safari");
            
        }
        if( getBrowserName() == "Android"&& navigator.userAgent.indexOf("4.0")!=-1 ){
            var element = document.getElementsByTagName("body")[0];
            element.classList.add("safari");
            
        }
        
    </script> 
  <div class="wrapper">
    <div class="atc_cos">
      <!-- Webcomponent: Header -->
      <mf-header></mf-header>

      <!-- Webcomponent: Dispatcher -->
      <mf-dispatcher></mf-dispatcher>

      <!-- Webcomponent: Footer -->
      <mf-footer></mf-footer>
    </div>
  </div>
  <!--googleoff: index --> 
  <!--googleoff: snippet--> 
  <div class="NG-container"> 
    <div class="NG-row-flex NG-goToTop__wrapper"> 
     <button class="NG-goToTop js-NG-goToTop" style="display: block;"> <img class="NG-goToTop_image" alt="Torna amunt" src="https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/fpca_peu_seuNou/img/chevron-up-w.svg"> </button> 
    </div> 
   </div> 
  <!--googleon: snippet --> 
  <!--googleon: index--> 
  <!-- GKPSTX04-->  
 </body>
</html>