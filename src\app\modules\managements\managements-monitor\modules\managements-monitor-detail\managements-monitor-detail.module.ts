import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { ManagementsMonitorDetailComponent } from './managements-monitor-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { DetailPageModule } from '../../../../shared/detail-page';

const routes: Routes = [
	{
		path: '', 
    component: ManagementsMonitorDetailComponent,
		data: { 
			title: ''
		}
	}
];

@NgModule({
	declarations: [
		ManagementsMonitorDetailComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		DetailPageModule,
    RouterModule.forChild(routes)
	]
})
export class ManagementsMonitorDetailModule { }
