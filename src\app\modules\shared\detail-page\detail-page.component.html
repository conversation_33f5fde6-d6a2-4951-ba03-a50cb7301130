<div *ngIf="procedureData">
	<div class="position-relative">
		<span class="position-absolute" style="right: 3px; top: 24px">
			<!--Go to previous self-assessment-->
			<pt-button
				_type="button"
				_label="UI_COMPONENTS.BUTTONS.PREVIOUS"
				_class="p-button-outlined"
				_icon="sli2-chevron-left"
				(_clickFn)="goPreviousSelfAssessment()"
				[_disabled]="!currentIndex || currentIndex <= 0"
			>
			</pt-button>
			<!--Go to previous self-assessment-->
			<pt-button
				_type="button"
				_label="UI_COMPONENTS.BUTTONS.NEXT"
				_class="p-button-outlined"
				_iconPos="right"
				_icon="sli2-chevron-right"
				(_clickFn)="goNextSelfAssessment()"
				[_disabled]="
					(!currentIndex && currentIndex !== 0) ||
					currentIndex === totalRows - 1
				"
			>
			</pt-button>
		</span>

		<pt-page-title
			[_title]="pageTitle"
		></pt-page-title>
	</div>

	<div class="d-flex justify-content-start">
		<!--/* Tabs */-->
		<div class="col-8 col-sm-8 col-md-8 col-lg-8">
			<app-detail-card
				[title]="detailCardTitle"
				[detailCardFields]="detailCardFields"
				[formValue]="procedureData"
				[stateIcon]="stateIcon"
				[stateLabel]="stateLabel"
				(showErrors)="onShowErrors($event)"
				(linkClicked)="onLinkClicked($event)"
			>
			</app-detail-card>

			<app-detail-tabs
				[customTabsContent]="customTabsContent"
				[detailTabsData]="{
					idSelfAssessment: idEntity,
					idTramitacio: idTramit,
					tabs: detailTabInput
				}"
			>
			</app-detail-tabs>
		</div>

		<app-detail-state-table
			[stateTranslations]="statesTableTranslation"
			[firstDate]="stateChangeFirstDate"
			[stateChanges]="stateChanges"
		>
		</app-detail-state-table>
	</div>

	<!--/* Buttons */-->
	<div class="d-flex justify-content-between">

		<pt-button
			_label="UI_COMPONENTS.BUTTONS.BACK"
			_type="button"
			_class="p-button-outlined"
			_icon="sli2-chevron-left"
			(_clickFn)="navigateBackwards()"
		>
		</pt-button>

		<div *ngIf="showPresentationButtons || showDeleteButton || customButtonsTemplate" class="d-flex">

			<ng-container *ngTemplateOutlet="customButtonsTemplate"></ng-container>

			<div *ngIf="showPresentationButtons">
				<mf-presentacions
					*axLazyElement
					[input]="donwloadDocumentsWcInput"
				></mf-presentacions>
			</div>

			<pt-button
				*ngIf="showDeleteButton"
				[allowRoles]="deleteAllowRoles"
				[_label]="deleteButtonLabel"
				_icon="sli2-trash"
				_type="button"
				_class="p-button-purple ml-2"
				(_clickFn)="deleteModal()"
			>
			</pt-button>
		</div>
	</div>
</div>
