import { Component, OnInit } from "@angular/core";
import { Router, ActivatedRoute, NavigationExtras } from "@angular/router";
import { DataStorageService } from "pt-ui-components-mf-lib";
import { Observable, Subject } from "rxjs";
import { takeUntil } from "rxjs/operators";
import { AppRoutes, ToolsRoutes, BiometricSignatureRoutes } from "src/app/core/models/config.model";
import { DetailCardData } from "src/app/modules/shared/detail-card";
import { StateChangesTableRow } from "src/app/modules/shared/detail-state-table";
import { TabData } from "src/app/modules/shared/detail-tabs";
import { IBiometricSignature, BIOMETRIC_SIGNATURE_DETAIL_STORAGE, EstatHistorialDTO, States } from "../../models/biometric-signature.model";
import { BiometricSignatureService } from "../../biometric-signature.service";
import { IDetailData, DETAIL_CARD_FORM_DATA, DETAIL_TAB_DATA, DetailCardFields } from "./models/biometric-signature-detail.model";
import { BiometricSignatureTableEndpointsService } from "../biometric-signature-table/services/biometric-signature-table-endpoints.service";
import { BiometricSignatureRequest, BiometricSignatureResponse, TableMode } from "../biometric-signature-table/models";
import { BiometricSignatureDetailEndpointsService } from "./services/biometric-signature-detail-endpoints.service";

@Component({
	selector: 'app-resolucions-detail',
	templateUrl: './biometric-signature-detail.component.html'
})
export class BiometricSignatureDetailComponent implements OnInit {

	// Procedure Data
	biometricSignature: IBiometricSignature;
	idEntity: string;
	idReceipt: string;

	// Detail data
	stateChanges: StateChangesTableRow[] = [];
	detailData: IDetailData;
	stateIcon: string;
	stateLabel: string;
	detailCardFields: DetailCardData[] = [];
	detailTabInput: TabData[] =[];

	// Navegation Data
	currentIndex:number;
	totalRows:number;
	navigateBackUrl: string;
	mode: TableMode;
	navigationExtras: NavigationExtras;

	TableMode = TableMode;

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		private tableEndpointsService: BiometricSignatureTableEndpointsService,
		private router: Router,
		private aRoute: ActivatedRoute,
		private biometricSignatureService: BiometricSignatureService,
		private dataStorageService: DataStorageService,
		private detailEndpointsService: BiometricSignatureDetailEndpointsService
	) {
		this.idEntity = this.aRoute.snapshot.params.id;
	}

	ngOnInit(): void {
		this.setConstantValues();

		this.setDetailData();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();

		this.dataStorageService.deleteItem(BIOMETRIC_SIGNATURE_DETAIL_STORAGE);
	}

	private setConstantValues() {
		this.detailCardFields = DETAIL_CARD_FORM_DATA;
		this.navigateBackUrl = '/'+AppRoutes.TOOLS.concat('/',ToolsRoutes.BIOMETRIC_SIGNATURE,'/',BiometricSignatureRoutes.TABLE);
	}

	private setDetailData(){
		this.detailData = this.dataStorageService.getItem(BIOMETRIC_SIGNATURE_DETAIL_STORAGE) as IDetailData;

		if (this.detailData && this.idEntity === this.detailData.idEntity) {
			this.currentIndex = this.detailData.index
			this.totalRows = this.detailData.totalRows
			this.setCoreValues(this.detailData.row);
			this.mode = this.detailData.mode;
			this.navigationExtras = {queryParams: {tab: this.mode === TableMode.ACTIVES ? '1' : '2'}};
		} else {
			this.dataStorageService.deleteItem(BIOMETRIC_SIGNATURE_DETAIL_STORAGE);
			this.router.navigate([this.navigateBackUrl]);
		}
	}

	private setCoreValues(data: IBiometricSignature) {
		this.idReceipt = data.id;
		this.biometricSignature = data;

		this.stateChanges = this.getStateChanges(data.historial_estats);
		this.detailTabInput = this.setTabData();

		this.stateIcon = data.estat_peticio ? this.biometricSignatureService.getStateIcon(data.estat_peticio) : '';
		this.stateLabel = data.estat_peticio ? `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.estat_peticio.${data.estat_peticio}` : null;
	}

	private getStateChanges(stateChanges: EstatHistorialDTO[]): StateChangesTableRow[] {
		return stateChanges?.map((state, index) => {
			const firstState = index === 0 ? States.GENERAT : stateChanges[index - 1].estat as string;

			return {
				before: firstState,
				current: state.estat as string,
				dateChange: new Date(state.data),
				currentStateIcon: this.biometricSignatureService.getStateIcon(state.estat),
				previousStateIcon: this.biometricSignatureService.getStateIcon(firstState),
			}
		}) || [];
	}

	private setTabData() {
		return DETAIL_TAB_DATA
	}

	getManagementList() {
		this.detailData.options.rows = 1;
		this.detailData.options.first = this.currentIndex;
		this.detailData.index = this.currentIndex;

		const request: BiometricSignatureRequest = new BiometricSignatureRequest(this.detailData.filterValue,this.detailData.options);

		const observable: Observable<BiometricSignatureResponse> = this.detailData.mode === TableMode.ACTIVES ?
			this.tableEndpointsService.getActiveList(request) : this.tableEndpointsService.getHistoricList(request);

		observable.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
				response => {
				const results: IBiometricSignature[] = response?.content?.results;

				if (results.length > 0) {
					this.biometricSignature =  results[0];
					this.detailData.idEntity = this.biometricSignature?.id_repositori_documental;

					this.setCoreValues(this.biometricSignature);

					this.dataStorageService.setItem(BIOMETRIC_SIGNATURE_DETAIL_STORAGE, this.detailData);

					this.router.navigate(['/' + AppRoutes.TOOLS.concat('/',ToolsRoutes.BIOMETRIC_SIGNATURE,'/',BiometricSignatureRoutes.DETAIL), this.biometricSignature?.id]);
				}
		});
	}

	onShowErrors() {
		this.biometricSignatureService.showErrors(this.biometricSignature?.errors)
	}

	onLinkClicked(event: string) {
		if(event === DetailCardFields.errors) {
			this.onShowErrors();
		} else if(event === DetailCardFields.metadades) {
			this.biometricSignatureService.showMetadades(this.biometricSignature?.metadades)
		}
	}

	resetIntents(): void {
		this.detailEndpointsService.resetSignature(this.idReceipt).pipe(
			takeUntil(this._unsubscribe)
		).subscribe();
	}
}
