version: 2.0.1
info:
  version: {project.version}
  description: PTR-PORTAL-TRACKING-GW
global-env:
  - CONTAINER_DOCKERFILE_PATH: Dockerfile
  - CONTAINER_IMAGE_NAME: pt-portal-tracking-gw
  - DEPLOYMENT_TYPE: DeploymentConfig
components:
  - build:
      steps:
        - container:
            image:
              remote:
                name: registreimatges.sic.intranet.gencat.cat/gencat-sic-builders/node-builder:1.0-14
            resources:
              limits:
                cpu: 3000m
                memory: 3072Mi
              requests:
                cpu: 100m
                memory: 128Mi
          execution:
            commands:
              - npm install && npm run build:prod
    deployment:
      scm: https://git.intranet.gencat.cat/4029/orchestrators.git
      environments:
        - name: integration
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: pt-portal-tracking-gw/int
                      - DEPLOYMENT_NAME: pt-portal-tracking-gw
                      - DEPLOYMENT_WAIT: 1200
        - name: preproduction
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: pt-portal-tracking-gw/pre
                      - DEPLOYMENT_NAME: pt-portal-tracking-gw
                      - DEPLOYMENT_WAIT: 1200
        - name: production
          actions:
            deploy:
              steps:
                - execution:
                    env:
                      - DESCRIPTORS_PATH: pt-portal-tracking-gw/pro
                      - DEPLOYMENT_NAME: pt-portal-tracking-gw
                      - DEPLOYMENT_WAIT: 1200
notifications:
  email:
    recipients: []
