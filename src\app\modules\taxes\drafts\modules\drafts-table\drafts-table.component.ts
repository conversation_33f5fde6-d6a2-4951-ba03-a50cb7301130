import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { LazyLoadEvent } from 'primeng/api';
import {
	PtTableColumn, PtValidations, iCommonError
} from 'pt-ui-components-mf-lib';
import { DraftsEndpointsService } from '../../services/drafts-endpoints.service';

import { DraftsService } from '../../services/drafts.service';
import { DatePipe } from '@angular/common';
import { DraftsRequest, DraftsRow, IDraftsFilter, IDrafts, DRAFTS_FILTER_STORAGE, DRAFTS_DETAIL_STORAGE } from '../../models/drafts.model';
import { DraftsResponse, DraftsResponseContent } from '../../models/drafts-endpoints.model';
import { AppRoutes, DraftsRoutes, StatesIcons, TaxesRoutes } from 'src/app/core/models/config.model';
import { IDetailData } from '../drafts-detail/models/drafts-detail.model';
import { FilterModalInput } from '../../../../shared/modal-filter/models/modal-filter.model';
import { EXPORT_COLUMNS_DATA, FILTER_FORM_DATA, FilterFormFields, FilterFormValue, TableColumns, TableSortFields } from './models/drafts-table.model';
import { ModalFilterComponent } from '../../../../shared/modal-filter';
import { TableExportService } from 'src/app/core/services/table-export.service';
import { PtTableComponent } from 'pt-ui-components-mf-lib/lib/components/table/table.component';

@Component({
	selector: 'app-drafts-table',
	templateUrl: './drafts-table.component.html'
})
export class DraftsTableComponent implements OnInit {
	
	@ViewChild('dt', { static: true }) private readonly table: PtTableComponent;

	filterValue: Partial<IDraftsFilter>;

	// Table
	searchColumns: PtTableColumn[] = [];
	searchRows: DraftsRow[] = [];

	// Other
	options: LazyLoadEvent;
	paginationTotal: number = 0;

	private _unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private endpointsService: DraftsEndpointsService,
		private router: Router,
		private draftsService: DraftsService,
		private exportService: TableExportService
	) { }

	ngOnInit(): void {
		this.searchColumns = this.setTableColumns();
		this.filterValue = this.setFilterValue();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	private setTableColumns = (): PtTableColumn[] => [
		{
			id: TableColumns.stateRow, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.estat",
			isResizable: true, isSortable: true, template: 'link', width: "14%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.model, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.model",
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.nifPresentador, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nifPresentador",
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.nomPresentador, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nomPresentador",
			isResizable: true, isSortable: true, width: "17%"
		},
		{
			id: TableColumns.nifTitular, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nifTitular",
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.nomTitular, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nomTitular",
			isResizable: true, isSortable: true, isToggled: true, width: "17%"
		},
		{
			id: TableColumns.nifSubjectePassiu, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nifSubjectePassiu",
			isResizable: true, isSortable: false, width: "10%"
		},
		{
			id: TableColumns.nomSubjectePassiu, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.nomSubjectePassiu",
			isResizable: true, isSortable: false, width: "17%"
		},
		{
			id: TableColumns.indErrEsborrany, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.indErrEsborrany",
			isResizable: true, isSortable: true, template:'icon', width: "17%"
		},
		{
			id: TableColumns.indEsborrany, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.indEsborrany",
			isResizable: true, isSortable: true, template:'icon', width: "17%"
		},
		{
			id: TableColumns.indCopia, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.indCopia",
			isResizable: true, isSortable: true, template:'icon', width: "17%"
		},
		// {
		// 	id: TableColumns.esborrany, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.esborrany",
		// 	isResizable: true, isSortable: true, template: 'boolean', width: "14%"
		// },
		{
			id: TableColumns.dataModificacio, label: "MODULE_TRACKING.MODULE_DRAFTS.TABLE.COLUMNS.dataAlta",
			isResizable: true, isSortable: true, width: "15%", template: "date",
			options: { dateFormat: 'dd/MM/yyyy HH:mm' }
		},
		{
			id: TableColumns.tableActions, label: null, isResizable: false, isSortable: false,
			template: "buttons", width: '4%'
		}
	];

	private setFilterValue():Partial<IDraftsFilter> {
		const data: IDraftsFilter = this.draftsService.dataStorageService.getItem(DRAFTS_FILTER_STORAGE);

		return new FilterFormValue(data);
	}
	
	openFilterModal() {
		// Open delete modal
		const modalRef = this.draftsService.modalService.open(
			ModalFilterComponent,
			{ size: 'xl', windowClass: '' }
		);
		
		const modalInput: FilterModalInput = this.setModalInput();
		modalRef.componentInstance.modalInput = modalInput;

		modalRef.componentInstance.modalOutput.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(filterFormValue: IDraftsFilter) => {
				this.filterValue = filterFormValue;

				this.table.table.reset();
			}
		);
	}
	
	private setModalInput (): FilterModalInput {
		return {
			formValue: this.filterValue,
			formResetValue: new FilterFormValue(),
			formFieldsData: FILTER_FORM_DATA,
			formGroupValidations: [
				PtValidations.equalsOrLessThan(FilterFormFields.dateToForm, FilterFormFields.dateFromForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan'),
				PtValidations.equalsOrGreaterThan(FilterFormFields.dateFromForm, FilterFormFields.dateToForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan'),
			]
		}
	}

	/**
	 * Search
	 * @description Search resolucions by criteria
	 */
	search = (event: LazyLoadEvent): void => {
		// Reset previous data
		this.searchRows = [];
		this.paginationTotal = 0;
		
		this.setOptionsEvent(event);
		
		this.draftsService.dataStorageService.setItem(DRAFTS_FILTER_STORAGE, this.filterValue);

		const request: DraftsRequest = new DraftsRequest(this.filterValue, event)
		this.endpointsService.getDraftsList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: DraftsResponse) => {
				if (response?.content) {
					this.setTableRows(response.content);
				}
			}
		);
	}
	
	private setOptionsEvent(event: LazyLoadEvent) {
		// Request
		if (!event.sortField){
			event.sortField = TableColumns.dataModificacio;
			event.sortOrder = -1;
		}

		if (TableSortFields[event.sortField]){
			event.sortField = TableSortFields[event.sortField];
		}

		this.options = event;
	}

	/**
	 * Table rows
	 * @description Map the table rows
	 */
	private setTableRows(response: DraftsResponseContent) {
		this.paginationTotal = response.total;

		this.searchRows = response.results?.map(
			(autoliquidacio: IDrafts, id: number) => {
				const stateIcon = this.draftsService.getStateIcon(
					autoliquidacio?.estat,
					'self-assessment'
				);

				// Map autoliquidacio entity into the autoliquidacio table row
				return Object.assign(
					{},
					// Default autoliquidacio entity
					autoliquidacio,
					// Table row model
					{
						[TableColumns.nifSubjectePassiu]: autoliquidacio?.subjectesPassius?.[0]?.nif,
						[TableColumns.nomSubjectePassiu]:	autoliquidacio?.subjectesPassius?.[0]?.nom,
						[TableColumns.indErrEsborrany]:	{
							id: `button${autoliquidacio.id}`,
							icon: autoliquidacio?.indErrEsborrany ? StatesIcons.ERROR : '',
							iconPos: 'left',
						},
						[TableColumns.indEsborrany]:	{
							id: `button${autoliquidacio.id}`,
							icon: autoliquidacio?.indEsborrany ? StatesIcons.SUCCESS : StatesIcons.CLOSE_INFO,
							iconPos: 'left',
						},
						[TableColumns.indCopia]:	{
							id: `button${autoliquidacio.id}`,
							icon: autoliquidacio?.indCopia ? StatesIcons.SUCCESS : StatesIcons.CLOSE_INFO,
							iconPos: 'left',
						},
						[TableColumns.stateRow]: {
							id: `button${autoliquidacio.id}`,
							icon: stateIcon,
							iconPos: 'left',
							label: autoliquidacio.estat
								? this.draftsService.translateService.instant(
										`MODULE_TRACKING.MODULE_DRAFTS.STATES.${autoliquidacio.estat}`
									)
								: '',
							...(autoliquidacio?.errors.length && stateIcon === StatesIcons.ERROR && {
								clickFn: this.showDetailError.bind(
									this,
									autoliquidacio?.errors
								)}
							),
							disabled: !this.isStateError(stateIcon) || !autoliquidacio?.errors.length,
						},
						[TableColumns.tableActions]: [
							{
								id: `detail${autoliquidacio.id}`,
								icon: 'sli2-eye',
								class: 'p-button-text',
								componentType: 'button',
								clickFn: this.navigateToDetail.bind(
									this,
									autoliquidacio,
									id
								),
							},
						],
					}
				);
			}
		);
	}

	private showDetailError = (errors?: iCommonError[]) => this.draftsService.showErrors(errors);

	private isStateError = (icon: string): boolean => icon === StatesIcons.ERROR;

	private navigateToDetail (row: DraftsRow,id: number) {
		const detailData: IDetailData = {
			filterValue : this.filterValue,
			totalRows: this.paginationTotal,
			options: this.options,
			index: id + this.options?.first,
			idSelfAssessment: row?.id
		}

		this.draftsService.dataStorageService.setItem(DRAFTS_DETAIL_STORAGE, detailData);

		this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.DRAFTS,'/',DraftsRoutes.DETAIL), row.id]);
	}

	export = () : void =>  {
		const options = {...this.options,first:0, rows: 500}
		const request: DraftsRequest = new DraftsRequest(this.filterValue, options)
		this.endpointsService.getDraftsList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: DraftsResponse) => {
				if (response?.content) {
					var datePipe = new DatePipe('en-US');
					let date = datePipe.transform(new Date(), 'yyyy-MM-dd_HH:mm');
					this.exportService.export2Excel(response?.content.results, EXPORT_COLUMNS_DATA, "Export_"+ date, 'Tributs');
				}
			}
		);
	}
}
