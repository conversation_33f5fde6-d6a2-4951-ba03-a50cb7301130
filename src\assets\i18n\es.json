{"MODULE_TRACKING": {"XLSX_ERROR": "No se puede generar el excel en estos momentos, inténtalo más tarde.", "ROLES": {"ADMIN": "Administrador", "TECHNICIAN": "Técnico", "TECHUSER": "Técnico", "READ_ONLY": "<PERSON><PERSON><PERSON>", "CIUTADA": "Ciudadano", "INSPECTOR": "Inspector"}, "CERTIFICATES": {"0": "Persona física", "1": "Persona jurídica", "2": "Componente SSL", "3": "Sede electrónica", "4": "Sello electrónico", "5": "Empleado público", "6": "Empleado personalidad jurídica", "7": "Pseudónimo empleado público", "8": "<PERSON><PERSON> cualificado", "9": "Autentificación cualificada", "10": "Certificado sello", "11": "Representante de persona jurídica", "12": "Representante entidad jurídica"}, "LOGIN_METHODS": {"CERTIFICAT": "Certificado", "IDCATMOBIL": "idCAT Mòbil", "GICAR": "Gicar", "CLAVE": "Cl@ve"}, "NAVBAR": {"LAST_SEEN": "Última conexión: ", "ROLE": "Rol", "SELF_ASSESSMENT": "Autoliquidaciones (Antiguas)", "SELF_ASSESSMENT_V2": "Nuevos tributos", "DRAFTS": "Sesiones de trabajo", "USER": "Usuarios", "PAYMENT_SETTLEMENT": "Liquidacions", "TEARC_MONITOR": "Resoluciones", "SIMULATED_LOGIN": "<PERSON>gin simulado", "MANAGEMENT": "Gestiones", "REFUND": "Devolución de ingresos indebidos", "NOTIFICATIONS": "Configuración de avisos", "SARCAT": "S@RCAT", "AUTOLIQUIDACIONS": "Autoliquidaciones", "GESTIONS": "Gestiones", "PAGAMENTS": "Pagos", "NIF_ENCRYPTION": "EncriptacióN nif", "TAXES": "Tributos", "USERS": "Usuarios", "MANAGEMENTS_MONITOR": "Gestiones", "BIOMETRIC_SIGNATURE": "Firma Biométrica", "TOOLS": "Herramientas", "AREA_PRIVADA": "Mi espacio ATC", "MAINTENANCE_CONF_SEU2": "Configuración mantenimiento SEU2", "MAINTENANCE_CONF_SEU": "Configuración mantenimiento SEU", "EXPEDIENTES": "Expedientes AEAT"}, "PAYMENT_METHODS": {"BANK_ACCOUNT": "Cargo a cuenta", "CREDIT_CARD": "Tarjeta", "BIZUM": "Bizum"}, "TYPES": {"DECINF": "DECINF", "TRIBUT": "TRIBUT"}, "STATES": {"HEADER": "Estado", "RECIVED": "Recibido", "ERROR": "Error", "GENERATED": "Generado", "DRAFT": "<PERSON><PERSON><PERSON>", "DRAFT_REVALIDATE": "Re-validar", "DRAFT_VALIDATING": "Validando borrador", "DRAFT_VALIDATED": "<PERSON><PERSON><PERSON> validado", "DRAFT_ERROR": "<PERSON><PERSON><PERSON> con errores", "DRAFT_GROUPING": "<PERSON><PERSON><PERSON> a<PERSON>", "DRAFT_GROUPING_ERROR": "Error agrupación", "DRAFT_GROUPED": "<PERSON><PERSON><PERSON>", "RECEIVED": "Recibido", "VALIDATING": "Validando", "VALIDATED": "Validado", "VALIDATING_ERROR": "Error validación", "PAYING": "Pa<PERSON>do", "PAYING_ERROR": "Error pago", "CONFIRMED": "<PERSON><PERSON><PERSON><PERSON>", "CONFIRMED_ERROR": "Error confirmación", "NOTIFIED_ERROR": "Error notificación", "NOTIFYING": "Notificando pago", "PAID": "<PERSON><PERSON>", "CONSOLIDATING": "Consolidando", "CONSOLIDATING_ERROR": "Error de consolidación", "CONSOLIDATED": "Consolidado", "PRESENTING": "<PERSON><PERSON><PERSON>", "PRESENTING_ERROR": "Error de presentación", "PROCESSING_ERROR": "<PERSON><PERSON>, error de presentación", "PRESENTED": "<PERSON>ado", "NO_PRESENTED": "No presentado", "PROCESSING": "<PERSON><PERSON><PERSON>", "PROCESSED": "<PERSON><PERSON> y presentado", "TRAMITATED": "Consolidado", "TRAMITATED_ERROR": "Error de consolidación", "TRAMITATING": "Consolidando"}, "WARNING_MSG": {"TITLE": "No es posible eliminar la autoliquidación", "SUBTITLE": "Las autoliquidaciones con el estado {{state}} no pueden ser eliminadas"}, "COMPONENT_LIQUIDACIO": {"PAGE_TITLE": "Datos Autoliquidación", "BUTTONS": {"DETAIL": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "EXCEL": "Lista de autoliquidaciones", "PAYMENT_DETAIL": "Ver datos del pago", "RECORD_DETAIL": "Ver datos del expediente", "RETRY_PAYMENT": "Reintentar pago", "RETRY_PRESENTATION": "Reintentar presentación", "RETRY_NOTIFICATION": "Reintentar notificación", "DELETE_SELF_ASSESSMENT": "Eliminar autoliquidación", "CLEAR_SELF_ASSESSMENT_ERRORS": "Limpiar errores autoliquidación", "ACTION1": "Notificar Pago GT", "ACTION2": "Ver documento padre", "ACTION3": "Ver documento de diligencia", "ACTION4": "Ver documento de notificación", "OPTIONS": "Opciones", "MODAL_DELETE_SELF_ASSESSMENT": "Eliminarás esta autoliquidación"}, "CONFIRMATION_MESSAGES": {"DELETE_SELF_ASSESSMENT_RECEIPT": "¿Estás seguro de eliminar la autoliquidación con nº Justificante {{numJustificant}}?", "DELETE_SELF_ASSESSMENT_NO_RECEIPT": "¿Estás seguro de eliminar la autoliquidación?", "CLEAR_SELF_ASSESSMENT_ERRORS": "¿Estás seguro de limpiar los errores de la autoliquidación con nº Justificante {{numJustificant}}?"}, "TABLE": {"TRUE": "Sí", "FALSE": "No", "ORIGIN": "Origen", "COMUNICATION_ID": "ID Comunicación", "RECEIPT": "<PERSON><PERSON><PERSON>", "AMOUNT": "Importe", "PAYMENT_METHOD": "Método de pago", "FILE": "<PERSON><PERSON><PERSON>", "CURRENT_STATE": "Estado Actual", "PRES_STATE": "Estado Presentación", "PRESENTATOR_NAME": "Nombre Presentador", "PRESENTATOR_NIF": "NIF Presentador", "TITULAR_NAME": "Nom Titular", "TITULAR_NIF": "NIF Titular", "SP_NAME": "Nombre SP", "SP_NIF": "NIF SP", "PROTOCOL": "Protocolo", "NOTARY_NAME": "Nombre notario", "MFPT_ID": "Id MFPT", "MODEL": "<PERSON><PERSON>", "COMPLEMENTARY": "Complementaria", "DATE": "<PERSON><PERSON>", "MUI_REFERENCE": "Referencia MUI", "DETAIL": "Detalle", "PAYMENT_DETAIL": "Pago", "RECORD_DETAIL": "Expediente", "MUI": "MUI", "ID_TRAMITACIO": "ID Tramitación", "ACTIONS": "Acciones"}, "DETAIL_MODAL": {"FIELDS": {"idSelfAssessment": "ID Autoliquidación", "amount": "Importe", "loadDate": "<PERSON><PERSON>", "idReceipt": "<PERSON><PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON>", "state": "Estado", "idTramitacio": "ID Tramitación", "notarialFileReference": "Número referencia"}, "TITLE": "Detalle de la autoliquidación", "MODEL_ID": "ID modelo", "ORIGIN": "Origen", "PRESENTATION_REQUEST_ID": "ID Petición presentación", "ERRORS": "Errores", "STATE_CHANGES": "Historial de estados", "DOCUMENTS": "Documentos", "CHECK_DECLARATION": "Ver declaración", "CLOSE": "<PERSON><PERSON><PERSON>", "FILTER": "Filtro", "NAVIGATION_TITLE": "Autoliquidación: {{idAutoliquidacio}}", "PAYMENT_DETAIL": "Pagos", "TAB_TITLE_PRESENTATIONS": "Presentaciones", "TAB_TITLE_VALIDATIONS": "Validaciones", "TAB_TITLE_NOTIFICATIONS": "Notificaciones", "TAB_TITLE_GROUPING": "Agrupaciones", "TAB_TITLE_TRAMITACIONS": "Tramitaciones"}, "DETAIL_MODAL_TAB": {"DATE": "<PERSON><PERSON>", "DESCRIPTION": "Descripción", "DOC_MANAGER_REF": "Referencia Gestor Documental", "PRESENTATION_REQUEST": "Petición de presentación", "DECLARATION": "Declaración", "BEFORE": "Estado anterior", "CURRENT": "Estado actual"}, "SECTION_SEARCH": {"SECTION_TITLE": "Filtros de búsqueda", "FORM": {"FIELDS": {"loadDateFrom": "<PERSON><PERSON> inicial", "loadDateTo": "Fecha final", "nivel": "<PERSON><PERSON>", "modulo": "<PERSON><PERSON><PERSON><PERSON>", "origen": "Origen", "state": "Estado actual", "idReceipt": "<PERSON><PERSON><PERSON>", "idC": "ID Comunicación", "idP": "ID Petición", "nifSP": "NIF SP", "nifPresenter": "NIF Presentador", "nifTitular": "NIF Titular", "notario": "Nombre notario", "protocolo": "Protocolo", "model": "<PERSON><PERSON>", "errores": "<PERSON><PERSON><PERSON>", "paymentMethod": "Método de pago", "SPRIU": "Sólo pendientes de e-SPRIU", "clauCobramentMUIFilter": "Clave cobro MUI", "presentationState": "Estado Presentación", "idTramitacio": "ID Tramitación", "idPRPT": "ID Agrupación", "optionsFilter": "Opciones", "complementedNumJustificant": "Es complementaria"}}}, "SECTION_RESULTS": {"SECTION_TITLE": "Resultados de la búsqueda", "MODAL_SUBTITLE": "ID autoliquidación: {{idLiquidacion}}", "MODAL1_TITLE": "Detalles del pago", "MODAL1_SUBTITLE": "Datos del pago", "MODAL2_TITLE": "Detalles del expediente", "MODAL2_SUBTITLE1": "Datos del expediente", "MODAL2_SUBTITLE2": "Datos de la notificación", "COL1": "NRC", "COL2": "<PERSON><PERSON>", "COL3": "<PERSON><PERSON>", "COL4": "PRPTID GT", "COL5": "Num Expediente", "COL6": "CSV", "COL7": "Fecha de Presentación", "COL8": "MUI", "COL9": "Fecha de Notificación", "COL10": "URL de Notificación", "COL11": "Referencia Gestor Documental", "COL12": "Reintentos"}}, "COMPONENT_GESTIONS": {"PAGE_TITLE": "Gestiones", "PAYMENTS_PAGE_TITLE": "<PERSON><PERSON>", "PAYMENTS_DETAIL_TITLE": "Detalle del pago", "COMPONENT_NIF_ENCRYPTION": {"PAGE_TITLE": "Encriptación de nif", "NIF": "NIF", "ENCRYPTED_NIF": "NIF encriptado", "BUTTONS": {"ENCRYPT": "Encriptar", "DECRYPT": "Desencriptar"}}, "COMPONENT_MAINTENANCE_CONF": {"PAGE_TITLE": "Configuración mantenimiento", "BUTTONS": {"CREATE": "Nueva configuración", "UPDATE": "Modificar configuración", "DELETE": "<PERSON><PERSON><PERSON><PERSON> configuraci<PERSON>"}, "STATES": {"ACTIVE": "Activo", "IN_MAINTENANCE": "En mantenimiento", "SCHEDULED_MAINTENANCE": "Mantenimiento programado"}, "TABLE": {"COLUMNS": {"application": "Funcionalidad", "stateRow": "Estado", "maintenanceStart": "Fecha de inicio", "maintenanceEnd": "<PERSON><PERSON> de fin", "nouCapcalera": "Nueva cabecera"}}, "CONTEXT_OPTIONS": {"EL_MEU_ESPAI_ATC": "Mi espacio ATC", "CITA_PREVIA": "Cita previa", "ESTADES_TURISTIQUES": "IEET - Estancias turísticas (940, 950, 920)", "TRANSMISSIONS_PATRIMONIALS": "ITP - Borrador de transmisiones patrimoniales (600)", "SIGNATURA_INSPECCIO": "Firma de inspección", "APORTAR_DOCUMENTACIO": "Aportar documentación", "DEVOLUCIO_INGRESSOS": "Devolución de ingresos indebidos", "PAGAMENT_LIQUIDACIONS": "Pago de liquidaciones", "RECURS_REPOSICIO": "Recursos de reposición", "DADES_CONTACTE": "Datos de contacto", "RECURS": "Recurso", "ANTENES": "IIIMA - Instalaciones Medio Ambiente (550, 560)", "CSV": "Consulta CSV", "ITPAJ": "ITP - Transmisiones Patrimoniales (600)", "IGEC": "IGEC - Grandes Establecimientos (910)", "ISBEE": "IBEE - <PERSON>bid<PERSON> (520)", "IANP": "IANP - Activos No Productivos (540)", "GRANS_ESTABLIMENTS": "IGEC - Grandes Establecimientos (910)", "SEGURETAT": "Seguridad", "ACTIUS_NP": "IANP - Activos No Productivos (540)", "ASSEGURANCES": "ISD - Seguros de Vida (652)", "SIMULADORS": "Simulad<PERSON>", "DONACIONS": "ISD - Donaciones (651)", "BEGUDES_ENSUCRADES": "IBEE - <PERSON>bid<PERSON> (520)", "PADRO_CO2": "IEDC - Emisiones CO2 vehículos", "APOSTES": "JOC - Apuestas (042)", "REA": "Reclamaciones económico-administrativas", "ALLEGACIONS": "Alegaciones", "APP": "Aplicación móvil para el contribuyente del ATC", "GASOS": "IEGI - Emisión Gases Industria (980)", "AVIACIO": "IENA - Emisión Gases Aviación (990)", "SIGNATURA_INSPECCIO_V2": "Firma de inspección", "DECINF": "DECINF - Declaraciones Informativas"}, "MODAL": {"MODES": {"FILTER": "Filtros de busqueda", "CREATE": "<PERSON><PERSON><PERSON> configu<PERSON>", "UPDATE": "<PERSON><PERSON><PERSON><PERSON> configurac<PERSON>", "DELETE": "<PERSON><PERSON><PERSON><PERSON> configuraci<PERSON>"}, "DELETE_SUBTITLE": "¿Está seguro de que desea borrar el elemento seleccionado?"}}, "COMPONENT_CASES": {"LIST": {"PAGE_TITLE": "Expedientes"}, "DETAIL": {"PAGE_TITLE": "Detalle del expediente"}}, "COMPONENT_SARCAR": {"PAGE_TITLE": "S@RCAT"}}, "COMPONENT_SIMULATED_LOGIN": {"PAGE_TITLE": "<PERSON>gin simulado", "FIELDS": {"METHOD": "Tip<PERSON> de login", "SIMULATION_DATE": "Simulación de la fecha del sistema", "CERTIFICATE": "Tipo de certificado", "USER_NIF": "NIF del usuario", "COMPANY_NIF": "NIF de la compañía"}}, "COMPONENT_AREA_PRIVADA": {"PAGE_TITLE": "Mi espacio ATC", "COMPONENT_AUTOLIQUIDACIONS": {"PAGE_TITLE": "Autoliquidaciones Mi espacio ATC"}, "COMPONENT_GESTIONS": {"PAGE_TITLE": "Gestiones El meu espai ATC"}, "COMPONENT_PAGAMENTS": {"PAGE_TITLE": "Pagos El meu espai ATC"}, "COMPONENT_NOTIFICATIONS": {"PAGE_TITLE": "Configuración de avisos", "NOTIFICATION_OPTIONS": {"ERROR": "Error", "WARNING": "Advertencia", "INFO": "Info", "SUCCESS": "Éxito", "CAD": "<PERSON><PERSON><PERSON><PERSON>"}, "CONTEXT_OPTIONS": {"AREA_PRIVADA": "Mi espacio ATC"}, "STATE_OPTIONS": {"ACTIVE": "Activo", "INACTIVE": "Inactivo"}, "NOTIFICATION_MODAL": {"FILTER": "Filtro de búsqueda", "NEW": "Nuevo aviso", "EDIT": "Editar aviso", "VALIDATIONS": {"LESS_EQUAL": "El campo a validar debe ser inferior o igual al campo Fecha de fin.", "GREATER_EQUAL": "El campo a validar debe ser mayor o igual al campo Fecha de inicio."}}, "DELETE_MODAL": {"TITLE": "Eliminar", "MSG": "¿Estás seguro de que desea eliminar el elemento seleccionado?"}, "TABLE": {"NEW_NOTIFICATION": "Nuevo aviso", "COLUMNS": {"NOTIFICATION_TYPE": "Tipo de aviso", "TITLE_CA": "Título en catalán", "TITLE_ES": "Título en español", "MSG_CA": "Mensaje en catalán", "MSG_ES": "Mensaje en español", "CONTEXT": "Contexto", "INITIAL_DATE": "Fecha de inicio", "FINAL_DATE": "<PERSON><PERSON> de fin", "STATE": "Estado"}}}}, "MODULE_SELFASSESSMENT_V2": {"PAGE_TITLE": "<PERSON><PERSON>", "STATES": {"GENERAT": "Generado", "NO_PRESENTAT": "No presentado", "PRESENTANT": "<PERSON><PERSON><PERSON>", "PRESENTACIO_ERROR": "<PERSON>rror Presentación", "PRESENTAT": "<PERSON>ado", "ESBORRANY": "<PERSON><PERSON><PERSON>", "ESBORRANY_ERROR": "<PERSON><PERSON><PERSON>", "ESBORRANY_VALIDAT": "<PERSON><PERSON><PERSON> validado", "ESBORRANY_VALIDANT": "Validando borrador", "ESBORRANY_AGRUPANT": "Agrupando borrador", "ESBORRANY_AGRUPAT": "<PERSON><PERSON><PERSON>", "PAGAT": "Presentado y pagado", "PAGANT": "Pa<PERSON>do", "PENDENT_PAGAMENT": "Pago <PERSON>", "PAGAMENT_ERROR": "<PERSON><PERSON><PERSON>", "PAGAMENT_CANCELLAT": "Pago cancelado", "NOTIFICANT_PAGAMENT": "Notificando pago", "NOTIFICACIO_ERROR": "Error notificación", "PAGAMENT_NOTIFICAT": "Pago notificado", "TRAMITAT": "Tramitado", "TRAMITANT": "Tramitando", "TRAMITACIO_ERROR": "Error Tramitación", "CONSOLIDANT": "Consolidando", "CONSOLIDAT": "Consolidado", "CONSOLIDACIO_ERROR": "Error consolidación", "ERROR": "Error", "PENDENT_PRESENTACIO": "Pendiente presentación", "ESBORRANY_AGRUPANT_ERROR": "<PERSON><PERSON><PERSON> a<PERSON> error", "DELETE": "<PERSON><PERSON><PERSON>"}, "TABLE": {"EMPTY_MSG": "Ningún resultado coincide con sus criterios de búsqueda. Vuelva a intentarlo.", "COLUMNS": {"numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "estat": "Estado", "impost": "Impuesto", "tipus": "Tipo", "nomPresentador": "Nom Presentador", "nifPresentador": "NIF Presentador", "nomSubjectePassiu": "Nom SP", "nifSubjectePassiu": "NIF SP", "nomTitular": "Nom titular", "nifTitular": "NIF titular", "model": "<PERSON><PERSON>", "dataAlta": "<PERSON><PERSON>", "mui": "Referencia ESPRIU", "pagamentEstat": "Estado pago", "presentacioEstat": "Estado presentación", "idTramitacioPagament": "ID pago", "idTramitacio": "ID presentación", "totalIngressar": "Importe", "idAgrupacio": "ID agrupación"}, "FILTER": {"TITLE": "Filtros de búsqueda", "FIELDS": {"estat": "Estado", "tipus": "Tipo", "impost": "Impuesto", "nifPresentador": "NIF Presentador", "nifSubjectePassiu": "NIF SP", "nifTitular": "NIF Titular", "numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON>", "dateFromForm": "<PERSON><PERSON> inicio", "dateToForm": "Fecha final", "pagamentEstat": "Estado de pago", "presentacioEstat": "Estado de presentación", "idTramitacioPagament": "ID tramitación de pago", "idTramitacio": "ID tramitación de presentación", "errorsFilter": "Opciones", "errors": "<PERSON><PERSON><PERSON>", "haveComplementari": "Es complementaria", "tipusPagament": "Método de pago", "idAgrupacio": "ID agrupación", "mui": "Referencia cobro E-SPRIU", "types": "Tipos"}}}, "DETAIL": {"PAGE_TITLE": "Detalle del tributo", "CARD": {"TITLE": "Datos del tributo", "FIELDS": {"estat": "Estado", "impost": "Impuesto", "tipus": "Tipo", "nomPresentador": "Nom Presentador", "nifPresentador": "NIF Presentador", "nomSubjectePassiu": "Nom SP", "nifSubjectePassiu": "NIF SP", "model": "<PERSON><PERSON>", "dataAlta": "<PERSON><PERSON>", "dataModificacio": "Fecha Modificación", "idAutoliquidacio": "ID Autoliquidación", "numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "periode": "Periodo", "exercici": "Exercicio", "idAgrupacio": "ID agrupación"}}, "STATE_HISTORY_TABLE": {"TITLE": "Historial de estados", "COLUMNS": {"date": "<PERSON><PERSON>", "state": "Estado"}}, "TABS": {"DOCUMENTS": "Documentos", "PAYMENTS": "Pagaments", "PRESENTATIONS": "Presentacions", "TRAMITACIONS": "Tramitacions", "VALIDATIONS": "Validaciones", "NOTIFICATIONS": "Notificaciones", "GROUPING": "Agrupaciones", "ERRORS": "Errores", "INTERVENERS": "Intervinientes", "COMPLEMENTARY": "Complementaria"}}}, "MODULE_DRAFTS": {"PAGE_TITLE": "Datos sesión de trabajo", "ALERT": {"COPY_TITLE": "Se ha realizado una copia de la sesión de trabajo correctame", "DELETE_TITLE": "Se ha borrado la copia de la sesión de trabajo correctamente"}, "STATES": {"GENERAT": "Generado", "NO_PRESENTAT": "No presentado", "PRESENTANT": "<PERSON><PERSON><PERSON>", "PRESENTACIO_ERROR": "<PERSON>rror Presentación", "PRESENTAT": "<PERSON>ado", "ESBORRANY": "<PERSON><PERSON><PERSON>", "ESBORRANY_ERROR": "<PERSON><PERSON><PERSON>", "ESBORRANY_VALIDAT": "<PERSON><PERSON><PERSON> validado", "ESBORRANY_VALIDANT": "Validando borrador", "ESBORRANY_AGRUPANT": "Agrupando borrador", "ESBORRANY_AGRUPAT": "<PERSON><PERSON><PERSON>", "PAGAT": "Presentado y pagado", "PAGANT": "Pa<PERSON>do", "PENDENT_PAGAMENT": "Pago <PERSON>", "PAGAMENT_ERROR": "<PERSON><PERSON><PERSON>", "PAGAMENT_CANCELLAT": "Pago cancelado", "NOTIFICANT_PAGAMENT": "Notificando pago", "NOTIFICACIO_ERROR": "Error notificación", "PAGAMENT_NOTIFICAT": "Pago notificado", "TRAMITAT": "Tramitado", "TRAMITANT": "Tramitando", "TRAMITACIO_ERROR": "Error Tramitación", "CONSOLIDANT": "Consolidando", "CONSOLIDAT": "Consolidado", "CONSOLIDACIO_ERROR": "Error consolidación", "ERROR": "Error", "PENDENT_PRESENTACIO": "Pendiente presentación", "ESBORRANY_AGRUPANT_ERROR": "<PERSON><PERSON><PERSON> a<PERSON> error", "DELETE": "<PERSON><PERSON><PERSON>"}, "TABLE": {"EMPTY_MSG": "Ningún resultado coincide con sus criterios de búsqueda. Vuelva a intentarlo.", "COLUMNS": {"id": "ID", "esborrany": "<PERSON><PERSON><PERSON>", "numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "estat": "Estado", "impost": "Impuesto", "tipus": "Tipo", "nomPresentador": "Nom Presentador", "nifPresentador": "NIF Presentador", "nomSubjectePassiu": "Nom SP", "nifSubjectePassiu": "NIF SP", "nomTitular": "Nom titular", "nifTitular": "NIF titular", "model": "<PERSON><PERSON>", "dataAlta": "<PERSON><PERSON>", "pagamentEstat": "Estado pago", "presentacioEstat": "Estado presentación", "idTramitacioPagament": "ID pago", "idTramitacio": "ID presentación", "totalIngressar": "Importe", "indEsborrany": "<PERSON><PERSON><PERSON>", "indErrEsborrany": "Error", "indCopia": "Copia"}, "FILTER": {"TITLE": "Filtros de búsqueda", "FIELDS": {"indCopia": "Solo copias", "esborrany": "<PERSON><PERSON><PERSON>", "esborranyLabel": "Opciones", "estat": "Estado", "tipus": "Tipo", "impost": "Impuesto", "nifPresentador": "NIF Presentador", "nifSubjectePassiu": "NIF SP", "nifTitular": "NIF Titular", "numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "model": "<PERSON><PERSON>", "dateFromForm": "<PERSON><PERSON> inicio", "dateToForm": "Fecha final", "pagamentEstat": "Estado de pago", "presentacioEstat": "Estado de presentación", "idTramitacioPagament": "ID tramitación de pago", "idTramitacio": "ID tramitación de presentación", "errorsFilter": "Opciones", "errors": "<PERSON><PERSON><PERSON>", "errEsborrany": "Error", "haveComplementari": "Es complementaria", "tipusPagament": "Método de pago"}}}, "DETAIL": {"PAGE_TITLE": "Detalle de la sesión de trabajo", "DELETE_MODAL": {"TITLE": "Eliminar sesión de trabajo", "TITLE_ERROR": "No se puede borrar la sesión de trabajo", "SUBTITLE": "¿Estas seguro de eliminar la sesión de trabajo con el id {{id}}?", "SUBTITLE_ERROR": "La sesión de trabajo que desea borrar no es una copia"}, "COPY_MODAL": {"TITLE_ERROR": "No se puede copiar la sesión de trabajo", "SUBTITLE_ERROR": "No se puede copiar una copia de la sesión de trabajo"}, "CARD": {"TITLE": "Datos de la sesión de trabajo", "FIELDS": {"id": "ID Sesión de trabajo", "esborrany": "<PERSON><PERSON><PERSON>", "estat": "Estado", "impost": "Impuesto", "tipus": "Tipo", "idioma": "Idioma", "nomPresentador": "Nom Presentador", "nifPresentador": "NIF Presentador", "nomSubjectePassiu": "Nom SP", "nifSubjectePassiu": "NIF SP", "nifTitular": "NIF Titular", "nomTitular": "Nom titular", "model": "<PERSON><PERSON>", "dataAlta": "<PERSON><PERSON>", "dataModificacio": "Fecha Modificación", "idAutoliquidacio": "ID Autoliquidación", "numJustificant": "<PERSON><PERSON><PERSON><PERSON>", "periode": "Periodo", "exercici": "Exercicio", "dadesAddicionals": "Datos Adiccionales"}}, "STATE_HISTORY_TABLE": {"TITLE": "Historial de estados", "COLUMNS": {"date": "<PERSON><PERSON>", "state": "Estado"}}, "TABS": {"DOCUMENTS": "Documentos", "PAYMENTS": "Pagaments", "PRESENTATIONS": "Presentacions", "TRAMITACIONS": "Tramitacions", "VALIDATIONS": "Validaciones", "NOTIFICATIONS": "Notificaciones", "GROUPING": "Agrupaciones", "ERRORS": "Errores", "INTERVENERS": "Intervinientes", "COMPLEMENTARY": "Complementaria", "SELFASSESSMENT": "Autoliquidaciones"}, "BUTTONS": {"COPY_DRAFT": "Copiar sessión de trabajo", "DELETE_DRAFT": "Eliminar sessión de trabajo"}}}, "MODULE_MANAGEMENTS_MONITOR": {"PAGE_TITLE": "Datos gestiones", "STATES": {"GENERAT": "Generado", "NO_PRESENTAT": "No presentado", "PRESENTANT": "<PERSON><PERSON><PERSON>", "PRESENTACIO_ERROR": "<PERSON>rror Presentación", "PRESENTAT": "<PERSON>ado", "ESBORRANY": "<PERSON><PERSON><PERSON>", "PAGAT": "<PERSON><PERSON>", "PAGANT": "Pa<PERSON>do", "PENDENT_PAGAMENT": "Pago <PERSON>", "PAGAMENT_ERROR": "<PERSON><PERSON><PERSON>", "TRAMITAT": "Tramitado", "TRAMITANT": "Tramitando", "TRAMITACIO_ERROR": "Error Tramitación", "CONSOLIDANT": "Consolidando", "CONSOLIDAT": "Consolidado", "CONSOLIDACIO_ERROR": "Error consolidación", "ERROR": "Error"}, "TABLE": {"EMPTY_MSG": "Ningún resultado coincide con sus criterios de búsqueda. Vuelva a intentarlo.", "COLUMNS": {"idTramitacio": "ID Tramitación", "estat": "Estado", "origen": "Origen", "presentadorNom": "Nom Presentador", "presentadorNif": "NIF Presentador", "tramit": "Tipo", "dadesAddicionals": "Datos Adiccionales", "subjectePassiuNom": "Nom SP", "subjectePassiuNif": "NIF SP", "dataModificacio": "<PERSON><PERSON>"}, "ROWS": {"TRAMIT": {"AVISOS": "Avisos", "IIIMA": "IIIMA", "ITPAJ": "ITPAJ", "IGEC": "IGEC", "DONACIONS": "Donacions", "IANP": "IANP", "ISBEE": "ISBEE", "ASSEGURANCES": "Assegurances", "RECURS": "Recurs", "RECURS_REPOSICIO_CO2": "Recursos CO2", "REA_CO2": "REA CO2", "REA": "REA", "DOMICILIACIO": "Domici<PERSON><PERSON><PERSON>", "ALLEGACIO_CO2": "Al·legacions CO2", "EXPEDIENT_CSV": "Expediente CSV", "CANVI_ADRECA": "<PERSON><PERSON>", "ALTA_NE": "ALTA NE", "MOD_NE": "MOD NE", "DEVOLUCIO_INGRESSOS": "Devolució <PERSON>", "SIGNATURA_INSPECCIO": "Signatura Inspecció", "APORTAR_DOCUMENTACIO": "Aportar documentación", "AJORNAMENT_FRACCIONAMENT": "Aplazamiento/Fraccionamiento"}}, "FILTER": {"TITLE": "Filtros de búsqueda", "FIELDS": {"state": "Estado", "presentadorNif": "NIF Presentador", "subjectePassiuNif": "NIF SP", "dateFromForm": "<PERSON><PERSON> inicio", "dateToForm": "Fecha final", "tipus": "Tipo", "dadesAddicionals": "Datos Adiccionales", "origen": "Origen", "idTramitacio": "ID Tramitación"}, "ORIGIN_OPTIONS": {"GAUDI": "GAUDI", "E_SPRIU": "e-SPRIU", "DALI": "DALI", "PADOCT": "PADOCT", "MIRO": "MIRO"}}}, "DETAIL": {"PAGE_TITLE": "Detalle de la gestión", "CARD": {"TITLE": "Datos de la gestión", "FIELDS": {"id": "ID Gestión Tracking", "idGestio": "ID Gestión", "idTramitacio": "ID Tramitación", "idTabTable": "ID Pestañas", "estat": "Estado", "presentadorNom": "Nom Presentador", "presentadorNif": "NIF Presentador", "subjectePassiuNom": "Nom SP", "subjectePassiuNif": "NIF SP", "origen": "Origen", "tramit": "Tipo", "dadesAddicionals": "Datos Adiccionales", "dataModificacio": "Fecha Modificación", "tipusActe": "Tipo Acto", "impost": "Impuesto", "import": "Importe", "dataNotificacio": "Fecha Notificación"}}, "STATE_HISTORY_TABLE": {"TITLE": "Historial de estados", "COLUMNS": {"date": "<PERSON><PERSON>", "state": "Estado"}}, "TABS": {"ACTS": "Actos", "DOCUMENTS": "Documentos", "PAYMENTS": "Pagaments", "PRESENTATIONS": "Presentacions", "TRAMITACIONS": "Tramitacions", "ERRORS": "Errores", "INTERVENERS": "Intervinientes", "COMPLEMENTARY": "Complementaria"}}}, "MODULE_BIOMETRIC_SIGNATURE": {"PAGE_TITLE": "Datos firma biométrica", "TABS": {"ACTIVES": "Activos", "HISTORIC": "Hist<PERSON><PERSON><PERSON>"}, "ENUMS": {"estat_peticio": {"GENERAT": "Generado", "ENVIAT": "Enviado", "SIGNAT": "<PERSON><PERSON><PERSON>", "ERROR_PETICIO": "<PERSON><PERSON><PERSON> petici<PERSON>", "ERROR_NOTIFICACIO": "Error notificación", "ERROR_REPOSITORI_DOCUMENTAL": "Error repositorio documental", "ERROR_HISTORIFICACIO": "Error historificación", "CANCELAT": "Cancelado", "CADUCAT": "<PERSON><PERSON><PERSON><PERSON>", "REBUTJAT": "<PERSON><PERSON><PERSON><PERSON>", "PENDENT": "Pendiente", "NOTIFICAT": "Notificado"}, "aplicacio": {"GAUDI": "GAUDI", "SRC": "SRC", "PORTAL": "PORTAL"}, "tipusSignatura": {"EMAILANDSMS": "EMAILANDSMS", "BIO": "BIO", "MOBILE": "MOBILE", "SMARTCARD": "SMARTCARD", "STAMP": "STAMP"}}, "TABLE": {"EMPTY_MSG": "Ningún resultado coincide con sus criterios de búsqueda. Vuelva a intentarlo.", "COLUMNS": {"id": "ID Petición", "class": "Clase", "anchor": "<PERSON><PERSON><PERSON>", "apiKey": "Clave API", "aplicacio": "Aplicación", "data_actualitzacio": "Fecha Actualización", "data_creacio": "<PERSON><PERSON>", "docGui": "<PERSON><PERSON><PERSON><PERSON>", "errors": "Errores", "stateRow": "Estado <PERSON>", "identificador_signant": "Identificador Firmante", "metadades": "Metadatos", "nom_dispositiu": "Nombre Dispositivo", "nom_document": "Nombre Documento", "nom_signant": "Nombre Firmante", "reintents": "Reintentos", "repositori_documental": "Repositorio Documental", "tipus_identificador_signant": "Tipo Identificador Firmante", "tipus_signatura": "Tipo de Firma", "id_vid_signer": "ID Vid Firmante", "id_repositori_documental": "ID Repositorio Documental"}, "ROWS": {"TRAMIT": {"AVISOS": "Avisos", "IIIMA": "IIIMA", "ITPAJ": "ITPAJ", "IGEC": "IGEC", "DONACIONS": "Donacions", "IANP": "IANP", "ISBEE": "ISBEE", "ASSEGURANCES": "Assegurances", "RECURS": "Recurs", "RECURS_REPOSICIO_CO2": "Recursos CO2", "REA": "REA", "DOMICILIACIO": "Domici<PERSON><PERSON><PERSON>", "ALLEGACIO_CO2": "Al·legacions CO2", "EXPEDIENT_CSV": "Expediente CSV", "CANVI_ADRECA": "<PERSON><PERSON>", "ALTA_NE": "ALTA NE", "MOD_NE": "MOD NE", "DEVOLUCIO_INGRESSOS": "Devolució <PERSON>", "SIGNATURA_INSPECCIO": "Signatura Inspecció", "APORTAR_DOCUMENTACIO": "Aportar documentación"}}, "FILTER": {"TITLE": "Filtros de búsqueda", "FIELDS": {"id": "ID Petición", "aplicacio": "Aplicación", "data_actualitzacio_from": "Fecha actualización inicio", "data_actualitzacio_to": "Fecha actualización fin", "data_creacio_from": "Fecha creación inicio", "data_creacio_to": "Fecha creación fin", "estat_peticio": "Estado petición", "identificador_signant": "Identificador firmante", "tipus_identificador_signant": "Tipo identificador firmante", "tipus_signatura": "Tipo firma", "opcions": "Opciones", "nomesErronis": "Solo erróneos", "metadades": "Metadatos", "data_creacio_error": "<PERSON><PERSON> creación error", "data_creacio": "<PERSON><PERSON> creación error", "error_missatge": "Descripción error", "errors": "Errores"}, "ORIGIN_OPTIONS": {"GAUDI": "GAUDI", "E_SPRIU": "e-SPRIU", "DALI": "DALI", "PADOCT": "PADOCT", "MIRO": "MIRO"}}}, "DETAIL": {"PAGE_TITLE": "Detalle de la firma biométrica", "RESET_BUTTON": "Reiniciar intents", "CARD": {"TITLE": "Datos de la firma biométrica", "FIELDS": {"id": "ID Petición", "class": "Clase", "anchor": "<PERSON><PERSON><PERSON>", "apiKey": "Clave API", "aplicacio": "Aplicación", "data_actualitzacio": "Fecha Actualización", "data_creacio": "<PERSON><PERSON>", "docGui": "<PERSON><PERSON><PERSON><PERSON>", "estat_peticio": "Estado <PERSON>", "identificador_signant": "Identificador Firmante", "nom_dispositiu": "Nombre Dispositivo", "nom_document": "Nombre Documento", "nom_signant": "Nombre Firmante", "reintents": "Reintentos", "repositori_documental": "Repositorio Documental", "tipus_identificador_signant": "Tipo Identificador Firmante", "tipus_signatura": "Tipo de Firma", "id_vid_signer": "ID Vid Firmante", "id_repositori_documental": "ID Repositorio Documental", "metadades": "Metadades", "errors": "Errores", "metadades_value": "Ver metadatos", "errors_value": "Ver errores"}}, "STATE_HISTORY_TABLE": {"TITLE": "Historial de estados", "COLUMNS": {"date": "<PERSON><PERSON>", "state": "Estado"}}, "TABS": {"ACTS": "Actos", "DOCUMENTS": "Documentos", "PAYMENTS": "Pagaments", "PRESENTATIONS": "Presentacions", "TRAMITACIONS": "Tramitacions", "ERRORS": "Errores", "INTERVENERS": "Intervinientes", "COMPLEMENTARY": "Complementaria"}}}}}