import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { DraftsTableComponent } from './drafts-table.component';
import { RouterModule, Routes } from '@angular/router';
import { ModalFilterModule } from 'src/app/modules/shared/modal-filter';

const routes: Routes = [
	{
		path: '', 
    component: DraftsTableComponent,
		data: { 
			title: 'MODULE_TRACKING.MODULE_DRAFTS.PAGE_TITLE', 
		}
	}
];

@NgModule({
	declarations: [
		DraftsTableComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		ModalFilterModule,
		TranslateModule.forChild(),
		RouterModule.forChild(routes),
		PtUiComponentsMfLibModule
	]
})
export class DraftsTableModule { }
