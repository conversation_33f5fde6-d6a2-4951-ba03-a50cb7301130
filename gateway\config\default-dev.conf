server {

    listen              1080;
    absolute_redirect   off;

    # server_name is important because it is used by shibboleth for generating SAML URLs
    # Using the catch-all '_' will NOT work.
    server_name integracio.admin-seu.atc.intranet.gencat.cat;


    # FastCGI authorizer for Auth Request module
    location = /shibauthorizer {
        internal;
        fastcgi_param  SERVER_PORT 443;
        include fastcgi_params;
        fastcgi_pass unix:/tmp/shibauthorizer.sock;
    }

    # FastCGI responder
    location ${SHIBBOLETH_RESPONDER_PATH:-/saml} {
        fastcgi_param  HTTPS on;
        fastcgi_param  SERVER_PORT 443;
        fastcgi_param  SERVER_PROTOCOL https;
        fastcgi_param  X_FORWARDED_PROTO https;
        fastcgi_param  X_FORWARDED_PORT 443;
        include fastcgi_params;
        fastcgi_pass unix:/tmp/shibresponder.sock;
    }

    # Resources for the Shibboleth error pages. This can be customised.
    location /shibboleth-sp {
        alias /etc/shibboleth/;
    }

    location = / {
        return 301 /tracking/;
    }

    location = /tracking {
        return 301 /tracking/;
    }

    location / {
        resolver ${RESOLVERS} valid=10s;

        ###################################
        ###     Protect front layer     ###
        ###################################

        location /tracking/ {
            more_clear_input_headers 'Variable-*' 'Shib-*' 'Remote-User' 'REMOTE_USER' 'Auth-Type' 'AUTH_TYPE';
            # Add your attributes here. They get introduced as headers
            # by the FastCGI authorizer so we must prevent spoofing.
            more_clear_input_headers 'displayName' 'mail' 'persistent-id' 'GICAR' 'GICARUMJ' 'GICARUMN' 'GICARMAIL' 'GICARCI' 'GICAR_ID' 'GICAR1' 'GICAR2' 'GICAR3' 'GICAR4' 'GICAR5';
            shib_request /shibauthorizer;
            shib_request_use_headers on;

            ### Requisit per a la Construccio de la capcalera GICAR ####
            more_set_input_headers 'GICAR1: CODIINTERN=';
            more_set_input_headers 'GICAR2: ;NIF=';
            more_set_input_headers 'GICAR3: ;EMAIL=';
            more_set_input_headers 'GICAR4: ;UNITAT_MAJOR=';
            more_set_input_headers 'GICAR5: ;UNITAT_MENOR=';

            ### Set headers ####
            proxy_set_header        Accept-Encoding   "";
            proxy_set_header        Host            $host;
            proxy_set_header        X-Real-IP       $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            ### Construccio de la capcalera GICAR ####
            proxy_set_header        GICAR $http_GICAR1$http_GICARCI$http_GICAR2$http_GICAR_ID$http_GICAR3$http_GICARMAIL$http_GICAR4$http_GICARUMJ$http_GICAR5$http_GICARUMN;
            proxy_set_header        origin_id       GICAR;
        }

        ############################################
        ### Protect microservice pt-seguretat-ms ###
        ############################################

        location /api/seguretat/ {
            more_clear_input_headers 'Variable-*' 'Shib-*' 'Remote-User' 'REMOTE_USER' 'Auth-Type' 'AUTH_TYPE';
            # Add your attributes here. They get introduced as headers
            # by the FastCGI authorizer so we must prevent spoofing.
            more_clear_input_headers 'displayName' 'mail' 'persistent-id' 'GICAR' 'GICARUMJ' 'GICARUMN' 'GICARMAIL' 'GICARCI' 'GICAR_ID' 'GICAR1' 'GICAR2' 'GICAR3' 'GICAR4' 'GICAR5';
            shib_request /shibauthorizer;
            shib_request_use_headers on;

            ### Requisit per a la Construccio de la capcalera GICAR ####
            more_set_input_headers 'GICAR1: CODIINTERN=';
            more_set_input_headers 'GICAR2: ;NIF=';
            more_set_input_headers 'GICAR3: ;EMAIL=';
            more_set_input_headers 'GICAR4: ;UNITAT_MAJOR=';
            more_set_input_headers 'GICAR5: ;UNITAT_MENOR=';

            ### ProxyPass Rule to tf-seguretat-ms ###
            proxy_pass http://***************:8082/api/seguretat/;

            ### Set headers ####
            proxy_set_header        Accept-Encoding   "";
            proxy_set_header        Host            $host;
            proxy_set_header        X-Real-IP       $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            ### Construccio de la capcalera GICAR ####
            proxy_set_header        GICAR $http_GICAR1$http_GICARCI$http_GICAR2$http_GICAR_ID$http_GICAR3$http_GICARMAIL$http_GICAR4$http_GICARUMJ$http_GICAR5$http_GICARUMN;
            proxy_set_header        origin_id       GICAR;
        }

        ##################################
        ### Proxy rules to back layer ###
        ##################################

        location /api/pagaments/ {
            proxy_pass http://***************:8081/api/pagaments/;
        }

        location /api/secured/pagaments/ {
            proxy_pass http://***************:8081/api/secured/pagaments/;
        }

        location /api/documents/ {
            proxy_pass http://***************:8083/api/documents/;
        }

        location /api/tributs/ {
            proxy_pass http://***************:8084/api/tributs/;
        }

        location /api/secured/tributs/ {
            proxy_pass http://***************:8084/api/secured/tributs/;
        }

        location /api/presentacions/ {
            proxy_pass http://***************:8085/api/presentacions/;
        }

        location /api/seguretat-admin/ {
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/api/seguretat-admin/;
			proxy_http_version 1.1;
        }

        location /api/dades-referencia/ {
            proxy_pass http://***************:8086/api/dades-referencia/;
        }
        
        location /api/pagament-liquidacions-config/ {
            proxy_pass http://***************:8094/api/pagament-liquidacions-config/;
        }
        
        location /api/secured/pagament-liquidacions-config/ {
            proxy_pass http://***************:8094/api/pagament-liquidacions-config/;
        }
        
    }
}
