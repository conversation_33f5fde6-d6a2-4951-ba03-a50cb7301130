import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PtSpinnerComponent } from 'pt-ui-components-mf-lib';
import { ExpedienteListComponent } from './expediente-list.component';
import {
	LazyElementModuleOptions,
	LazyElementsModule,
} from '@angular-extensions/elements';
import { environment } from 'src/environments/environment';

const routes: Routes = [
	{
		path: '',
		component: ExpedienteListComponent,
	},
];

const webcomponentsConfig: LazyElementModuleOptions = {
	elementConfigs: [
		{
			tag: 'aeat-conector-expediente-list',
			url: environment.wcUrlAEATConectorJs,
			loadingComponent: PtSpinnerComponent,
			preload: true,
		},
	],
};

@NgModule({
	declarations: [ExpedienteListComponent],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule.forFeature(webcomponentsConfig),
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ExpedienteListModule {}
