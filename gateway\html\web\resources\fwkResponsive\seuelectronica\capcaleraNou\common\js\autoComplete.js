$( document ).ready(function() {

// ******************* AUTO COMPLETE *************************/

    var RESULT_LIST_HITS_PER_PAGE = 10;
    var X_ALGOLIA_APPLICATION_ID = "QPQAVM8S9W";
    var X_ALGOLIA_API_KEY = "********************************";
    var INDEX_NAME_GENCAT = "pro_GENCAT";

    //Algolia settings
    const client = algoliasearch(X_ALGOLIA_APPLICATION_ID, X_ALGOLIA_API_KEY);
    var index = client.initIndex(INDEX_NAME_GENCAT);

    //Redirect settings
    var searchPage = $('#searchPage').val();
    var localeSearch = $('#localeSearch').val();
    var asSiteSearch = $('#asSiteSearch').val();
    var facetFilters = "";

    if(asSiteSearch){
        facetFilters = "site:"+asSiteSearch;
    }

    // Wait time for predictive search
    var globalTimeout = null;
    
    // Enabling predictive search in Algolia when the user releases a key, with 700 ms delay
    $("#autoCompleteCapcalera").keyup(function(e){
    	if(globalTimeout != null) clearTimeout(globalTimeout);  
    	  globalTimeout = setTimeout(SearchFunc,700,e); 
    });
    
    /**
     * Function that allows searching results in Algolia if the user has entered 3 or more characters
     * 
	 * @e Event
	*/
    function SearchFunc(e){
    	    	
    	  globalTimeout = null;  

    	  var code = e.key;
          if(code==="Enter"){
              //window.location.href = searchPage+ '?q=' + $("#autoCompleteCapcalera").val() + '&lr=lang_' + localeSearch;
              $('#fpca_capcalera_cercador').submit();
          }else{
              var autoSearch = $("#autoCompleteCapcalera").val();
              if(autoSearch.length > 2){
                  $('.NG-autoCompleteCapcalera_list').empty();
                  var searchQuery = {
                      query: $("#autoCompleteCapcalera").val(),
                      facetFilters: facetFilters,
                      hitsPerPage: RESULT_LIST_HITS_PER_PAGE
                  };
                  index.search(searchQuery, function(err, response) {
                      if (err) throw err;

                      //Put autocomplete div inside NG-inputSearch--wrapper & set styles
                      $('.NG-inputSearch--wrapper').append($('.NG-autoCompleteCapcalera_list'));
                      $('.NG-autoCompleteCapcalera_list').css("display", "block");
                      $('.NG-autoCompleteCapcalera_list').css("position", "absolute");
                      $('.NG-autoCompleteCapcalera_list').css("left", "11px");
                      $('.NG-autoCompleteCapcalera_list').css("right", "11px");
                      $('.NG-autoCompleteCapcalera_list').css("top", "44px");
                      $('.NG-autoCompleteCapcalera_list').css("bottom", "0px")
                      $('.NG-autoCompleteCapcalera_list').css("z-index", "2000");
                      $('.NG-autoCompleteCapcalera_list').css("border", "1px solid #DDDDDD");

                      //Fill autocomplete results
                      $(response.hits).each(function() {
                          //console.log(this);

                          if(this.title){
                              $('.NG-autoCompleteCapcalera_list').append('<div class="autocomplete-suggestion" data-val="' + this.title + '">' + this.title + '</div>');
                          }
                      });
                  });
              }
          }
          e.preventDefault();
    };

    new autoComplete({
        selector: '#autoCompleteCapcalera',
        minChars: 1,
        menuClass: "NG-autoCompleteCapcalera_list",
        renderItem: function (item, search){
            search = search.replace(/[-\/\\^$*+?.()|[\]{}]/g, '\\$&');
            var re = new RegExp("(" + search.split(' ').join('|') + ")", "gi");
            return '<div class="autocomplete-suggestion" data-val="' + item + '">' + item.replace(re, "<b>$1</b>") + '</div>';
        }
    });

    // ******************* END AUTO COMPLETE *************************/
});