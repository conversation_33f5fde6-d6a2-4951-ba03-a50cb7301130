import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MySpaceRoutes } from 'src/app/core/models/config.model';

const routes: Routes = [
	{
		path: '',
		redirectTo: MySpaceRoutes.SELF_ASSESSMENT,
		pathMatch: 'full'
	},
	{
		path: MySpaceRoutes.SELF_ASSESSMENT,
		loadChildren: () => import(`./autoliquidacions/autoliquidacions.module`).then(module => module.AutoliquidacionsModule),
	},
	{
		path: MySpaceRoutes.GESTIONS,
		loadChildren: () => import(`./gestions/gestions.module`).then(module => module.GestionsModule),
	},
	{
		path: MySpaceRoutes.PAGAMENTS,
		loadChildren: () => import(`./pagaments/pagaments.module`).then(module => module.PagamentsModule),
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class MySpaceRoutingModule { }
