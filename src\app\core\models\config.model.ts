export const PT_USER = 'pt-user';

export enum AppRoutes {
	OLD_SELFASSESSMENT_TABLE = 'secured/autoliquidacio/autoliquidacions',
	TAXES = 'secured/tributs',
	MANAGEMENTS = 'secured/gestions',
	LIQUIDACIONS = 'secured/liquidacions/llistat',
	RESOLUCIONS = 'secured/resolucions/llistat',
	USERS = 'secured/usuaris',
	USERS_TABLE = 'secured/seguretat/usuaris/llistat',
	MY_SPACE_CONF = 'secured/el-meu-espai-atc-config',
	PRIVATE_AREA_CONF = 'secured/area-privada-config',
	TOOLS = 'secured/eines',
	SARCAT_MIDDLEWARE = 'secured/sarcat-middleware/applications',
	WEB_MAP = 'mapa-web',
	PAGAMENTS = 'secured/gestions/pagaments',
}

export enum ManagementRoutes {
	MONITOR = 'gestions',
	PAGAMENTS = 'pagaments',
	DETAIL = 'secured/gestions/pagaments/detall'
}

export enum UserRoutes {
	SIMULATED_LOGIN = 'login-simulat',
}

export enum ToolsRoutes {
	NIF_ENCRYPTION = 'nif-encriptacio',
	MAINTENANCE_CONF = 'configuracio-manteniment',
	BIOMETRIC_SIGNATURE = 'biometric-signature',
	NOTICES_CONF = 'configuracio-avisos',
  EXPEDIENTES = 'expedients',
	SARCAT_MIDDLEWARE = 'sarcat-middleware/applications',
}

export enum TaxesRoutes {
	SELF_ASSESSMENT_V1 = 'autoliquidacions/v1',
	SELF_ASSESSMENT_V2 = 'nous-tributs',
	DRAFTS = 'sessions-treball',
}

export enum SelfAssessmentV1Routes {
	TABLE = 'taula',
	DETAIL = 'detall',
	DETAIL_ID = 'detall/:id',
}

export enum SelfAssessmentV2Routes {
	TABLE = 'taula',
	DETAIL = 'detall',
	DETAIL_ID = 'detall/:id',
}

export enum DraftsRoutes {
	TABLE = 'taula',
	DETAIL = 'detall',
	DETAIL_ID = 'detall/:id',
}

export enum ManagementsMonitorRoutes {
	TABLE = 'taula',
	DETAIL = 'detall',
	DETAIL_ID = 'detall/:id',
}

export enum BiometricSignatureRoutes {
	TABLE = 'taula',
	DETAIL = 'detall',
	DETAIL_ID = 'detall/:id',
}

export enum MySpaceRoutes {
	SELF_ASSESSMENT = 'autoliquidacions',
	GESTIONS = 'gestions',
	PAGAMENTS = 'pagaments',
}

export enum ExpedientesRoutes {
  LIST = 'taula',
  DETAIL_ID = 'detall/:idEni/:idPeticion',
}

export enum StatesIcons {
	ERROR = 'sli4-close-circle-solid text-danger',
	SUCCESS = 'sli4-check-circle-solid text-success',
	SPINNER = 'pi pi-spin pi-spinner text-warning',
	WAITING = 'sli4-en-espera',
	WAITING_WARNING = 'sli4-en-espera text-warning',
	CLOSE_INFO = 'sli4-close-circle-solid text-info',
	EXCLAMATION_WARNING = 'sli4-exclamation-circle-solid text-warning',
	INFO = 'sli2-info-circle text-info',
}
export type StatesIconsT = keyof typeof StatesIcons;
export type StatesIconsValues<StatesIcons> = StatesIcons[keyof StatesIcons];
