import { PtHttpResponse } from "pt-ui-components-mf-lib";
import { IDrafts } from "../../../models/drafts.model";

export interface CopyDraftResponse extends PtHttpResponse {
	content:  Partial<IDrafts>;
}

export interface ResponseGetDrafts extends PtHttpResponse {
	content:  IDrafts;
}

export interface ResponseDeleteAutoliquidacio extends PtHttpResponse {
	content: string;
}

export interface GetSelfAssessmentsResponse extends PtHttpResponse {
	content:  DraftSelfAssesment[];
}

export interface DraftSelfAssesment {
	id:string;
	numJustificant: string;
	importe: number;
	nomSubjectePassiu: string;
	nifSubjectePassiu: string;
	estat: string;
}