import { Validators } from "@angular/forms";
import { ExportColumnsData } from "src/app/core/models/export-table.model";
import { FilterFieldType, FilterFieldsData } from "src/app/modules/shared/filter-inputs";
import { IAutoliquidacioFilters, PaymentMethods, PresentationStates, States } from "../../../models/autoliquidacions-v1.model";
import { PtValidations } from "pt-ui-components-mf-lib";

export enum TableColumns {
  idReceipt = 'idReceipt',
  state = 'state',
  presentationState = 'presentationState',
  nifPresenter = 'agent',
  namePresenter = 'agent',
  nifPassiveSubject = 'passiveSubject',
  namePassiveSubject = 'passiveSubject',
  model = 'model',
  complementedNumJustificant = 'complementedNumJustificant',
  amount = 'amount',
  mui = 'clauCobramentMUI',
  paymentMethod = 'paymentMethod',
  nifTitular = 'agent',
  nameTitular = 'agent',
  loadDate = 'loadDate',
  paymentDate = 'paymentDate',
  presentationDate = 'presentationDate',
  toReview = 'A_Revisar',
  clauCobrament = 'clauCobramentMUI',
  idTramitacio = 'idTramitacio',
  canviEstat = 'canviEstat',
  codiErrors = 'errors',
  errors = 'errors',
  stateRow = "stateRow",
  tableActions = "tableActions"
}
export type TableColumnsT = keyof typeof TableColumns;
export type TableColumnsValues<TableColumns> = TableColumns[keyof TableColumns];

export const EXPORT_COLUMNS_DATA: ExportColumnsData[] = [
  {
    id: TableColumns.toReview,
    columnTitle: "A_Revisar",
    columnType: 'toReview'
  },
  {
    id: TableColumns.loadDate,
    columnTitle: "Data_Creacio",
    columnType: 'date'
  },
  {
    id: TableColumns.presentationDate,
    columnTitle: "Data_Presentacio",
    columnType: 'date'
  },
  {
    id: TableColumns.paymentDate,
    columnTitle: "Data_Pagament",
    columnType: 'date'
  },
  {
    id: TableColumns.idReceipt,
    columnTitle: "Num_Justificant",
    columnType: 'text'
  },
  {
    id: TableColumns.state,
    columnTitle: "Estat",
    columnType: 'translation',
    translation: 'MODULE_TRACKING.STATES'
  },
  {
    id: TableColumns.presentationState,
    columnTitle: "Presentacio",
    columnType: 'presentationState',
    translation: 'MODULE_TRACKING.STATES'
  },
  {
    id: TableColumns.model,
    columnTitle: "Model",
    columnType: 'text'
  },
  {
    id: TableColumns.amount,
    columnTitle: "Import",
    columnType: 'text'
  },
  {
    id: TableColumns.nifTitular,
    columnTitle: "Nif_Titular",
    columnType: 'text',
    attr: 'nif'
  },
  {
    id: TableColumns.nameTitular,
    columnTitle: "Nom_Titular",
    columnType: 'text',
    attr: 'name'
  },
  {
    id: TableColumns.nifPresenter,
    columnTitle: "Nif_Presentador",
    columnType: 'text',
    attr: 'presenterNif'
  },
  {
    id: TableColumns.namePresenter,
    columnTitle: "Nom_Presentador",
    columnType: 'text',
    attr: 'presenterName'
  },
  {
    id: TableColumns.nifPassiveSubject,
    columnTitle: "Nif_Sub_Passiu",
    columnType: 'array',
    attr: 'nif',
  },
  {
    id: TableColumns.namePassiveSubject,
    columnTitle: "Nom_Sub_Passiu",
    columnType: 'array',
    attr: 'name',
    arrayPos: 0
  },
  {
    id: TableColumns.idTramitacio,
    columnTitle: "ID_Tramitacio",
    columnType: 'text'
  },
  {
    id: TableColumns.mui,
    columnTitle: "MUI",
    columnType: 'text',
    attr: 'referencia'
  },
  {
    id: TableColumns.clauCobrament,
    columnTitle: "Clau_Cobrament",
    columnType: 'text',
    attr: 'composition'
  },
  {
    id: TableColumns.paymentMethod,
    columnTitle: "Tipus_Pagament",
    columnType: 'translation',
    translation: 'MODULE_TRACKING.PAYMENT_METHODS'
  },
  {
    id: TableColumns.complementedNumJustificant,
    columnTitle: "Complementaria",
    columnType: 'textToBoolean',
    translation: 'MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE'
  },
  {
    id: TableColumns.canviEstat,
    columnTitle: "Canvi_Estat",
    columnType: "stateChanges"
  },
  {
    id: TableColumns.codiErrors,
    columnTitle: "Codi_Error",
    columnType: 'array',
    attr: 'technicalCode',
    arrayPos: -1
  },
  {
    id: TableColumns.errors,
    columnTitle: "Errors",
    columnType: 'errors'
  }
]

export type FilterFieldsFormData = {
  [K in FilterFormFieldsT] : FilterFieldsData;
}

export enum FilterFormFields {
  loadDateFrom = 'loadDateFrom',
  loadDateTo = 'loadDateTo',
  idReceipt = 'idReceipt',
  state = 'state',
  nifSP = 'nifSP',
  nifPresenter = 'nifPresenter',
  nifTitular = 'nifTitular',
  clauCobramentMUIFilter = 'clauCobramentMUIFilter',
  presentationState = 'presentationState',
  idTramitacio = 'idTramitacio',
  idPRPT = 'idPRPT',
  model = 'model',
  paymentMethod = 'paymentMethod',
  optionsFilter = 'optionsFilter',
  complementedNumJustificant = 'complementedNumJustificant'
}
export type FilterFormFieldsT = keyof typeof FilterFormFields;

const FILTER_FIELDS_TRANSLATIONS = "MODULE_TRACKING.COMPONENT_LIQUIDACIO.SECTION_SEARCH.FORM.FIELDS";
export const FILTER_FORM_DATA:FilterFieldsData[][] = [
  [
    {
      id: FilterFormFields.loadDateFrom,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.loadDateFrom}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.loadDateTo,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.loadDateTo}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.idReceipt,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idReceipt}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.state,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.state}`,
      fieldType: FilterFieldType.multiSelect,
      optionsValue: Object.entries(States),
      optionsLabelTranslations : 'MODULE_TRACKING.STATES'
    },
    {
      id: FilterFormFields.nifSP,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifSP}`,
      fieldType: FilterFieldType.text,
      validations: PtValidations.dniNieCif,
    },
    {
      id: FilterFormFields.nifPresenter,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifPresenter}`,
      fieldType: FilterFieldType.text,
      validations: PtValidations.dniNieCif
    },
    {
      id: FilterFormFields.nifTitular,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifTitular}`,
      fieldType: FilterFieldType.text,
      validations: PtValidations.dniNieCif
    },
    {
      id: FilterFormFields.clauCobramentMUIFilter,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.clauCobramentMUIFilter}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.presentationState,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.presentationState}`,
      fieldType: FilterFieldType.multiSelect,
      optionsValue: Object.entries(PresentationStates),
      optionsLabelTranslations : 'MODULE_TRACKING.STATES'
    },
    {
      id: FilterFormFields.idTramitacio,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idTramitacio}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.idPRPT,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idPRPT}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.model,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.model}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.paymentMethod,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.paymentMethod}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(PaymentMethods),
      optionsLabelTranslations : 'MODULE_TRACKING.PAYMENT_METHODS'
    },
  ],
  [
    {
      id: FilterFormFields.optionsFilter,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.optionsFilter}`,
      fieldType: FilterFieldType.checkbox,
      options: [
        { id: 'errors', label: `${FILTER_FIELDS_TRANSLATIONS}.errores` },
      ]
    },
    {
      id: FilterFormFields.complementedNumJustificant,
      containerClass: 'col-12 col-sm-3 col-md-3 col-lg-3 align-self-end',
      fieldType: FilterFieldType.triCheckbox,
      options: [
        { id: 1, label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.complementedNumJustificant}` },
      ]
    }
  ]
]

export class FilterFormValue implements IAutoliquidacioFilters {
  loadDateFrom: Date;
  loadDateTo: Date;
  dateFrom: number;
  dateTo: number;
  state: string[];
  presentationState: string[];
  idReceipt: string;
  model: string;
  complementedNumJustificant: string;
  isComplementary: boolean;
  origin: string;
  optionsFilter: string[];
  idComunication: string;
  idTramitacio: string;
  idPRPT: string;
  nifSP: string;
  protocol: string;
  nifPresenter: string;
  nifTitular: string;
  notaryName: string;
  paymentMethod: PaymentMethods;
  clauCobramentMUIFilter: string;
  clauCobramentMUI: { referencia: string; };

  constructor(
    data?: IAutoliquidacioFilters
  ) {
    const dateFrom = data?.loadDateFrom ? new Date(data.loadDateFrom) : new Date();
    const dateTo = data?.loadDateTo ? new Date(data.loadDateTo) : new Date();
    if (!data?.loadDateFrom) dateFrom.setDate(dateFrom.getDate() - 30);

    Object.assign(this, {
      ...data,
      loadDateFrom: dateFrom,
      loadDateTo: dateTo,
    });
  }
  
  
}