import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { FilterInputsComponent } from './filter-inputs.component';

@NgModule({
	declarations: [
		FilterInputsComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule
	],
  exports: [FilterInputsComponent]
})
export class FilterInputsModule { }
