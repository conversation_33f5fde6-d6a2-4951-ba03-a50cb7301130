import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DetailComponent } from './detail.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { RouterModule, Routes } from '@angular/router';
import { DetailPageModule } from 'src/app/modules/shared/detail-page';

const routes: Routes = [
	{
		path: '', component: DetailComponent,
		data: { 
			title: '',
			isElementVisible: false
		}
	}
];

@NgModule({
	declarations: [
		DetailComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		DetailPageModule,
		RouterModule.forChild(routes)
	]
})
export class DetailModule { }
