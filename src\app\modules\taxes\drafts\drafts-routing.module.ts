import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DraftsRoutes } from 'src/app/core/models/config.model';
import { DraftsComponent } from './drafts.component';

const routes: Routes = [
	{
		path: '', component: DraftsComponent,
		children: [
			{
				path: '', 
				redirectTo: DraftsRoutes.TABLE, 
				pathMatch: 'full'
			},
			{
				path: DraftsRoutes.TABLE,
				component: DraftsComponent,
				loadChildren: () => import(`./modules/drafts-table/drafts-table.module`).then(module => module.DraftsTableModule),
			},
			{
				path: DraftsRoutes.DETAIL_ID, 
				loadChildren: () => import(`./modules/drafts-detail/drafts-detail.module`).then(module => module.DraftsDetailModule),
			}
		]
	}
	
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class DraftsRoutingModule { }