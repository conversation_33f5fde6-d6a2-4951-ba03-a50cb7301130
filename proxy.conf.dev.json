{"/api/tributs/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/tributs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/tributs": ""}}, "/api/cita-previa/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/cita-previa", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/cita-previa": ""}}, "/api/area-privada-config/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/area-privada-config", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/area-privada-config": ""}}, "/api/gestions-admin/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/gestions-admin", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gestions-admin": ""}}, "/api/area-privada/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/area-privada", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/area-privada": ""}}, "/api/documents-admin/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/documents-admin", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents-admin": ""}}, "/api/documents/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/documents", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents": ""}}, "/api/dades-referencia/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/dades-referencia", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/dades-referencia": ""}}, "/api/seguretat-privat/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat-privat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat-privat": ""}}, "/api/seguretat-admin/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat-admin", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat-admin": ""}}, "/api/seguretat/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat": ""}}, "/api/gestio-configuracio-tr/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/gestio-configuracio-tr", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gestio-configuracio-tr": ""}}, "/api/signatura-biometrica/*": {"target": "https://integracio.seu2.atc.intranet.gencat.cat/api/signatura-biometrica", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/signatura-biometrica": ""}}, "/api/gestio-configuracio/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/gestio-configuracio", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gestio-configuracio": ""}}, "/api/pagament-liquidacions-config/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/pagament-liquidacions-config", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/pagament-liquidacions-config": ""}}, "/mf/pt-seguretat-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-seguretat-mf": ""}}, "/mf/pt-gestions-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-gestions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-gestions-mf": ""}}, "/mf/pt-recurs-reposicio-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-recurs-reposicio-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-recurs-reposicio-mf": ""}}, "/mf/pt-dispatcher-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-dispatcher-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-dispatcher-mf": ""}}, "/mf/pt-documents-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-documents-mf": ""}}, "/mf/pt-pagaments-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-pagaments-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-pagaments-mf": ""}}, "/mf/pt-ciutada-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-ciutada-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-ciutada-mf": ""}}, "/mf/pt-commons-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-commons-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-commons-mf": ""}}, "/mf/pt-presentacions-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-presentacions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-presentacions-mf": ""}}, "/mf/pt-el-meu-espai-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-el-meu-espai-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-el-meu-espai-mf": ""}}, "/mf/sarcat-middleware-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/sarcat-middleware-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/sarcat-middleware-mf": ""}}, "/mf/se-recurs-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-recurs-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-recurs-mf": ""}}, "/mf/se-reas-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-reas-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-reas-mf": ""}}, "/mf/se-suspensio-garantia-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-suspensio-garantia-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-suspensio-garantia-mf": ""}}, "/mf/se-documents-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-documents-mf": ""}}, "/mf/se-contribuent-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-contribuent-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-contribuent-mf": ""}}, "/mf/se-pagaments-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-pagaments-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-pagaments-mf": ""}}, "/mf/se-seguretat-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-seguretat-mf": ""}}, "/mf/se-gestions-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-gestions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-gestions-mf": ""}}, "/mf/se-padro-co2-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-padro-co2-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-padro-co2-mf": ""}}, "/mf/se-el-meu-espai-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/se-el-meu-espai-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/se-el-meu-espai-mf": ""}}, "/api/reas/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/reas", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/reas": ""}}, "/api/recurs/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/recurs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/recurs": ""}}, "/api/emissions-co2/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/emissions-co2", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/emissions-co2": ""}}, "/api/login-simple/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/login-simple", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/login-simple": ""}}, "/api/gestions/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/gestions", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gestions": ""}}, "/api/contribuent/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/contribuent", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/contribuent": ""}}, "/api/presentacions/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/presentacions", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/presentacions": ""}}, "/api/pagaments/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/pagaments": ""}}, "/api/secured/pagaments/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/secured/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/secured/pagaments": ""}}, "/api/sarcat-middleware/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/sarcat-middleware", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/sarcat-middleware": ""}}, "/mf/aeat-conector-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/aeat-conector-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/aeat-conector-mf": ""}}, "/api/aeat-conector/v1/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/aeat-conector/v1", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/aeat-conector/v1": ""}}}