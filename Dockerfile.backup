FROM registreimatges.sic.intranet.gencat.cat/gencatcloud/gicar-nginx-openshift:1.1

USER root

# Copy nginx config files
COPY gateway/config/default.conf /etc/nginx/conf.d/

# Copy certificates
COPY gateway/certificates/* /etc/shibboleth/

# Copy static files
RUN rm -rf /etc/nginx/html/*
COPY dist/pt-portal-tracking-gw /etc/nginx/html/tracking
RUN ln -s /www/data/html/mf /etc/nginx/html/mf

# Changing file permissions
RUN chmod g+w /etc/nginx/conf.d/default.conf

# Provisional: Copy static files of all components
#RUN mkdir -p /data/html/pt-portal-tracking-gw
#COPY html /data/html/pt-portal-tracking-gw/tracking
#RUN rm /data/html/pt-portal-tracking-gw/tracking/README.md

USER 1001
