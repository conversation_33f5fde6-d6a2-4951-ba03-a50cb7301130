<div *ngIf="dataLoaded | async">
	<app-navbar></app-navbar>
	
	<div class="main-container">
		<div class="page-container">
			<div class="container-fluid">
				
				<!--/* PAGE TITLE */-->
				<pt-page-title [_title]="pageLayoutService.title"></pt-page-title>
				
				<!--/* BACK MSG*/-->
				<pt-exception-viewer *ngIf="pageLayoutService.isElementVisible"></pt-exception-viewer>

				<router-outlet></router-outlet>
			</div>
		</div>
	</div>
</div>
