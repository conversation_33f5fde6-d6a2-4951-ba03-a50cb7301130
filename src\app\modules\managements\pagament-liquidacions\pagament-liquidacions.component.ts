import { Component, OnInit } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-pagament-liquidacions',
	templateUrl: './pagament-liquidacions.component.html',
	styleUrls: ['./pagament-liquidacions.component.sass']
})
export class PagamentLiquidacionsComponent {

	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlPagamentLiquidacioCss;

	constructor(
		private webComponentService: WebComponentsService
	) {
		this.webComponentService.setWebComponentStyle(this.wcUrlCss, 'pagaments-liquidacions');
	}

}
