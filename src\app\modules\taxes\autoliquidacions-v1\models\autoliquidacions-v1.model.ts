import { LazyLoadEvent } from 'primeng/api';
import { PtTableButtonsTemplate, PtTableLinkTemplate } from 'pt-ui-components-mf-lib';
import { RequestDeleteSelfAssessment } from '../modules/detail/models/detail-endpoint.model';

/**
 * Enum states
 */
export enum States {
	GENERATED = 'GENERATED',
	DRAFT = 'DRAFT',
	DRAFT_VALIDATING = 'DRAFT_VALIDATING',
	DRAFT_ERROR = 'DRAFT_ERROR',
	DRAFT_REVALIDATE = 'DRAFT_REVALIDATE',
	DRAFT_VALIDATED = 'DRAFT_VALIDATED',
	DRAFT_GROUPING = 'DRAFT_GROUPING',
	DRAFT_GROUPING_ERROR = 'DRAFT_GROUPING_ERROR',
	DRAFT_GROUPED = 'DRAFT_GROUPED',
	RECEIVED = 'RECEIVED',
	RECIVED = 'RECIVED',
	VALIDATING = 'VALIDATING',
	VALIDATING_ERROR = 'VALIDATING_ERROR',
	VALIDATED = 'VALIDATED',
	PAYING = 'PAYING',
	PAYING_ERROR = 'PAYING_ERROR',
	CONFIRMED = 'CONFIRMED',
	CONFIRMED_ERROR = 'CONFIRMED_ERROR',
	PAID = 'PAID',
	TRAMITATED = 'TRAMITATED',
	TRAMITATED_ERROR = 'TRAMITATED_ERROR',
	TRAMITATING = 'TRAMITATING',
	PRESENTING = 'PRESENTING',
	PRESENTING_ERROR = 'PRESENTING_ERROR',
	PRESENTED = 'PRESENTED',
	NO_PRESENTED = 'NO_PRESENTED',
	PROCESSED = 'PROCESSED',
	PROCESSING = 'PROCESSING',
	PROCESSING_ERROR = 'PROCESSING_ERROR',
	ERROR = 'ERROR',
	NOTIFIED_ERROR = 'NOTIFIED_ERROR',
	NOTIFYING = 'NOTIFYING'
}
export type StatesT = keyof typeof States;
export type StatesValues<States> = States[keyof States];

export enum PresentationStates {
	PAID = 'PAID',
	PRESENTED = 'PRESENTED',
	NO_PRESENTED = 'NO_PRESENTED',
	PROCESSED = 'PROCESSED',
}
export type PresentationStatesT = keyof typeof PresentationStates;
export type PresentationStatesValues<PresentationStates> = PresentationStates[keyof PresentationStates];

/**
 * Enum states
 */
export enum PaymentMethods {
	BANK_ACCOUNT = 'BANK_ACCOUNT',
	CREDIT_CARD = 'CREDIT_CARD'
}
export type PaymentMethodsT = keyof typeof PaymentMethods;
export type PaymentMethodsValues<PaymentMethods> = PaymentMethods[keyof PaymentMethods];

/**
 * Entity: Autoliquidació
 */

export interface IAutoliquidacio {
	id: string;
	idSelfAssessment: string;
	idModel: string;
	amount: string;
	loadDate: Date;
	idReceipt: string;
	model: string;
	complementedNumJustificant: string;
	presentacio: string;
	errors: [];
	canvis: [];
	number: number;
	presentationReqId: number;
	fileRequestID: number;
	payment: number;
	loadOn: boolean;
	validationOn: boolean;
	idMFPT: number;
	tax
	incomePeriod: string;
	origin: string;
	version: number;
	certificateID: string;
	versionModel: string;
	agent: IPresentador;
	taxPayers
	state: States;
	presentationState: States;
	stateChanges: StateChangesTableRow[];
	presenter: IPresentador;
	protocol: string;
	notary: INotary;
	nifSP: string;
	nameSP: string;
	attachedDocument: string;
	mui: string;
	idComunication: string;
	passiveSubject: IPresentador[];
	paymentDate: Date;
	paymentMethod: PaymentMethods;
	bankAccount: string;
	NRC: string;
	idGt: string;
	idProceeding: string;
	CSV: string;
	presentationDate: Date;
	notificationDate: Date;
	notificationURL: string;
	docManagerRef: string;
	numRetries: number;
	folderDataSource: string;
	idDocument: string;
	notarialFileReference: number;
	idTramitacio: string;
	clauCobramentMUI: IClauCobrament;
}

/**
 * Autoliquidacions: search filters
 */
export interface IAutoliquidacioFilters {
	loadDateFrom: Date;
	loadDateTo: Date;
	dateFrom: number;
	dateTo: number;
	state: string[];
	presentationState: string[];
	idReceipt: string;
	model: string;
	complementedNumJustificant: boolean | string;
	isComplementary: boolean;
	origin: string;
	optionsFilter: string[];
	idComunication: string;
	idTramitacio: string;
	idPRPT: string;
	nifSP: string;
	protocol: string;
	nifPresenter: string;
	nifTitular: string;
	notaryName: string;
	paymentMethod: PaymentMethods,
	clauCobramentMUIFilter: string,
	clauCobramentMUI: {
		referencia: string
	}
}

export class AutoliquidacioRequest {
	filter: Partial<IAutoliquidacioFilters>;
	options: LazyLoadEvent;

	constructor(params: Partial<IAutoliquidacioFilters>, options: LazyLoadEvent) {
		const df = new Date(params.loadDateFrom);
		const dt = new Date(params.loadDateTo);
		let from: Date = new Date(df.getFullYear(), df.getMonth(), df.getDate(), 0, 0, 0);
		let to: Date = new Date(dt.getFullYear(), dt.getMonth(), dt.getDate() + 1, 0, 0, 0);

		this.filter = {
			dateFrom: from.getTime(),
			dateTo: to.getTime(),
			state: params?.state?.length > 0 ? params?.state : null,
			presentationState: params?.presentationState?.length > 0 ? params?.presentationState : null,
			idReceipt: params.idReceipt,
			model: params.model,
			complementedNumJustificant: this.handleComplementedNumJustificantValue(params.complementedNumJustificant),
			isComplementary: params.isComplementary,
			origin: params.origin,
			optionsFilter: params.optionsFilter,
			idComunication: params.idComunication,
			idTramitacio: params.idTramitacio,
			idPRPT: params.idPRPT,
			nifSP: params.nifSP,
			protocol: params.protocol,
			nifPresenter: params.nifPresenter,
			nifTitular: params.nifTitular,
			notaryName: params.notaryName,
			paymentMethod: params.paymentMethod,
			clauCobramentMUI: {
				referencia: params.clauCobramentMUIFilter
			}
		}
		this.options = options
	}

	private handleComplementedNumJustificantValue(complementedNumJustificant: string | boolean) {
		return complementedNumJustificant === false ? '' : complementedNumJustificant
	}
}

export interface IClauCobrament {
	composition: string;
	emissora: string;
	identificacio: string;
	importe: string;
	referencia: string;
}

export interface IPresentador {
	certificate: any;
	goverment: any;
	id: string;
	name: string;
	nif: string;
	role: any;
	presenterNif:string;
	presenterName:string;
}

export interface IDetailData {
	filterValue: Partial<IAutoliquidacioFilters>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idSelfAssessment?: string
}

export interface INotary {
	APELLIDOS: any;
	COD_CAT: any;
	CUV: any;
	DOC_IDE: any;
	NOMBRE: string;
}

/**
 * Autoliquidacions: table row
 */
export interface AutoliquidacioRow extends IAutoliquidacio {
	stateRow: PtTableLinkTemplate;
	presenterName: string;
	presenterNif: string;
	notaryName: string;
	mui: string;
}

export interface ErrorsTableRow {
	data: string;
	error: string;
}

export interface DocumentosTableRow {
	referenciaGestorDocumental: number;
	descripcion: PtTableButtonsTemplate[];
}

export interface StateChangesTableRow {
	before: string,
	current: string,
	dateChange: Date
}

export interface Idocument {
	gd: string;
	id: string;
	idGd: string;
	idTribut: string;
	name: string;
	partentId: string;
	type: string;
	version: string;
}

export interface IModalFiltreData {
	filterFormValue: Partial<IAutoliquidacioFilters>,
	mapSourceOrigin: Map<string, string>
}

export interface DeleteModalData extends RequestDeleteSelfAssessment {
	state: string,
	confirmFn: (idReceipt?: string, idSelfAssessment?: string) => void
}