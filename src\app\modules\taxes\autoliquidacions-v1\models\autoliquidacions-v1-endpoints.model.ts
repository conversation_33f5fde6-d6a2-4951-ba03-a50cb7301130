import { PtHttpResponse } from "pt-ui-components-mf-lib";
import { IAutoliquidacio, Idocument } from "./autoliquidacions-v1.model";

export interface ListDocumentGet {
	idAutoliquidacion: string;
}

export interface DocumentB64Get {
	idPadoc: string;
	b64: boolean;
}

export interface ResponseListaDocumentGet extends PtHttpResponse {
	content: {
		listaDocuments: Idocument[];
	}
}

export interface ResponseGetDocument extends PtHttpResponse {
	content: string;
}

export interface ResponseDeleteErrorsAutoliquidacio extends PtHttpResponse {
	content: string;
}

export interface AutoliquidacioResponseContent {
	results: IAutoliquidacio[];
	size: number;
	total: number;
}

export interface AutoliquidacioResponse extends PtHttpResponse {
	content: AutoliquidacioResponseContent;
}