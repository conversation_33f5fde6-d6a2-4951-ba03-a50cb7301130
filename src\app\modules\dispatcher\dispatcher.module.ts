// Core
import { Routes, RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';

//Components
import { TrackingDispatcherComponent } from './dispatcher.component';

const routes: Routes = [
	{
		path: '', component: TrackingDispatcherComponent,
		data: { 
			title: '',
			isElementVisible: false 
		}
	}
];
@NgModule({
	declarations: [
		TrackingDispatcherComponent
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	]
})
export class TrackingDispatcherModule { }
