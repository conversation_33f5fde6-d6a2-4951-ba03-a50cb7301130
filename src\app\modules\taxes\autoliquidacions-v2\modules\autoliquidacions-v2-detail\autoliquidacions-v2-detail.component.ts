import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { 
	SelfAssessmentV2Request, 
	ISelfAssessmentV2, 
	AUTOLIQUIDACIONS_V2_DETAIL_STORAGE, 
	CanviEstat, 
	States
} from '../../models/autoliquidacions-v2.model';
import { AppRoutes, SelfAssessmentV2Routes, TaxesRoutes } from 'src/app/core/models/config.model';
import { DETAIL_CARD_FORM_DATA, DETAIL_TABS_MODELS, DETAIL_TAB_DATA, IDetailData, TypesDeclaration } from './models/autoliquidacions-v2-detail.model';
import { takeUntil } from 'rxjs/operators';
import { AutoliquidacionsV2DetailEndpointsService } from './services/autoliquidacions-v2-detail-endpoint.service';
import { AutoliquidacionsV2Service } from '../../services/autoliquidacions-v2.service';
import { AutoliquidacionsV2EndpointsService } from '../../services/autoliquidacions-v2-endpoints.service';
import { DetailCardData } from '../../../../shared/detail-card';
import { StateChangesTableRow } from '../../../../shared/detail-state-table';
import { TabData, TabType } from '../../../../shared/detail-tabs';
import { DeleteModalData } from '../../../autoliquidacions-v1/models/autoliquidacions-v1.model';
import { ResponseDeleteAutoliquidacio } from './models/autoliquidacions-v2-detail-endpoint.model';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { DownloadButtonsData } from 'src/app/modules/shared/detail-page';

@Component({
	selector: 'app-resolucions-detail',
	templateUrl: './autoliquidacions-v2-detail.component.html'
})
export class AutoliquidacionsV2DetailComponent implements OnInit {

	// Procedure Data
	selfAssessment: ISelfAssessmentV2;
	idSelfAssessment: string;
	idReceipt: string;

	// Detail data
	stateChanges: StateChangesTableRow[] = [];
	detailData: IDetailData;
	stateIcon: string;
	stateLabel: string;
	detailCardFields:DetailCardData[] = [];
	detailTabInput: TabData[] =[];
	
	// Navegation Data
	currentIndex:number;
	totalRows:number;	
	navigateBackUrl: string;
	
	// Validations models
	/* En esta lista se tienen que añadir los identificadores de los modelos
		tributarios para los que se requiera mostrar los botones
		"Descargar justificante" y "Descargar diligencia". */
	downloadButtonsModelList:string[] = ["600", "651", "652"];

	// Other
	showDeleteButton: boolean = true;
	deleteAllowRoles: UserRoles[] = [UserRoles.ADMIN];
	allowReceiptStates: string[] = [States.PAGAT, States.PRESENTAT];
	allowProceedingStates: string[] = [States.PAGAT];

	downloadButtonsData: DownloadButtonsData;
	showPresentationButtons:boolean;

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		private autoliquidacionsV2EndpointsService: AutoliquidacionsV2EndpointsService,
		private detailEndpointsService: AutoliquidacionsV2DetailEndpointsService,
		private router: Router,
		private aRoute: ActivatedRoute,
		private autoliquidacionsV2Service: AutoliquidacionsV2Service
	) {
		this.idSelfAssessment = this.aRoute.snapshot.params.id;
	}

	ngOnInit(): void {
		this.setConstantValues();

		this.setDetailData();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();

		this.autoliquidacionsV2Service.dataStorageService.deleteItem(AUTOLIQUIDACIONS_V2_DETAIL_STORAGE);
	}
	
	private setConstantValues() {
		this.detailCardFields = DETAIL_CARD_FORM_DATA;
		this.navigateBackUrl = '/'+AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V2,'/',SelfAssessmentV2Routes.TABLE);
	}

	private setDetailData(){
		this.detailData = this.autoliquidacionsV2Service.dataStorageService.getItem(AUTOLIQUIDACIONS_V2_DETAIL_STORAGE) as IDetailData;

		if (this.detailData && this.idSelfAssessment === this.detailData.idSelfAssessment) {
			this.currentIndex = this.detailData.index
			this.totalRows = this.detailData.totalRows
		} else {
			this.autoliquidacionsV2Service.dataStorageService.deleteItem(AUTOLIQUIDACIONS_V2_DETAIL_STORAGE);
		}
	}

	getSelfAssessment() {
		this.detailEndpointsService.getSelfAssessment(this.idSelfAssessment)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				if (response?.content){
					this.selfAssessment = response.content;
		
					this.setCoreValues(this.selfAssessment);
				}
			});
	}
	
	private setCoreValues(data: ISelfAssessmentV2) {
		this.idReceipt = data.numJustificant;
		this.idSelfAssessment = data.idAutoliquidacio;
		
		this.stateChanges = this.setStateChanges(data?.canviEstat);

		this.downloadButtonsData = {
			// enable both buttons,isDisabled in pt-presentacions-mf works the other way around
			isDisabled: true,
			// disable receipt button
			isReceiptButtonDisabled: !this.allowReceiptStates.includes(data?.estat),
			// disable proceeding button
			isProceedingButtonDisabled: !this.allowProceedingStates.includes(data?.estat),
			idEntity: data?.encryptedIdMFPT || 'NO_ID'
		}
		
		this.showPresentationButtons = this.downloadButtonsModelList.includes(data?.model);

		this.stateIcon = data.estat ? this.autoliquidacionsV2Service.getStateIcon(data.estat,'detail') : '';
		this.stateLabel = data.estat ? `MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES.${data.estat}` : null;

		this.setTabsToDisplay(data);
	}

	private setStateChanges(stateChanges: CanviEstat[]): StateChangesTableRow[] {
		return stateChanges.map(state => {
			return {
				before: state.estatAnterior,
				current: state.estat,
				dateChange: state.dataCanvi,
				currentStateIcon: this.autoliquidacionsV2Service.getStateIcon(state.estat,'detail'),
				previousStateIcon: this.autoliquidacionsV2Service.getStateIcon(state.estatAnterior,'detail'),
			}
		}) || [];
	}

	private setTabsToDisplay(data: ISelfAssessmentV2) {
		this.detailTabInput = DETAIL_TAB_DATA.filter(
			tabData => {
				if(DETAIL_TABS_MODELS[tabData.tabType]) {					
					return DETAIL_TABS_MODELS[tabData.tabType]?.includes(data.model);
				}
				
				return true;
			}  
		);
		// Manejar caso especial para DECINF
		if (data.tipus === TypesDeclaration.DECINF) {
			const tramitacionsTab = DETAIL_TAB_DATA.find(t => t.tabType === TabType.procedures);
			
			this.detailTabInput = [
				...this.detailTabInput.filter(t => t.tabType !== TabType.payments),
				...(tramitacionsTab ? [tramitacionsTab] : [])
			];
		}
	}

	getSelfAssessmentList() {
		this.detailData.options.rows = 1;
		this.detailData.options.first = this.currentIndex;
		this.detailData.index = this.currentIndex;

		const request: SelfAssessmentV2Request = new SelfAssessmentV2Request(this.detailData.filterValue,this.detailData.options);

		this.autoliquidacionsV2EndpointsService.getSelfAssessmentList(request)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				if (response?.content?.results?.length > 0) {
					this.selfAssessment =  response?.content?.results[0];
					this.detailData.idSelfAssessment = this.selfAssessment?.idAutoliquidacio;
		
					this.setCoreValues(this.selfAssessment);
		
					this.autoliquidacionsV2Service.dataStorageService.setItem(AUTOLIQUIDACIONS_V2_DETAIL_STORAGE,this.detailData);
		
					this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V2,'/',SelfAssessmentV2Routes.DETAIL), this.idSelfAssessment]);
				}
		});
	}
	
	onShowErrors() {
		this.autoliquidacionsV2Service.showErrors(this.selfAssessment?.errors)
	}

	deleteModal() {
		if (this.autoliquidacionsV2Service.authService.getSessionStorageUser()?.rol !== UserRoles.ADMIN) return;

		const deleteData:DeleteModalData = {
			idReceipt:this.idReceipt,
			idSelfAssessment: this.idSelfAssessment,
			state: this.selfAssessment.estat,
			confirmFn: this.deleteSelfAssessment.bind(this)
		}

		this.autoliquidacionsV2Service.deleteModal(deleteData);
	}

	// Función que borra las autoliquidaciones
	private deleteSelfAssessment() {
		this.detailEndpointsService.deleteAssessment(this.idSelfAssessment)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response: ResponseDeleteAutoliquidacio) => {
				if(response?.content) this.navigateBackwards()
			})
	}

	navigateBackwards = () => this.router.navigate([this.navigateBackUrl]);
}
