<app-detail-page
	[pageTitle]="'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.PAGE_TITLE'"
	[statesTableTranslation]="'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.estat_peticio'"
	[detailCardTitle]="'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.CARD.TITLE'"

	[procedureData]="biometricSignature"
	[idEntity]="idEntity"
	[idTramit]="idEntity"
	[idReceipt]="idReceipt"

	[stateChangeFirstDate]="biometricSignature?.data_creacio"
	[stateChanges]="stateChanges"
	[stateIcon]="stateIcon"
	[stateLabel]="stateLabel"
	[detailCardFields]="detailCardFields"
	[detailTabInput]="detailTabInput"
	[customTabsContent]="{
		documentsTab: documentsTab
	}"

	[currentIndex]="currentIndex"
	[totalRows]="totalRows"
	[backUrl]="navigateBackUrl"
	[customButtonsTemplate]="resetButton"
	[navigationExtras]="navigationExtras"

	(getProcedureList)="getManagementList()"
	(showErrors)="onShowErrors()"
	(linkClicked)="onLinkClicked($event)"

	(incrementIndex)="currentIndex = currentIndex + 1"
	(decrementIndex)="currentIndex = currentIndex - 1"
>
</app-detail-page>

<ng-template #resetButton>
	<pt-button *ngIf="mode === TableMode.ACTIVES"
		[_label]="'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.RESET_BUTTON'"
		_icon="sli2-reload"
		_type="button"
		_class="p-button-primary"
		(_clickFn)="resetIntents()"
	>
	</pt-button>
</ng-template>

<ng-template #documentsTab>
	<app-bio-documents-tab [biometricSignature]="biometricSignature"></app-bio-documents-tab>
</ng-template>
