import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { LoginSimulatComponent } from './login-simulat.component';

const routes: Routes = [
	{
		path: '', component: LoginSimulatComponent,
		data: { 
			title: 'MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.PAGE_TITLE',
		}
	}
];

@NgModule({
	declarations: [
		LoginSimulatComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule,
		LazyElementsModule,
		RouterModule.forChild(routes)
	]
})
export class LoginSimulatModule { }
