import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { PtUiComponentsMfLibModule, UserRoles } from 'pt-ui-components-mf-lib';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';
import { MaintenanceConfComponent } from './maintenance-conf.component';
import { ModalMaintenanceConfComponent } from './components/modal-maintenance-conf/modal-maintenance-conf.component';

const routes: Routes = [
  {
    path: '',
    component: MaintenanceConfComponent,
    canActivate:[AuthGuardService],
    data: { 
      title: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.PAGE_TITLE' ,
      allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
    }
  }
];

@NgModule({
	declarations: [
		MaintenanceConfComponent,
		ModalMaintenanceConfComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule,
		LazyElementsModule,
		RouterModule.forChild(routes)
	]
})
export class MaintenanceConfModule { }
