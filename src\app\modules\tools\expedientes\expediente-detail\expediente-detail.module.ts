import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ExpedineteDetailComponent } from './expediente-detail.component';
import {
	LazyElementModuleOptions,
	LazyElementsModule,
} from '@angular-extensions/elements';
import { environment } from 'src/environments/environment';
import { PtSpinnerComponent } from 'pt-ui-components-mf-lib';

const routes: Routes = [
	{
		path: '',
		component: ExpedineteDetailComponent,
	},
];

const webcomponentsConfig: LazyElementModuleOptions = {
	elementConfigs: [
		{
			tag: 'aeat-conector-expediente-detail',
			url: environment.wcUrlAEATConectorJs,
			loadingComponent: PtSpinnerComponent,
			preload: true,
		},
	],
};

@NgModule({
	declarations: [ExpedineteDetailComponent],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule.forFeature(webcomponentsConfig),
	],
})
export class ExpedienteDetailModule {}
