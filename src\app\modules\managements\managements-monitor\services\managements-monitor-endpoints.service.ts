import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ManagementsMonitorRequest } from '../models/managements-monitor.model';
import { ManagementsMonitorResponse } from '../models/managements-monitor-endpoints.model';

@Injectable({
	providedIn: 'root'
})
export class ManagementsMonitorEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search autoliquidacions by criteria
	getManagementList(criteria?: ManagementsMonitorRequest | object): Observable<ManagementsMonitorResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlGestionsAdmin,
			url: "/llistat",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}
}