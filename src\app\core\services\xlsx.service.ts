import { Injectable } from '@angular/core';
import { PtMessageService } from 'pt-ui-components-mf-lib';

const EXCEL_EXTENSION = '.xlsx';

@Injectable({
	providedIn: 'root'
})
export class XlsxService {

	constructor(private msgService: PtMessageService) { 
		//...
	}

	saveFile(data: any, fileName:string, sheetName: string) {
		import("xlsx").then(XLSX => {
			// Creamos el fichero
			const ws = XLSX.utils.json_to_sheet(data);
			const wb = XLSX.utils.book_new();
			XLSX.utils.book_append_sheet(wb, ws, sheetName);

			// Guadamos el fichero
			XLSX.writeFile(wb, `${fileName}${EXCEL_EXTENSION}`);
		}).catch(() => {
			this.msgService.addMessages([{
				title: 'Error',
				severity: 'error',
				subtitle: 'MODULE_TRACKING.XLSX_ERROR'
			}])
		});
	}
}
