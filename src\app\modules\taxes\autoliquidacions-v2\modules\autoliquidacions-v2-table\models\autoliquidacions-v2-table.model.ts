import { PtValidations } from "pt-ui-components-mf-lib";
import { ISelfAssessmentV2Filter, PaymentMethodsV2, States, TaxStates } from "../../../models/autoliquidacions-v2.model";
import { Validators } from "@angular/forms";
import { FilterFieldType, FilterFieldsData } from "src/app/modules/shared/filter-inputs";
import { ExportColumnsData } from "src/app/core/models/export-table.model";
import { TypesDeclaration } from "../../autoliquidacions-v2-detail/models/autoliquidacions-v2-detail.model";

export enum TableColumns {
  numJustificant = 'numJustificant',
  impost = 'impost',
  tipus = 'tipus',
  nomPresentador = 'nomPresentador',
  nifPresentador = 'nifPresentador',
  nomSubjectePassiu = 'nomSubjectePassiu',
  nifSubjectePassiu = 'nifSubjectePassiu',
  nomTitular = 'nomTitular',
  nifTitular = 'nifTitular',
  model = 'model',
  idTramitacio = 'idTramitacio',
  idTramitacioPagament = 'idTramitacioPagament',
  presentacioEstat = 'presentacioEstat',
  pagamentEstat = 'pagamentEstat',
  dataModificacio = 'dataModificacio',
  dataAlta = 'dataAlta',
  estat = 'estat',
  mui = 'mui',
  stateRow = 'stateRow',
  tableActions = 'tableActions',
  totalIngressar = 'totalIngressar',
  tempsValidacio = 'tempsValidacio',
  tempsAgrupacio = 'tempsAgrupacio',
  tempsPresentacio = 'tempsPresentacio',
  tempsPagament = 'tempsPagament',
  idAgrupacio = 'idAgrupacio',
  codiErrors = 'errors',
  errors = 'errors'
}

export enum TableSortFields {
  idTramitacio = "presentacio.idTramitacio",
  idTramitacioPagament = "pagament.idTramitacio",
  totalIngressar = "totalIngressar",
  nifPresentador = "presentador.nifPresentador",
  nomPresentador = "presentador.nombrePresentador",
  nifSubjectePassiu ="subjectePassiu.nif",
  nomSubjectePassiu = "subjectePassiu.nom",
  stateRow = 'estat',
  nomTitular = 'presentador.nombreCompleto',
  nifTitular = 'presentador.nif',
}

export const EXPORT_COLUMNS_DATA: ExportColumnsData[] = [
  {
    id: TableColumns.numJustificant,
    columnTitle: "Num_Justificant",
    columnType: 'text'
  },
  {
    id: TableColumns.estat,
    columnTitle: "Estat",
    columnType: 'translation',
    translation: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'
  },
  {
    id: TableColumns.impost,
    columnTitle: "Impost",
    columnType: 'text',
  },
  {
    id: TableColumns.tipus,
    columnTitle: "Tipus",
    columnType: 'text',
  },
  {
    id: TableColumns.nomPresentador,
    columnTitle: "Nom_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.nifPresentador,
    columnTitle: "Nif_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.nomSubjectePassiu,
    columnTitle: "Nom_Subjecte_Passiu",
    columnType: 'text',
  },
  {
    id: TableColumns.nifSubjectePassiu,
    columnTitle: "Nif_Subjecte_Passiu",
    columnType: 'text',
  },
  {
    id: TableColumns.nomTitular,
    columnTitle: "Nom_Titular",
    columnType: 'text',
  },
  {
    id: TableColumns.nifTitular,
    columnTitle: "Nif_Titular",
    columnType: 'text',
  },
  {
    id: TableColumns.model,
    columnTitle: "Model",
    columnType: 'text',
  },
  {
    id: TableColumns.idTramitacio,
    columnTitle: "ID_Tramitacio",
    columnType: 'text',
  },
  {
    id: TableColumns.idTramitacioPagament,
    columnTitle: "ID_Tramitacio_Pagament",
    columnType: 'text',
  },
  {
    id: TableColumns.presentacioEstat,
    columnTitle: "Presentacio_Estat",
    columnType: 'text',
  },
  {
    id: TableColumns.pagamentEstat,
    columnTitle: "Pagament_Estat",
    columnType: 'text',
  },
  {
    id: TableColumns.totalIngressar,
    columnTitle: "Pagament_Estat",
    columnType: 'text',
  },
  {
    id: TableColumns.dataModificacio,
    columnTitle: "Data_Modificacio",
    columnType: 'date',
  },
  {
    id: TableColumns.dataAlta,
    columnTitle: "Data_Alta",
    columnType: 'date',
  },
  {
    id: TableColumns.tempsValidacio,
    columnTitle: "Temps_Validacio",
    columnType: 'text',
  },
  {
    id: TableColumns.tempsAgrupacio,
    columnTitle: "Temps_Agrupacio",
    columnType: 'text',
  },
  {
    id: TableColumns.tempsPresentacio,
    columnTitle: "Temps_Presentacio",
    columnType: 'text',
  },
  {
    id: TableColumns.tempsPagament,
    columnTitle: "Temps_Pagament",
    columnType: 'text',
  },
  {
    id: TableColumns.codiErrors,
    columnTitle: "Codi_Error",
    columnType: 'array',
    attr: 'technicalCode',
    arrayPos: -1
  },
  {
    id: TableColumns.errors,
    columnTitle: "Errors",
    columnType: 'errors'
  }
]

export enum FilterFormFields {
  dateFromForm = 'dateFromForm',
  dateToForm = 'dateToForm',
  estat = 'estat',
  numJustificant = 'numJustificant',
  nifPresentador = 'nifPresentador',
  nifSubjectePassiu = 'nifSubjectePassiu',
  impost = 'impost',
  model = 'model',
  mui = 'mui',
  idTramitacio = 'idTramitacio',
  idTramitacioPagament = 'idTramitacioPagament',
  presentacioEstat = 'presentacioEstat',
  pagamentEstat = 'pagamentEstat',
  errorsFilter = 'errorsFilter',
  haveComplementari = 'haveComplementari',
  tipusPagament = 'tipusPagament',
  nifTitular = 'nifTitular',
  idAgrupacio = "idAgrupacio",
  types = "types"
}
export type FilterFormFieldsT = keyof typeof FilterFormFields;

const FILTER_FIELDS_TRANSLATIONS = "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.FILTER.FIELDS";
export const FILTER_FORM_DATA:FilterFieldsData[][] = [
  [ 
    {
      id: FilterFormFields.dateFromForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateFromForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.dateToForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateToForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.estat,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.estat}`,
      fieldType: FilterFieldType.multiSelect,
      optionsValue: Object.entries(States),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'
    },
    {
      id: FilterFormFields.numJustificant,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.numJustificant}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.nifPresentador,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifPresentador}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.nifSubjectePassiu,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifSubjectePassiu}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.nifTitular,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifTitular}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.impost,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.impost}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(TaxStates)
    },
    {
      id: FilterFormFields.model,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.model}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.idTramitacio,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idTramitacio}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.idTramitacioPagament,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idTramitacioPagament}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.presentacioEstat,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.presentacioEstat}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(States),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'
    },
    {
      id: FilterFormFields.pagamentEstat,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.pagamentEstat}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(States),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'
    },
    {
      id: FilterFormFields.tipusPagament,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.tipusPagament}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(PaymentMethodsV2),
      optionsLabelTranslations: 'MODULE_TRACKING.PAYMENT_METHODS'
    },
    {
      id: FilterFormFields.idAgrupacio,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idAgrupacio}`,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.mui,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.mui}`,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.types,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.types}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(TypesDeclaration),
      optionsLabelTranslations: 'MODULE_TRACKING.TYPES'
    },
  ],
  [
    {
      id: FilterFormFields.errorsFilter,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.errorsFilter}`,
      fieldType: FilterFieldType.checkbox,
      options: [
        { id: 'errors', label: `${FILTER_FIELDS_TRANSLATIONS}.errors` },
      ]
    },
    {
      id: FilterFormFields.haveComplementari,
      containerClass: 'col-12 col-sm-3 col-md-3 col-lg-3 align-self-end',
      fieldType: FilterFieldType.triCheckbox,
      options: [
        // el valor que toma el componente pt-tri-checkbox es el id, por defecto null
        { id: null, label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.haveComplementari}` },
      ]
    }
  ]
]

export class FilterFormValue implements ISelfAssessmentV2Filter {
  dateFromForm: Date;
  dateToForm: Date;
  dateFrom: number;
  dateTo: number;
  idAutoliquidacio: string;
  idTramit: string;
  numJustificant: string;
  model: string;
  periode: string;
  exercici: string;
  declaracioExempcio: boolean;
  nifPresentador: string;
  nifSubjectePassiu: string;
  nifTitular: string;
  estat: string[];
  totalIngressar: number;
  idAgrupacio: string; 
  tipus: string;

  constructor(
    data?: ISelfAssessmentV2Filter
  ) {
    const dateFrom = data?.dateFromForm ? new Date(data.dateFromForm) : new Date();
    const dateTo = data?.dateToForm ? new Date(data.dateToForm) : new Date();
    if (!data?.dateFromForm) dateFrom.setDate(dateFrom.getDate() - 30);

    Object.assign(this, {
      ...data,
      dateFromForm: dateFrom,
      dateToForm: dateTo,
    });
  }
  
}

export interface ExportTempData {
  tempsValidacio: string,
  tempsAgrupacio: string,
  tempsPresentacio: string,
  tempsPagament: string,
}

export interface ExportStateChangeRow {
  currentState: string,
	date: Date
}