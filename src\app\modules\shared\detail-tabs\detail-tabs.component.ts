import { Component, OnInit, Input, TemplateRef } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { DetailTabsData, TabType, TabInput } from './models/detail-tabs.model';

@Component({
	selector: 'app-detail-tabs',
	templateUrl: './detail-tabs.component.html'
})
export class DetailTabsComponent implements OnInit {
	
	@Input() set detailTabsData(value: DetailTabsData) {
		this.setupTabsInput(value);
	};
	@Input() customTabsContent: {[key: string]: TemplateRef<any>} = {};
	
	// Css
	wcDocumentsUrlCss = environment.wcUrlDocumentsCss;
	wcPagamentsUrlCss = environment.wcUrlPagamentsCss;
	wcPresentacionsUrlCss = environment.wcUrlPresentacionsCss;
	
	// Enums
	TabType = TabType
	
	// Other
	tabsInput: TabInput[] = [];
	
	constructor(
		private webComponentService: WebComponentsService
	) {
		//...
	}

	ngOnInit(): void {
		this.setUpWebComponentsCss();
	}

	private setUpWebComponentsCss () {
		this.webComponentService.setWebComponentStyle(this.wcDocumentsUrlCss, 'documents');
		this.webComponentService.setWebComponentStyle(this.wcPagamentsUrlCss, 'pagaments');
		this.webComponentService.setWebComponentStyle(this.wcPresentacionsUrlCss, 'presentacions');
	}

	private setupTabsInput(detailData: DetailTabsData) {
		const idSelfAssessment = detailData.idSelfAssessment;
		const idTramitacio = detailData.idTramitacio;

		this.tabsInput = detailData.tabs.map(tab => {
			const idEntity = tab.tabType !== TabType.procedures ? idSelfAssessment : (idTramitacio || 'NO_ID');
			return {
				...tab,
				tabInput: {
					module: null,
					component: tab.tabType,
					service: null,
					data: {
						idEntity: idEntity || "NO_ID",
						reinjectClass: tab?.tabRetryButtonClass || ''
					}
				}
			}
		})
	}
}
