import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ManagementsMonitorRoutes } from 'src/app/core/models/config.model';

const routes: Routes = [
	{
		path: '', 
		redirectTo: ManagementsMonitorRoutes.TABLE, 
		pathMatch: 'full'
	},
	{
		path: ManagementsMonitorRoutes.TABLE,
		loadChildren: () => import(`./modules/managements-monitor-table/managements-monitor-table.module`).then(module => module.ManagementsMonitorTableModule),
	},
	{
		path: ManagementsMonitorRoutes.DETAIL_ID, 
		loadChildren: () => import(`./modules/managements-monitor-detail/managements-monitor-detail.module`).then(module => module.ManagementsMonitorDetailModule),
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class ManagementsMonitorRoutingModule { }