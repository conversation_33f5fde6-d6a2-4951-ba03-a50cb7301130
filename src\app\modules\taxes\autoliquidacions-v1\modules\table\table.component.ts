import { LazyLoadEvent } from 'primeng/api';
import {
	PtTableColumn, PtTableColumnTemplate, PtValidations
} from 'pt-ui-components-mf-lib';

import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { PtTableComponent } from 'pt-ui-components-mf-lib/lib/components/table/table.component';
import { TableExportService } from '../../../../../core/services/table-export.service';
import { DatePipe } from '@angular/common';
import { AutoliquidacionsV1EndpointsService } from '../../services/autoliquidacions-v1-endpoints.service';
import { AutoliquidacionsV1Service } from '../../services/autoliquidacions-v1.service';
import { AutoliquidacioRequest, AutoliquidacioRow, IAutoliquidacio, IAutoliquidacioFilters, IDetailData, States } from '../../models/autoliquidacions-v1.model';
import { AutoliquidacioResponse, AutoliquidacioResponseContent } from '../../models/autoliquidacions-v1-endpoints.model';
import { AppRoutes, SelfAssessmentV1Routes, StatesIcons, TaxesRoutes } from 'src/app/core/models/config.model';
import { EXPORT_COLUMNS_DATA, FILTER_FORM_DATA, FilterFormFields, FilterFormValue } from './models/table.model';
import { FilterModalInput, ModalFilterComponent } from 'src/app/modules/shared/modal-filter';

@Component({
	selector: 'app-autoliquidacions-v1-table',
	templateUrl: './table.component.html'
})

export class TableComponent implements OnInit {

	@ViewChild('dt', { static: true }) private readonly table: PtTableComponent;

	filterValue: Partial<IAutoliquidacioFilters>;

	// Table
	searchColumns: PtTableColumn[] = [];
	searchRows: AutoliquidacioRow[] = [];

	// Other
	options: LazyLoadEvent;
	paginationTotal: number = 0;

	private unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private endPointService: AutoliquidacionsV1EndpointsService,
		private router: Router,
		private autoliquidacioService: AutoliquidacionsV1Service,
		private exportService: TableExportService
	) { }

	ngOnInit(): void {
		this.setTableColumns();

		this.setFilterValue();
	}

	ngOnDestroy(): void {
		this.unsubscribe.next();
		this.unsubscribe.complete();
	}

	private setTableColumns() {

		this.searchColumns = [
			// { 
			// 	id: "originRow", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.ORIGIN", 
			// 	isResizable: true, isSortable: true, width: "90px", isVisible:false
			// },
			//{ id: "idComunication", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.COMUNICATION_ID", isResizable: true, isSortable: true, width: "100px" },
			{
				id: "idReceipt", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.RECEIPT",
				isResizable: true, isSortable: true, width: "7%"
			},
			// {
			// 	id: "idTramitacio", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.ID_TRAMITACIO",
			// 	isResizable: true, isSortable: true, width: "10%", isToggled: true
			// },
			{
				id: "stateRow", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.CURRENT_STATE",
				isResizable: true, isSortable: true, template: 'link', width: "10%",
				options: { removeOpacity: true, cellClass: 'justify-content-start' }
			},
			{
				id: "presentationState", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.PRES_STATE",
				isResizable: true, isSortable: true, template: 'link', width: "9%",
				options: { removeOpacity: true, cellClass: 'justify-content-start' }
			},
			{
				id: "agent.presenterNif", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.PRESENTATOR_NIF",
				isResizable: true, isSortable: true, width: "7%"
			},
			{
				id: "presentador.nombreCompleto", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.PRESENTATOR_NAME",
				isResizable: true, isSortable: true, width: "8%"
			},
			{
				id: "subjectePassiu.document", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.SP_NIF",
				isResizable: true, isSortable: true, width: "7%"
			},
			{
				id: "passiveSubject.name", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.SP_NAME",
				isResizable: true, isSortable: true, width: "8%"
			},
			{
				id: "model", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.MODEL",
				isResizable: true, isSortable: true, width: "5%"
			},
			{
				id: "complementedNumJustificant", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.COMPLEMENTARY",
				isResizable: true, isSortable: true, width: "9%", template: PtTableColumnTemplate.boolean,
				options: {
					translation: 'MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE'
				},
			},
			// { 
			// 	id: "protocol", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.PROTOCOL", 
			// 	isResizable: true, isSortable: true, width: "100px" , isVisible:false
			// },
			// { 
			// 	id: "notaryName", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.NOTARY_NAME", 
			// 	isResizable: true, isSortable: true, width: "100px" , isVisible:false
			// },
			// { 
			// 	id: "idMFPT", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.MFPT_ID", 
			// 	isResizable: true, isSortable: true, width: "85px" , isVisible:false
			// },
			{
				id: "amount", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.AMOUNT",
				isResizable: true, isSortable: true, template: 'currency', width: "6%"
			},
			{
				id: "clauCobramentMUI.referencia", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.MUI",
				isResizable: true, isSortable: true, width: "6%"
			},
			{
				id: "paymentMethod", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.PAYMENT_METHOD",
				isResizable: true, isSortable: true, template: "translate", width: "8%", isToggled: true,
				options: {
					translation: 'MODULE_TRACKING.PAYMENT_METHODS'
				}
			},
			{
				id: "agent.nif", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.TITULAR_NIF",
				isResizable: true, isSortable: true, width: "7%",isToggled: true,
			},
			{
				id: "agent.name", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.TITULAR_NAME",
				isResizable: true, isSortable: true, width: "8%",isToggled: true,
			},
			{
				id: "loadDate", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.DATE",
				isResizable: true, isSortable: true, width: "7%", template: "date",
				options: { dateFormat: 'dd/MM/yyyy HH:mm' }
			},
			//{ id: "attachedDocument", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.FILE", isResizable: true, isSortable: true, width: "100px" },
			{
				id: 'tableActions', label: null, isResizable: false, isSortable: false,
				template: "buttons", width: '2%'
			}
			//{ id: "mui", label: "MODULE_TRACKING.COMPONENT_LIQUIDACIO.TABLE.MUI_REFERENCE", isResizable: true, isSortable: true, width: "100px" },
		];
	}

	private setFilterValue() {
		const data: IAutoliquidacioFilters = this.autoliquidacioService.dataStorageService.getItem("filterData");

		this.filterValue = new FilterFormValue(data);
	}

	openFilterModal() {
		// Open delete modal
		const modalRef = this.autoliquidacioService.modalService.open(
			ModalFilterComponent,
			{ size: 'xl', windowClass: '' }
		);

		const modalInput: FilterModalInput = this.setModalInput();
		modalRef.componentInstance.modalInput = modalInput;

		modalRef.componentInstance.modalOutput.pipe(takeUntil(this.unsubscribe)).subscribe(
			(filterFormValue: IAutoliquidacioFilters) => {
				this.filterValue = filterFormValue;

				this.table.table.reset();
			}
		);
	}

	private setModalInput (): FilterModalInput {
		return {
			formValue: this.filterValue,
			formResetValue: new FilterFormValue(),
			formFieldsData: FILTER_FORM_DATA,
			formGroupValidations: [
				PtValidations.equalsOrLessThan(FilterFormFields.loadDateTo, FilterFormFields.loadDateFrom, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan'),
				PtValidations.equalsOrGreaterThan(FilterFormFields.loadDateFrom, FilterFormFields.loadDateTo, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan'),
			]
		}
	}

	/**
	 * Search
	 * @description Search autoliquidacions by criteria
	 */
	search = async (event: LazyLoadEvent): Promise<void> => {
		// Reset previous data
		this.searchRows = [];
		this.paginationTotal = 0;

		if (!event.sortField){
			event.sortField = "loadDate";
			event.sortOrder = -1;
		}

		this.autoliquidacioService.dataStorageService.setItem("filterData", this.filterValue);

		// Request
		this.options = event;
		const request: AutoliquidacioRequest = new AutoliquidacioRequest(this.filterValue, event)
		this.endPointService.getSelfAssessmentList(request)
			.pipe(takeUntil(this.unsubscribe))
			.subscribe((response: AutoliquidacioResponse) => {
				if (response.content) {
					this.setTableRows(response.content);
				}
			});
	}

	/**
	 * Table rows
	 * @description Map the table rows
	 */
	private setTableRows(response: AutoliquidacioResponseContent) {
		this.paginationTotal = response.total;

		this.searchRows = response.results?.map((autoliquidacio: IAutoliquidacio, id: number) => {
			const presType = this.presentationState(autoliquidacio);
			const stateIcon = this.autoliquidacioService.getStateIcon(autoliquidacio?.state, 'self-assessment');
			const presTypeIcon = this.autoliquidacioService.getStateIcon(presType, 'self-assessment');
			const passiveSubject = autoliquidacio?.passiveSubject;

			// Map autoliquidacio entity into the autoliquidacio table row
			return Object.assign(
				{},
				// Default autoliquidacio entity
				autoliquidacio,
				// Table row model
				{
					'agent.name': autoliquidacio?.agent?.name,
					'agent.nif': autoliquidacio?.agent?.nif,
					'presentador.nombreCompleto': autoliquidacio?.agent?.presenterName,
					'agent.presenterNif': autoliquidacio?.agent?.presenterNif,
					'passiveSubject.name': passiveSubject?.length > 0 && passiveSubject[0]?.name,
					'subjectePassiu.document': passiveSubject?.length > 0 && passiveSubject[0]?.nif,
					'clauCobramentMUI.referencia': autoliquidacio?.clauCobramentMUI?.referencia,
					originRow: this.autoliquidacioService.mapSourceOrigin.get(autoliquidacio?.origin),
					notaryName: autoliquidacio?.notary?.NOMBRE,
					complementedNumJustificant: autoliquidacio?.complementedNumJustificant ? true : false,
					stateRow: {
						id: `button${autoliquidacio.idReceipt}`,
						icon: stateIcon,
						iconPos: 'left',
						label: autoliquidacio.state ? this.autoliquidacioService.translateService.instant(`MODULE_TRACKING.STATES.${autoliquidacio.state}`) : "",
						clickFn: this.showDetailError.bind(this, autoliquidacio),
						disabled: !this.isStateError(stateIcon)
					},
					presentationState: {
						id: `button${autoliquidacio.idReceipt}0`,
						icon: presTypeIcon,
						iconPos: 'left',
						label: this.autoliquidacioService.translateService.instant(`MODULE_TRACKING.STATES.${presType}`),
						clickFn: () => { },
						disabled: true
					},
					tableActions: [{
						id: `detail${autoliquidacio.idReceipt}`,
						icon: 'sli2-eye',
						class: 'p-button-text',
						componentType: 'button',
						clickFn: this.detailNav.bind(this, autoliquidacio, id)
					}],
				}
			);
		});
	}

	// Estado de la presentacion
	presentationState = (aut: IAutoliquidacio): States => this.exportService.presentationState(aut);

	private showDetailError = (presentacio: AutoliquidacioRow) => this.autoliquidacioService.showErrors(presentacio?.errors);

	private isStateError = (icon: string): boolean => icon === StatesIcons.ERROR;

	/**
	 * Navigate: Detail
	 * @description Navigate to the detail page of a specific "Autoliquidacio"
	 */
	private detailNav (row: AutoliquidacioRow,id: number) {
		const detailData: IDetailData = {
			filterValue : this.filterValue,
			totalRows: this.paginationTotal,
			options: this.options,
			index: id + this.options?.first,
			idSelfAssessment: row?.idSelfAssessment
		}

		this.autoliquidacioService.dataStorageService.setItem("detailData", detailData);

		this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V1,'/',SelfAssessmentV1Routes.DETAIL), row.idSelfAssessment]);
	}

	export = (): void => {
		// Listamos todas las autoliquidaciones sin paginar
		const request: AutoliquidacioRequest = new AutoliquidacioRequest(this.filterValue, {})
		this.endPointService.getSelfAssessmentList(request)
			.pipe(takeUntil(this.unsubscribe))
			.subscribe((response: AutoliquidacioResponse) => {
				if (response?.content?.results) {
					// Exportamos a excel
					var datePipe = new DatePipe('en-US');
					let date = datePipe.transform(new Date(), 'yyyy-MM-dd_HH:mm');
					this.exportService.export2Excel(response?.content?.results, EXPORT_COLUMNS_DATA, "Export_" + date,'Autoliquidacions');
				}
			});

	}
}
