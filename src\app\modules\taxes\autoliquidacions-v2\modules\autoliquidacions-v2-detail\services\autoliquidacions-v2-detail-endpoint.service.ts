import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ResponseDeleteAutoliquidacio, ResponseGetSelfAssessmentV2 } from '../models/autoliquidacions-v2-detail-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class AutoliquidacionsV2DetailEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: GET > Get self assessment by id
	getSelfAssessment(idSelfAssessment: string): Observable<ResponseGetSelfAssessmentV2> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/v2/autoliquidacio/${idSelfAssessment}`,
			method: 'get'
		}
		return this.httpService.get(requestOptions);
	}

	deleteAssessment(id: string): Observable<ResponseDeleteAutoliquidacio> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/v2/autoliquidacio/${id}`,
			method: 'delete'
		}
		return this.httpService.delete(requestOptions);
	}
}