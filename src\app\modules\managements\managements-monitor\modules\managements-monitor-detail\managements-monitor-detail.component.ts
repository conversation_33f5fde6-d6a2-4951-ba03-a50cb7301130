import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { 
	ManagementsMonitorRequest, 
	MANAGEMENTS_MONITOR_DETAIL_STORAGE, 
	CanviEstat, 
	IManagementsMonitor
} from '../../models/managements-monitor.model';
import { AppRoutes, ManagementRoutes, ManagementsMonitorRoutes } from 'src/app/core/models/config.model';
import { DETAIL_CARD_FORM_DATA, DETAIL_TAB_DATA, DetailCardFields, IDetailData, MANAGEMENTS_TABS_TO_EXCLUDE, Managements } from './models/managements-monitor-detail.model';
import { takeUntil } from 'rxjs/operators';
import { ManagementsMonitorDetailEndpointsService } from './services/managements-monitor-detail-endpoint.service';
import { ManagementsMonitorService } from '../../services/managements-monitor.service';
import { ManagementsMonitorEndpointsService } from '../../services/managements-monitor-endpoints.service';
import { DetailCardData } from '../../../../shared/detail-card';
import { StateChangesTableRow } from '../../../../shared/detail-state-table';
import { TabData } from '../../../../shared/detail-tabs';

@Component({
	selector: 'app-resolucions-detail',
	templateUrl: './managements-monitor-detail.component.html'
})
export class ManagementsMonitorDetailComponent implements OnInit {

	// Procedure Data
	management: IManagementsMonitor;
	idEntity: string;
	idReceipt: string;

	// Detail data
	stateChanges: StateChangesTableRow[] = [];
	detailData: IDetailData;
	stateIcon: string;
	stateLabel: string;
	detailCardFields:DetailCardData[] = [];
	detailTabInput: TabData[] =[];
	
	// Navegation Data
	currentIndex:number;
	totalRows:number;	
	navigateBackUrl: string;
	
	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		private managementsMonitorEndpointsService: ManagementsMonitorEndpointsService,
		private detailEndpointsService: ManagementsMonitorDetailEndpointsService,
		private router: Router,
		private aRoute: ActivatedRoute,
		private managementsMonitorService: ManagementsMonitorService
	) {
		this.idEntity = this.aRoute.snapshot.params.id;
	}

	ngOnInit(): void {
		this.setConstantValues();

		this.setDetailData();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();

		this.managementsMonitorService.dataStorageService.deleteItem(MANAGEMENTS_MONITOR_DETAIL_STORAGE);
	}
	
	private setConstantValues() {
		this.detailCardFields = DETAIL_CARD_FORM_DATA;
		this.navigateBackUrl = '/'+AppRoutes.MANAGEMENTS.concat('/',ManagementRoutes.MONITOR,'/',ManagementsMonitorRoutes.TABLE);
	}

	private setDetailData(){
		this.detailData = this.managementsMonitorService.dataStorageService.getItem(MANAGEMENTS_MONITOR_DETAIL_STORAGE) as IDetailData;

		if (this.detailData && this.idEntity === this.detailData.idEntity) {
			this.currentIndex = this.detailData.index
			this.totalRows = this.detailData.totalRows
		} else {
			this.managementsMonitorService.dataStorageService.deleteItem(MANAGEMENTS_MONITOR_DETAIL_STORAGE);
		}
	}

	getManagement() {
		this.detailEndpointsService.getManagement(this.idEntity)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				if (response?.content){
					this.updateManagementData(response.content);
					this.setCoreValues(this.management);
				}
			});
	}

	private setCoreValues(data: IManagementsMonitor) {
		this.idReceipt = data.id;
		this.idEntity = data.idGestio || data.id;

		this.stateChanges = this.setStateChanges(data.canviEstat);
		this.detailTabInput = this.setTabData(data.tramit as Managements);

		this.stateIcon = data.estat ? this.managementsMonitorService.getStateIcon(data.estat,'detail') : '';
		this.stateLabel = data.estat ? `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.STATES.${data.estat}` : null;
	}

	private setStateChanges(stateChanges: CanviEstat[]): StateChangesTableRow[] {
		return stateChanges?.map(state => {
			return {
				before: state.estatAnterior,
				current: state.estat,
				dateChange: state.dataCanvi,
				currentStateIcon: this.managementsMonitorService.getStateIcon(state.estat,'detail'),
				previousStateIcon: this.managementsMonitorService.getStateIcon(state.estatAnterior,'detail'),
			}
		}) || [];
	}
	
	private setTabData(management: Managements) {
		return DETAIL_TAB_DATA.filter((tab) =>
			!MANAGEMENTS_TABS_TO_EXCLUDE[management]?.includes(tab.tabType)
		);
	}

	getManagementList() {
		this.detailData.options.rows = 1;
		this.detailData.options.first = this.currentIndex;
		this.detailData.index = this.currentIndex;

		const request: ManagementsMonitorRequest = new ManagementsMonitorRequest(this.detailData.filterValue,this.detailData.options);

		this.managementsMonitorEndpointsService.getManagementList(request)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				const results: IManagementsMonitor[] = response?.content?.results;
				
				if (results.length > 0) {
					this.updateManagementData(results[0]);
					this.detailData.idEntity = this.management?.id;
		
					this.setCoreValues(this.management);
		
					this.managementsMonitorService.dataStorageService.setItem(MANAGEMENTS_MONITOR_DETAIL_STORAGE,this.detailData);
					
					this.router.navigate(['/' + AppRoutes.MANAGEMENTS.concat('/',ManagementRoutes.MONITOR,'/',ManagementsMonitorRoutes.DETAIL), this.management?.id]);
				}
		});
	}
	
	onShowErrors() {
		this.managementsMonitorService.showErrors(this.management?.dadesAddicionals, this.management?.errors)
	}

	private updateManagementData(response: IManagementsMonitor){
		response.tramit = this.managementsMonitorService.tramitTranslation(response.tramit);
		this.management = response;
	}
}
