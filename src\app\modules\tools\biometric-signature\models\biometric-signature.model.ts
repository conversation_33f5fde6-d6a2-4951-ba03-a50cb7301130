export const BIOMETR<PERSON>_SIGNATURE_FILTER_STORAGE = 'biometric-signature-filter-storage';
export const BIOMETRIC_SIGNATURE_DETAIL_STORAGE = 'biometric-signature-detail-storage';

export interface Metadades {
	clau: string;
	valor: string;
}

export interface ErrorDTO {
	error_missatge: string;
	data_creacio: Date | string;
}

export interface DispositiuDTO {
	nom: string;
	descripcio: string;
}

export interface DocumentDTO {
	id: string;
	nom: string;
	contentBase64: string;
	estat: string;
	idVidSigner: string;
	idRepositoriDocumental: string;
	dataCreacio: string;
	dataActualitzacio: string
}

export interface SignedDocumentDTO {
	nomDocument: string;
	contentBase64: string;
	estatPeticio: EstatPeticioENUM;
	motiuRebuig: string
}

export interface EstatHistorialDTO {
	data: string;
	estat: EstatPeticioENUM;
}

export enum EstatPeticioENUM {
	ENVIAT="ENVIAT",
	SIGNAT="SIGNAT",
	ERROR_PETICIO="ERROR_PETICIO",
	ERROR_NOTIFICACIO="ERROR_NOTIFICACIO",
	ERROR_REPOSITORI_DOCUMENTAL="ERROR_REPOSITORI_DOCUMENTAL",
	ERROR_HISTORIFICACIO="ERROR_HISTORIFICACIO",
	CANCELAT="CANCELAT",
	CADUCAT="CADUCAT",
	REBUTJAT="REBUTJAT",
	PENDENT="PENDENT",
	NOTIFICAT="NOTIFICAT"
}

export enum TipusSignaturaENUM {
	EMAILANDSMS="EMAILANDSMS",
	BIO="BIO",
	MOBILE="MOBILE",
	SMARTCARD="SMARTCARD",
	STAMP="STAMP",
}

export enum DocStatusENUM {
	CREATED="CREATED",
	SENT="SENT",
	SIGNED="SIGNED",
	CANCELLED="CANCELLED",
	EXPIRED="EXPIRED",
	ERROR="ERROR",
	REJECTED="REJECTED",
	PENDING="PENDING",
	UNSIGNED="UNSIGNED",
}

export enum RepositoriDocumentalENUM {
	PADOCT="PADOCT",
	NONE="NONE",
}

export enum AplicacioENUM {
	GAUDI="GAUDI",
	SRC="SRC",
	PORTAL="PORTAL",
}

export enum States {
	GENERAT="GENERAT",
	ENVIAT="ENVIAT",
	SIGNAT="SIGNAT",
	ERROR_PETICIO="ERROR_PETICIO",
	ERROR_NOTIFICACIO="ERROR_NOTIFICACIO",
	ERROR_HISTORIFICACIO="ERROR_HISTORIFICACIO",
	CANCELAT="CANCELAT",
	CADUCAT="CADUCAT",
	REBUTJAT="REBUTJAT",
	PENDENT="PENDENT",
	NOTIFICAT="NOTIFICAT"
}
export type StatesT = keyof typeof States;
export type StatesValues<States> = States[keyof States];

export interface IBiometricSignature {
	anchor: string;
	aplicacio: AplicacioENUM;
	content_base64: string;
	data_actualitzacio: Date;
	data_creacio: Date;
	errors: ErrorDTO[];
	estat_peticio: EstatPeticioENUM;
	historial_estats: EstatHistorialDTO[];
	id: string,
	id_repositori_documental: string;
	id_vid_signer: string;
	identificador_signant: string;
	metadades: Metadades[];
	nom_dispositiu: string;
	nom_document: string;
	nom_signant: string;
	reintents: number;
	repositori_documental: string;
	tipus_identificador_signant: string;
	tipus_signatura: TipusSignaturaENUM;
}

export interface CanviEstat {
	estatAnterior: string,
	estat: string,
	dataCanvi: Date
}
