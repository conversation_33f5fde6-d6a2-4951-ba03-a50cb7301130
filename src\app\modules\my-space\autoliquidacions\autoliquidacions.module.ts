// Core
import { Routes, RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { AutoliquidacionsComponent } from './autoliquidacions.component';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';
import { UserRoles } from 'pt-ui-components-mf-lib';

const routes: Routes = [
	{
		path: '',
		component: AutoliquidacionsComponent,
		canActivate: [AuthGuardService],
		data: {
			title: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_AUTOLIQUIDACIONS.PAGE_TITLE',
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
			isElementVisible: false
		},
	}
];

@NgModule({
	declarations: [
		AutoliquidacionsComponent
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	]
})
export class AutoliquidacionsModule { }
