import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { ToolsRoutes } from 'src/app/core/models/config.model';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: ToolsRoutes.MAINTENANCE_CONF,
		loadChildren: () =>
			import(`./maintenance-conf/maintenance-conf.module`).then(
				(module) => module.MaintenanceConfModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
		}
	},
	{
		path: ToolsRoutes.NIF_ENCRYPTION,
		loadChildren: () =>
			import(`./nif-encriptacio/nif-encriptacio.module`).then(
				(module) => module.NifEncriptacioModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
		}
	},
	{
		path: ToolsRoutes.NOTICES_CONF,
		loadChildren: () =>
			import(`./notifications/notifications.module`).then(
				(module) => module.NotificationsModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
		}
	},
	{
		path: ToolsRoutes.SARCAT_MIDDLEWARE,
		loadChildren: () =>
			import(`./sarcat-middleware/sarcat-middleware.module`).then(
				(module) => module.SarcatMiddlewareModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
		}
	},
	{
		path: ToolsRoutes.EXPEDIENTES,
		loadChildren: () =>
			import(`./expedientes/expedientes-routing.module`).then(
				(module) => module.ExpedientesRoutingModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN, UserRoles.INSPECTOR]
		}
	},
	{
		path: ToolsRoutes.BIOMETRIC_SIGNATURE,
		loadChildren: () =>
			import(`./biometric-signature/biometric-signature-routing.module`).then(
				(module) => module.BiometricSignatureRoutingModule
			),
		canActivate:[AuthGuardService],
		data: { 
			allowRoles: [UserRoles.ADMIN]
		}
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class ToolsRoutingModule {}
