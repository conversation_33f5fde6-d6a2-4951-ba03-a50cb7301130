import { HTTP_INTERCEPTORS, HttpClient, HttpClientModule } from '@angular/common/http';
import { APP_INITIALIZER, CUSTOM_ELEMENTS_SCHEMA, LOCALE_ID, NgModule } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

// Locale
import { CommonModule, registerLocaleData } from '@angular/common';
import localeCa from '@angular/common/locales/ca';
registerLocaleData(localeCa, 'ca');

// Translations
import { TranslateLoader, TranslateModule, TranslateService } from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
export function HttpLoaderFactory(httpClient: HttpClient) {
	return new MultiTranslateHttpLoader(httpClient, [
		{ prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
		{ prefix: `${environment.baseUrCommons}/i18n/`, suffix: '.json' }
	]);
}
export function appInitializerFactory(translate: TranslateService) {
	return () => {
    translate.addLangs(['ca', 'es']);
    translate.setDefaultLang('ca');

    if (window.location.href.includes('/es/')) {
      return translate.use('es');
    }

    return translate.use('ca');
  };
}

// Ui Components library
import {
  NAME_USER_STORAGE,
  PtHttpInterceptorService,
  PtSpinnerComponent,
  PtUiComponentsMfLibModule,
} from 'pt-ui-components-mf-lib';

// App
import { LazyElementModuleOptions, LazyElementsModule } from '@angular-extensions/elements';
import { CookieService } from 'ngx-cookie-service';
import { environment } from 'src/environments/environment';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { NavbarModule } from './core/navbar/navbar.module';

// Webcomponents configuration

const webcomponentsConfig: LazyElementModuleOptions = {
    elementConfigs: [
		{ tag: 'mf-dispatcher', url: environment.wcUrlDispatcherJs, loadingComponent: PtSpinnerComponent, preload: true },
		{ tag: 'mf-documents', url: environment.wcUrlDocumentsJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-pagaments', url: environment.wcUrlPagamentsJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-presentacions', url: environment.wcUrlPresentacionsJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-seguretat', url: environment.wcUrlSeguretatJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-el-meu-espai', url: environment.wcUrlElMeuEspaiJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-pagament-liquidacions', url: environment.wcUrlPagamentLiquidacioJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'mf-tearc-monitor', url: environment.wcUrlTearcMonitorJs, loadingComponent: PtSpinnerComponent, preload: false },
		{ tag: 'sarcat-middleware', url: environment.wcUrlSarcatMiddlewareJs, loadingComponent: PtSpinnerComponent, preload: false },
  	]
};
@NgModule({
	declarations: [
		AppComponent
	],
	imports: [
		BrowserModule,
		BrowserAnimationsModule,
		CommonModule,
		HttpClientModule,
		TranslateModule.forRoot({
			loader: {
				provide: TranslateLoader,
				useFactory: HttpLoaderFactory,
				deps: [HttpClient]
			}
		}),
		ReactiveFormsModule,
		PtUiComponentsMfLibModule,
		NavbarModule,
		LazyElementsModule.forFeature(webcomponentsConfig),
		AppRoutingModule
	],
	schemas: [CUSTOM_ELEMENTS_SCHEMA],
	providers: [
		{ provide: LOCALE_ID, useValue: 'ca-ES' },
		{ provide: NAME_USER_STORAGE, useValue: 'pt-user-t' },
		{ provide: HTTP_INTERCEPTORS, useClass: PtHttpInterceptorService, multi: true },
		CookieService,
		{	// Initialize app when the translations are loaded
			provide: APP_INITIALIZER,
			useFactory: appInitializerFactory,
			deps: [TranslateService],
			multi: true
		}
	],
	bootstrap: [AppComponent]
})
export class AppModule { }
