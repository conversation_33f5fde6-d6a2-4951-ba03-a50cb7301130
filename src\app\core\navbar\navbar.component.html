<!-- Menu -->
<nav class="pt-navbar">
	<div class="pt-navbar-header d-flex justify-content-between align-items-center">
		<ng-template #content>
			<div class="profile-container">
				<!-- User image -->
				<div class="profile-img">
					<em class="sli2-user"></em>
				</div>
				<div class="profile-info">
					<!-- User name -->
					<div>
						<span *ngIf="data.nombre" class="profile-name">{{data.nombre}}</span>
						<span *ngIf="data.apellido1" class="profile-name">{{data.apellido1}}</span>
						<span *ngIf="data.apellido2" class="profile-name">{{data.apellido2}}</span>
					</div>
					<!-- NIF -->
					<div class="mb-2">
						<h5 *ngIf="data.nif" class="value">{{data.nif}}</h5>
					</div>
					<!-- Role -->
					<div class="mb-2">
						<h5 class="text">{{'MODULE_TRACKING.NAVBAR.ROLE' | translate}}</h5>
						<h5 *ngIf="data.rol" class="value">{{rol}}</h5>
					</div>
					<!-- Last connected -->
					<div>
						<h5 class="text">{{'MODULE_TRACKING.NAVBAR.LAST_SEEN' | translate}}</h5>
						<h5 *ngIf="data.fechaUltimaConexion" class="value">{{data.fechaUltimaConexion | date:'dd/MM/yyyy HH:mm:ss' }}</h5>
					</div>
				</div>
			</div>
		</ng-template>

		<div class="pt-navbar-logo">
			<img src="assets/icons/gencat-nuevo-completo-w.svg" alt= "GENCAT logo" class="p-mr-2">
			<span class="ml-4 mr-4 align-middle h5"
				[translate]="'MODULE_TRACKING.COMPONENT_LIQUIDACIO.MENU_TITLE'"></span>
		</div>
		<div class="">
			<em placement="bottom-right" [ngbPopover]="content" popoverClass="user-info-popover" class="sli2-user"></em>
		</div>
	</div>

	<p-menubar styleClass="pt-menu" [model]="menuItems" [autoDisplay]="true">
	</p-menubar>
</nav>