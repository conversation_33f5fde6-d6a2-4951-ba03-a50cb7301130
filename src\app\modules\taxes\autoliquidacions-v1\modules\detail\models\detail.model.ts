import { LazyLoadEvent } from "primeng/api";
import { DetailCardData, DetailFieldType } from "src/app/modules/shared/detail-card";
import { TabData, TabType } from "src/app/modules/shared/detail-tabs";
import { IAutoliquidacio } from "../../../models/autoliquidacions-v1.model";

export interface IDetailData {
	filterValue: Partial<IAutoliquidacio>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idSelfAssessment?: string
}

export enum DetailCardFields {
  loadDate = "loadDate",
  amount = "amount",
  model = "model",
  origin = "origin",
  idSelfAssessment = "idSelfAssessment",
  idReceipt = "idReceipt",
  presentationId = "presentationId",
  idModel = "idModel",
  state = "state",
  notarialFileReference = "notarialFileReference",
  idTramitacio = "idTramitacio"
}
export type DetailCardFieldsT = keyof typeof DetailCardFields;

const DETAIL_CARD_TRANSLATIONS = "MODULE_TRACKING.COMPONENT_LIQUIDACIO.DETAIL_MODAL.FIELDS";
export const DETAIL_CARD_FORM_DATA:DetailCardData[] = [
  {
    id: DetailCardFields.idSelfAssessment,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idSelfAssessment}`
  },
  {
    id: DetailCardFields.model,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.model}`,
    fieldContainerClass: 'col-2 col-sm-2 col-md-2 col-lg-2'
  },
  {
    id: DetailCardFields.loadDate,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.loadDate}`,
    fieldContainerClass: 'col-3 col-sm-3 col-md-3 col-lg-3'
  },
  {
    id: DetailCardFields.state,
    fieldType: DetailFieldType.state,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.state}`,
    fieldContainerClass: 'col-3 col-sm-3 col-md-3 col-lg-3'
  },
  {
    id: DetailCardFields.idReceipt,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idReceipt}`
  },
  {
    id: DetailCardFields.amount,
    fieldType: DetailFieldType.number,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.amount}`,
    fieldNumberMode: 'currency',
    fieldContainerClass: 'col-2 col-sm-2 col-md-2 col-lg-2'
  },
  {
    id: DetailCardFields.idTramitacio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idTramitacio}`,
    fieldContainerClass: 'col-3 col-sm-3 col-md-3 col-lg-3'
  },
  {
    id: DetailCardFields.notarialFileReference,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.notarialFileReference}`
  }
]

export type DetailTabsModel = {
  [K in TabType]?: string[];
}

export const DETAIL_TABS_MODELS:DetailTabsModel  = {
  [TabType.documents]: ["600","920","940","950"],
  [TabType.payments]: ["600","920","940","950"],
  [TabType.validations]: ["600"],
  [TabType.grouping]: ["600"],
  [TabType.notifications]: ["600"],
  [TabType.presentations]: ["600","920","940","950"],
  [TabType.procedures]: ["920","940","950"]
}

const DETAIL_TABS_TRANSLATIONS = "MODULE_TRACKING.COMPONENT_LIQUIDACIO.DETAIL_MODAL"
export const DETAIL_TAB_DATA: TabData[] = [
  {
    tabType: TabType.documents,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
  },
  {
    tabType: TabType.validations,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TAB_TITLE_VALIDATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.grouping,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TAB_TITLE_GROUPING`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.payments,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.PAYMENT_DETAIL`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.notifications,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TAB_TITLE_NOTIFICATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.presentations,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TAB_TITLE_PRESENTATIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
  {
    tabType: TabType.procedures,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TAB_TITLE_TRAMITACIONS`,
    tabRetryButtonClass: "p-button-primary"
  }
]
