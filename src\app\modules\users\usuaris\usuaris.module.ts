// Core
import { Routes, RouterModule } from '@angular/router';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { UsuarisComponent } from './usuaris.component';

const routes: Routes = [
	{
		path: '', component: UsuarisComponent,
		data: { 
			title: '',
			isElementVisible: false
		}
	}
];

@NgModule({
	declarations: [
		UsuarisComponent
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	]
})
export class UsuarisModule { }
