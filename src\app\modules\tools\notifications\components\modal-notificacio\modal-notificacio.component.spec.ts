import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ModalNotificacioComponent } from './modal-notificacio.component';

describe('ModalNotificacioComponent', () => {
  let component: ModalNotificacioComponent;
  let fixture: ComponentFixture<ModalNotificacioComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ModalNotificacioComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ModalNotificacioComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
