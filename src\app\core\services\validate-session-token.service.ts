import { Injectable } from '@angular/core';
import { PtAuthService } from 'pt-ui-components-mf-lib';

@Injectable({
	providedIn: 'root'
})
export class ValidateSessionTokenService {

	constructor(private authService: PtAuthService) { }

	isValidToken(): boolean {
		const tokenExpiration = new Date(this.authService.getSessionStorageUser()?.tokenExpiration)
		
		return tokenExpiration > new Date();
	} 
}
