import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { PtMessageI, PtTableColumn } from 'pt-ui-components-mf-lib';
import { Metadades } from '../../models/biometric-signature.model';

@Component({
  selector: 'app-metadades-modal.component.ts',
  templateUrl: './metadades-modal.component.html',
})
export class MetadadesModalComponent implements OnInit {

	@Input() metadades: Metadades[] = [];

	// Modal
	modalData: PtMessageI;

	// Table
	tableColumns: PtTableColumn[] = [];

  constructor(
		private activeModal: NgbActiveModal,
	) { }

  ngOnInit(): void {
		this.setConfirmationData();
		this.setTableColumns();
  }

	private setConfirmationData = (): void => {
		this.modalData = {
			severity: 'info',
			title: 'Metadades',
			subtitle: null,
			closable: true,
			closableFn: this.closeModal.bind(this)
		}
	}

	private setTableColumns = (): void => {
		this.tableColumns = [
			{
				id: 'clau',
				label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.CODE',
				isResizable: false,
				isSortable: true,
				width: '180px'
			},
			{
				id: 'valor',
				label: 'UI_COMPONENTS.MODAL_ERRORS.TABLE_COLUMNS.DESCRIPTION',
				isResizable: false,
				isSortable: true,
			},
		]
	}

	closeModal = (): void => { this.activeModal.close(); }
}
