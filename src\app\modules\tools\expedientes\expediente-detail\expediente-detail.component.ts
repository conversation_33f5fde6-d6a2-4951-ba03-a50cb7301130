import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-expediente-detail',
	template: ` <div id="aeat-detail-container" *ngIf="idEni && idPeticionIntercambio">
		<aeat-conector-expediente-detail
			*axLazyElement
			[idEni]="idEni"
			[idPeticionIntercambio]="idPeticionIntercambio"
			(goBack)="navigateToList()"
		></aeat-conector-expediente-detail>
	</div>`,
})
export class ExpedineteDetailComponent implements OnInit {
	idEni: string | undefined;
	idPeticionIntercambio: string | undefined;


	constructor(
		private webComponentService: WebComponentsService,
		private router: Router,
		private aRoute: ActivatedRoute
	) {
		//Empty
	}

	ngOnInit(): void {
		this.webComponentService.setWebComponentStyle(
			environment.wcUrlAEATConectorCss,
			'aeat-conector'
		);
		this.idEni = this.aRoute.params['_value']?.idEni;
		this.idPeticionIntercambio = this.aRoute.params['_value']?.idPeticion;
		
		if (!this.idEni || !this.idPeticionIntercambio) {
			this.navigateToList()
		}
	}

	navigateToList() {
		this.router.navigate(['/secured/eines/expedients']);
	}
}
