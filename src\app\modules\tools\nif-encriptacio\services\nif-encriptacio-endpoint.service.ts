import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { EncryptNifResponse } from '../models/nif-encriptacio-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class NifEncriptacioEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	/**
	 * Encryptions > Encrypt
	 * @description Encrypt a nif
	 */
	encryptNif = (nif: string): Observable<EncryptNifResponse> => {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlSeguretat,
			url: `/xifrat`,
			method: 'post',
			body:{ data:nif }
		};
		return this.httpService.post(httpRequest);
	}
}
