/********************************/
/**** INICI ESTILS PEU ANTIC ****/
footer {
    background-color: #f6f6f6;
    border-top: 0;
}

footer .avis_legal p {
    color: #666;
}
footer .avis_legal p a {
    color: #666;
    text-decoration: underline;
}
footer .avis_legal p a:hover {
    color: #666;
}


@media (min-width: 47.939em){
    html footer .avis_legal p {
        width: 100%;
    }
    footer .footer_tab_ord{
        margin-bottom: 1em;
    }
}
#peuImatge
{
    padding-left:0
}
footer p .torna_amunt{
    margin-top: -32px;
}

@media(max-width: 61.938em) and (min-width:48.000em) {
    footer .connecta {
        border-top: 2px solid #ccc;
        margin-top: 10px;
        padding-top: 20px
    }

    footer .connecta .formatter_body.fpca_peu_faldoGiny {
        width: 66%;
    }

    footer .connecta .formatter_body.fpca_peu_faldoSocial {
        margin-top: 45px;
        width: 33%;
    }

    footer .connecta .formatter_body .twitter {
        width: 50%;
        margin-right: 20px;
    }

    footer .connecta .formatter_body .cerca_xarxes {
        width: 43%;
        float: left;
        margin-right: 10px;
    }
    footer .list-group1.w-45{
        width:45%;
    }
    footer .connecta .llistat_xarxes_socials{
        float: right;
        width: auto;
    }

    footer .connecta .formatter_body .accordionFoot {
        width: 66%;
    }
}

@media (min-width: 62.000em){
    footer .list-group1.w-45{
        width:100%;
    }
}

@media (max-width: 767px){
    footer .footer_tab_top{
        z-index: 999;
        position: relative;
    }

}
/***** FI ESTILS PEU ANTIC  *****/
/********************************/

/********************************/
/***** INICI ESTILS PEU NOU *****/
.fpca_peu a:focus, .fpca_peu button:focus, .fpca_peu input:focus {
    outline: thin dotted;
    outline-offset: -2px;
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.2), 0 0 8px rgba(192, 0, 0, 0.6); }

.fpca_peu_promocional .footer-cta {
    background-color: #f6f6f6;
    padding: 30px 0; }
.footer-cta > div {
    justify-content: flex-end;
    display: flex;
    padding-left: 15px;
    padding-right: 15px;
}

.fpca_peu_new .paragraph_date {
    font-size: 14px;
    font-family: "openSans_semiBold";
    color:#333;
}

.fpca_peu_new .NG-col--fullwidth-xs {
    justify-content: center;
}

.logo_financament {
    max-width: 225px;
    height: auto;
}

.footer-cta__title {
    font-family: 'OpenSans_Semibold', Arial, Helvetica, sans-serif;
    font-size: 20px; }
.footer-cta__description {
    font-size: 14px;
    color: #FFFFFF;
    text-align: center;
    margin-left: auto;
    margin-right: auto; }
.footer-cta__btn {
    margin-top: 24px;
    margin-left: auto;
    margin-right: auto;
    color: #FFFFFF;
    background-color: #C00000;
    padding: 9px 26px;
    border: none;
    outline: none;
    border-radius: 4px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    -ms-border-radius: 4px;
    -o-border-radius: 4px; }
.footer-cta__btn:hover {
    background-color: #900000; }
.footer-cta__btn:focus {
    outline: thin dotted;
    outline: 5px auto -webkit-focus-ring-color;
    outline-offset: -2px; }
@media (min-width: 1025px) {
    .footer-cta__title {
        margin-bottom: 17px; } }
@media (max-width: 1024px) {
    .footer-cta__title {
        margin-bottom: 14px; }
    .footer-cta__btn {
        margin-top: 4px; } }
@media (max-width: 767px) {
    .footer-cta__title {
        font-size: 16px;
        margin-bottom: 17px; }
    .footer-cta__description {
        font-size: 13px; } }

.footer-social {
    background-color: #F5F5F5; }
.footer-social .footer-social__wrapper {
    width: 100%; }
.footer-social .footer-social__wrapper > div {
    justify-content: center;
    flex-direction: row;
    padding-top: 12px;
}
.footer-social .footer-social__wrapper__list {
    display: flex;
    padding-left: 0; }
.footer-social .footer-social__wrapper .footer-social__text {
    text-align: center;
    display: flex;
    align-items: center;
    max-width: 80%;
    margin-left: auto;
    margin-right: auto;
    /*margin-top: 16px;*/ }
.footer-social .footer-social__wrapper .footer-social__text .footer-social__text-title {
    font-size: 15px;
    color: #333;
    font-family: 'OpenSans_Regular';
    margin-bottom: 12px; }
.footer-social .footer-social__wrapper .footer-social__list {
    display: inline-flex;
    width: 100%;
    padding-left: 13px;
    justify-content: center;
    margin-bottom: 22px; }
.footer-social .footer-social_border {
    height: 3px;
    width: 100%; }
.footer-social_border--line {
    display: block;
    border-top: 1px solid #ddd;
    width: calc(100% - 28px);
    padding-top: 3px;
    margin-left: 14px;
    margin-right: 14px; }

@media (min-width: 1025px) {
    .footer-social__list {
        position: relative;
        top: 13px; }
    .footer-social .footer-social__wrapper > div{
        padding: 0;
    }
    .footer-social .footer-social__wrapper .footer-social__text .footer-social__text-title {
        margin-bottom: 16px; }
    .footer-social .footer-social__wrapper .footer-social__text {
        max-width: 100%;
        margin-top: 16px;}
}
@media (max-width:768px){
    .footer-social .footer-social__wrapper {
        margin-top: 15px;
    }
    .footer-social .footer-social__wrapper >div{
        justify-content: center;
        flex-direction: row;
        flex-wrap: wrap;
    }
    .footer-social .footer-social__wrapper .footer-social__text{
        max-width: 100%;
        margin: 0;
    }
    .footer-social .footer-social__wrapper .footer-social__list{
        padding: 0;
        margin-left: 0;
    }
}

.footer-search {
    background-color: #F5F5F5; }
.footer-search__wrapper {
    padding: 22px 15px 0px 15px; }
@media (max-width:768px){
    .footer-search__wrapper {
        max-width: 90%;
        margin: auto;
        padding: 0;
    }
}
@media (min-width: 1025px) {
    .footer-search .NG-row-flex--vertical-md {
        flex-direction: row; } }
.footer-search__description-wrapper {
    width: 100%; }
@media (min-width: 1025px) {
    .footer-search__description-wrapper {
        width: 44.44444%;
        flex: .4444444;
        border-bottom: 1px solid #ddd; } }
.footer-search__search-wrapper {
    width: 100%;
    border-bottom: 1px solid #ddd; }
@media (min-width: 1025px) {
    .footer-search__search-wrapper {
        width: 66.66666%;
        flex: .6666666; } }
.footer-search__description {
    width: 100%; }
.footer-search__title {
    font-family: 'OpenSans_Bold', Arial, Helvetica, sans-serif;
    font-size: 20px;
    color: #333333;
    margin-bottom: 15px; }
@media (max-width: 767px) {
    .footer-search__title {
        text-align: center; } }
.footer-search__text {
    font-family: 'OpenSans_Regular', Arial, Helvetica, sans-serif;
    font-size: 14px;
    color: #666666; }
@media (max-width: 767px) {
    .footer-search__text {
        text-align: center; } }
.footer-search__text_paragraph {
    color: #666;
    font-family: 'OpenSans_Semibold';
    margin-bottom: 21px; }
.footer-search .footer-search__search {
    display: flex;
    justify-content: center;
    width: 100%; }
@media (max-width:768px){
    .footer-search .footer-search__search {
        max-width: 560px;
        margin: auto;
    }
    .footer-cta {
        padding: 0px;
    }

    .footer-cta > div {
        justify-content: center;
    }
}
.footer-search .footer-search__search .NG-inputSearch__input {
    flex: 1;
    height: 43px;
    margin-left: 0;
    border-radius: 0;
    -webkit-appearance: none;  }
@media (min-width: 1025px) {
    .footer-search .footer-search__search .NG-inputSearch__input {
        margin-left: 0; } }

.footer-contact {
    background-color: #F5F5F5; }
.footer-contact__wrapper {
    padding: 22px 15px; }
.footer-contact__description-wrapper {
    width: 100%; }
@media (min-width: 1025px) {
    .footer-contact__description-wrapper {
        width: 33.33333%;
        flex: .3333333; } }
.footer-contact__description {
    width: 100%; }
.footer-contact__title {
    font-family: 'OpenSans_Bold';
    font-size: 20px;
    color: #333333;
    margin-bottom: 15px; }
@media (max-width: 767px) {
    .footer-contact__title {
        text-align: center; } }
.footer-contact__text {
    font-family: 'OpenSans_Semibold';
    font-size: 14px;
    color: #666; }
@media (max-width: 767px) {
    .footer-contact__text {
        text-align: center; } }
.footer-contact__list-wrapper {
    width: 100%; }
@media (min-width: 1025px) {
    .footer-contact__list-wrapper {
        text-align: right;
        flex: .666666; } }
.fpca_peu_new .footer-contact__list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 0;
    justify-content: flex-start;}
.footer-contact__list .footer-contact__list-item {
    width: 25%;
    margin-bottom: 18px;
    margin-top: 0;
    text-align: left; }
.footer-contact__list .footer-contact__list-img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
    flex: 0 0 40px; }
.footer-contact__list .footer-contact__list-txt {
    font-family: 'OpenSans_Semibold';
    font-size: 11px;
    color: #333333; }
.footer-contact__list .footer-contact__list-link {
    display: flex;
    align-items: center;
     padding:0;}
.footer-contact__list .footer-contact__list-link:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important; }
.footer-contact__list .footer-contact__list-link:hover .footer-contact__list-txt {
    text-decoration: underline; }
@media (max-width: 1024px) {
    .footer-contact__list {
        padding-left: 0;
        margin-top: 6px; } }
@media (max-width: 767px) {
    .footer-contact__list {
        padding: 0; }
    .footer-contact__list .footer-contact__list-item {
        width: 50%;
        padding-right: 15px;
        margin-bottom: 8px; }
    .footer-contact__list .footer-contact__list-item:nth-child(even){
    padding-right: 0;
    padding-left: 15px;
    }
        }

.footer-logos {
    background-color: #F5F5F5; }
.footer-logos__wrapper {
    width: 100%;
    padding: 10px 15px 20px 15px; }
.footer-logos__description {
    display: flex;
    margin-right: 22px; }
.footer-logos__description span {
    display: block;
    margin: auto;
    font-family: 'OpenSans_Bold';
    font-size: 14px; }
@media (max-width: 767px) {
    .footer-logos__description {
        width: 100%;
        text-align: center;
        justify-content: center;
        margin: auto;
        margin-bottom: 15px; } }
.footer-logos__list {
    display: flex;
    padding-left: 0;
    margin-bottom: 0;
    align-items: center;
    flex-wrap: wrap; }
.footer-logos__list-item {
    margin: 0 14px 15px 14px; }
@media (max-width: 767px) {
    .footer-logos__list {
        width: 100%;
        text-align: center;
        justify-content: center; } }
.footer-logos__list-link {
    display: block; }
.footer-logos .NG-border {
    border-top: 1px solid #DDDDDD;
    padding-top: 20px; }

.footer-bottom {
    background-color: #333333; }
.footer-bottom__wrapper {
    padding: 4px 15px;
    width: 100%; }
@media (max-width: 1025px){
    .footer-bottom__wrapper {
        width: 93%;
        margin: auto;
        padding: 4px 0;
    }
}
@media (max-width: 425px){
    .footer-bottom__wrapper {
        width: 100%;
    }
}
.footer-bottom__list {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    align-self: center;
    width: 100%;
    overflow: hidden;
}
@media (min-width: 768px) {
    .footer-bottom__list {
        justify-content: end;
        justify-content: flex-end;
        right: -13px;
        position: relative;
    }
}
@media (max-width: 767px) {
    .footer-bottom__list {
        text-align: center;
        justify-content: center;
        margin: auto;
        margin-bottom: 15px; } }
.footer-bottom__list-item {
    padding: 0 13px;
    position: relative;
    display: flex;
    align-items: center;
    height: 25px;
    margin:0;
}
/*.footer-bottom__list-item.separator:after {
    content: " ";
    color: #FFFFFF;
    position: absolute;
    right: -1px;
    top: 50%;
    height: 16px;
    border-left: 1px solid white;
    transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%); }*/

.footer-bottom__list-link {
    font-size: 10px;
    line-height: 2;
    color: #ffffff;
    padding:0;}

.footer-bottom__list-item.separator:after {
    content: '|';
    color: white;
    position: absolute;
    right: -5px;
    top: 2px;
}
.footer-bottom__list-item:last-of-type:after {
    content: none; }
.footer-bottom__list-link:hover, .footer-bottom__list-link:focus {
    color: #ffffff; }
.footer-bottom__list-link:focus {
    outline: none !important;
    outline-offset: 0 !important;
    box-shadow: none !important;
    text-decoration: underline; }
.footer-bottom__logo {
    max-width: 160px;
    height: auto; }
.footer-bottom__logo-link {
    align-self: center; }

@media (max-width:450px){
    .footer-bottom__logo {
        margin-bottom:10px;
    }
}

@media (max-width: 768px){

}
@media (max-width: 1025px){

}
@media (max-width: 767px) {
    .footer-bottom__logo-link {
        margin: 0 auto 10px auto; } }
.footer-bottom__logo-link:focus {
    outline: thin dotted;
    outline-offset: -2px;
    color: #FFFFFF; }

.NG-goToTop {
    position: fixed;
    bottom: 64px;
    z-index: 999;
    /*margin-right: 15px;*/
    /* right: 0;*/
    background-color: #333;
    border-radius: 4px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.5);
    width: 40px;
    height: 40px;
    padding: 0;
    border: 0; }
.NG-goToTop__wrapper {
    flex-direction: row-reverse;
    position: relative;
    margin:auto;
}
@media (max-width: 1025px) {
    .NG-goToTop__wrapper {
        max-width: 93%;
    }
}
@media (max-width: 767px) {
    .NG-goToTop {
        bottom: 10px; } }
.NG-goToTop .NG-goToTop_image {
    transform: rotate(180deg); }
.NG-goToTop:hover {
    opacity: 0.7; }

.carousel-tablist-highlight{display:none;}

/*****  FI ESTILS PEU NOU   *****/
/********************************/