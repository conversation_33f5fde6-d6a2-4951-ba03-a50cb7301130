$gris: #999999
$negro: #333333
$negro-light: #404040
$color-secondary-lighter-5: #dddddd
$color-secondary-lighter-10: #333333
$color-secondary-lighter-8: #999999
$white: #ffffff

.pt-navbar
	::ng-deep
		.p-menubar
			height: 40px
			padding: 0 28px 0 0
			box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1)
			background-color: $negro-light
			color: $white
			border-radius: 0px
			.p-menubar-root-list > .p-menuitem
				text-align: right

				&.p-menuitem-active > .p-menuitem-link
					border: 4px solid $negro-light
					
					&:not(.p-disabled):hover
						border: 4px solid $negro-light
						background-color: $gris
						.p-menuitem-text
							color: $white
						.p-submenu-icon
							color: $white
						.p-menuitem-icon
							color: $white

					.p-menuitem-text
						color: $white
					.p-submenu-icon
						color: $white
					.p-menuitem-icon
						color: $white

				.p-menuitem-link
					height: 40px
					color: $white
					font-family: 'OpenSans-SemiBold'
					font-size: 16px
					border: none 
					border-radius: 0
					text-align: left

					// Poner background gris
					&:hover
						border: 4px solid $negro-light
						background-color: $gris
						color: $white

					&:focus
						border: 4px solid $negro-light
						background-color: $white
						box-shadow: none
						color: $white
						background-color: $gris

					&.p-menuitem-link-active
						border: 4px solid $negro-light
						background-color: $white !important
						.p-menuitem-text
							color: $negro !important
						.p-menuitem-icon
							color: $negro !important

					.p-menuitem-text
						color: $white

					.p-submenu-icon
						color: $white

					.p-menuitem-active
						color: $white

					.p-menuitem-icon
						color: $white

			.p-submenu-list
				background-color: $negro
				padding: 0
				border: none
				z-index: 2
			   
				.p-menuitem
					&.p-menuitem-active > .p-menuitem-link
						border: 4px solid $negro
						 
						&:not(:hover)
							background-color: $color-secondary-lighter-5
							.p-menuitem-text
								color: $negro
							.p-submenu-icon
								color: $negro
							.p-menuitem-active
								color: $negro
							.p-menuitem-icon
								color: $negro

em:hover
	cursor: pointer

.pt-navbar-header
	background-color: $negro
	color: $white
	padding: 8px 20px

// body > app-root > div > app-navbar > nav > div > div:nth-child(2)
//     line-height: 0.5

::ng-deep .user-info-popover.popover
	border: 1px solid $color-secondary-lighter-5
	border-radius: 4px
	box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.1)
	margin-top: 14px
	min-width: 300px

	.arrow
		display: none

	.popover-body
		background-color: $white
		padding: 20px

		.profile-container
			display: flex
			align-items: center

			.profile-img
				color: $color-secondary-lighter-10
				font-size: 40px
				margin-right: 10px

			.profile-info
				font-family: 'OpenSans-SemiBold'
				font-size: 14px
				color: $color-secondary-lighter-10

				.profile-name
					&:not(:last-child)
						margin-right: 5px

				.text
					color: $color-secondary-lighter-8
					font-family: 'OpenSans-Regular'
					font-size: 12px
					margin-bottom: 0

				.value
					font-family: 'OpenSans-Regular'
					font-size: 12px
					color: $color-secondary-lighter-10
					margin-bottom: 0
