import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SelfAssessmentV1Routes } from 'src/app/core/models/config.model';

const routes: Routes = [
	{
		path: '',
		redirectTo: SelfAssessmentV1Routes.TABLE,
		pathMatch: 'full',
	},
	{
		path: SelfAssessmentV1Routes.TABLE,
		loadChildren: () =>
			import(`./modules/table/table.module`).then(
				(module) => module.TableModule
			),
	},
	{
		path: SelfAssessmentV1Routes.DETAIL_ID,
		loadChildren: () =>
			import(`./modules/detail/detail.module`).then(
				(module) => module.DetailModule
			),
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule],
})
export class AutoliquidacionsV1RoutingModule {}
