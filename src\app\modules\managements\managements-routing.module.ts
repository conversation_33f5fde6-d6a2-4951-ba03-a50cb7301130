import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { ManagementRoutes } from 'src/app/core/models/config.model';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: ManagementRoutes.MONITOR,
		loadChildren: () => import(`./managements-monitor/managements-monitor-routing.module`).then(module => module.ManagementsMonitorRoutingModule),
		canActivate:[AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN,UserRoles.TECHNICIAN]
		}
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class ManagementsRoutingModule { }
