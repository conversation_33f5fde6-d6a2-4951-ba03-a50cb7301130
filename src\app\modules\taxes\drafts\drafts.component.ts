import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
	PtMessageI,
} from 'pt-ui-components-mf-lib';


import { AlertService } from 'src/app/core/services/alert.service';

@Component({
	selector: 'app-drafts-table',
	templateUrl: './drafts.component.html'
})
export class DraftsComponent implements OnDestroy {

	// Other
	displayAlert: boolean = false;
	alertData: PtMessageI;

	private _unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private alertService: AlertService
	) { 
		this.alertService.alert$.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(alert => this.showAlert(alert))
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	private showAlert(alert: PtMessageI) {
		this.displayAlert = true
		this.alertData = {
			...alert,
			closableFn: this.closeFn.bind(this)
		}

		setTimeout(() => this.displayAlert = false, 5000)
	}

	private closeFn() {
    this.displayAlert = false;
  }
}
