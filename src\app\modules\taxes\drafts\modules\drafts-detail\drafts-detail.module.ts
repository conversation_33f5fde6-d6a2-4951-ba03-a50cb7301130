import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { DraftsDetailComponent } from './drafts-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { DetailPageModule } from '../../../../shared/detail-page';
import { ErrorTabComponent } from './components/error-tab/error-tab.component';
import { SelfAssessmentTabComponent } from './components/self-assessment-tab/self-assessment-tab.component';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';

const routes: Routes = [
	{
		path: '', 
    component: DraftsDetailComponent,
		data: { 
			title: ''
		}
	}
];

@NgModule({
	declarations: [
		DraftsDetailComponent,
		ErrorTabComponent,
		SelfAssessmentTabComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		DetailPageModule,
		PtUiComponentsMfLibModule,
    RouterModule.forChild(routes)
	]
})
export class DraftsDetailModule { }
