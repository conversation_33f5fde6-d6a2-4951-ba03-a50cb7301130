import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SarcatMiddlewareComponent } from './sarcat-middleware.component';
import { UserRoles } from 'pt-ui-components-mf-lib';

const routes: Routes = [
	{
		path: '',
		component: SarcatMiddlewareComponent,
		data: {
			title: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_SARCAR.PAGE_TITLE',
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
];

@NgModule({
	declarations: [SarcatMiddlewareComponent],
	imports: [CommonModule, RouterModule.forChild(routes), LazyElementsModule],
})
export class SarcatMiddlewareModule {}
