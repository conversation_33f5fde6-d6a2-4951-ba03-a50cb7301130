import { Injectable } from '@angular/core';
import { PtHttpService, PtHttpRequest, PtHttpResponse } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class BiometricSignatureDetailEndpointsService {

  constructor(
		private httpService: PtHttpService
	) { }

	resetSignature(id_peticio: string | object): Observable<PtHttpResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlBiometricSignature,
			url: `/retries/reset/${id_peticio}`,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}

	downloadDocument(idDoc: string): Observable<PtHttpResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlDocumentsAdmin,
			url: `/${idDoc}`,
			method: 'get'
		}
		return this.httpService.get(requestOptions);
	}
}
