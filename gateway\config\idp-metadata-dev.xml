<?xml version="1.0" encoding="UTF-8"?>
<!--
     This is example metadata only. Do *NOT* supply it as is without review,
     and do *NOT* provide it in real time to your partners.

     This metadata is not dynamic - it will not change as your configuration changes.
-->
<EntityDescriptor  xmlns="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:shibmd="urn:mace:shibboleth:metadata:1.0" xmlns:xml="http://www.w3.org/XML/1998/namespace" xmlns:mdui="urn:oasis:names:tc:SAML:metadata:ui" xmlns:req-attr="urn:oasis:names:tc:SAML:protocol:ext:req-attr" validUntil="2040-10-13T10:17:39.600Z" entityID="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/shibboleth">

    <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol urn:oasis:names:tc:SAML:1.1:protocol urn:mace:shibboleth:1.0">

        <Extensions>
            <shibmd:Scope regexp="false">localdomain</shibmd:Scope>
<!--
    Fill in the details for your IdP here 

            <mdui:UIInfo>
                <mdui:DisplayName xml:lang="en">A Name for the IdP at lgicsmt20-bck</mdui:DisplayName>
                <mdui:Description xml:lang="en">Enter a description of your IdP at lgicsmt20-bck</mdui:Description>
                <mdui:Logo height="80" width="80">https://preproduccio.autenticaciogicar4.extranet.gencat.cat/Path/To/Logo.png</mdui:Logo>
            </mdui:UIInfo>
-->
        </Extensions>

        <!-- First signing certificate is BackChannel, the Second is FrontChannel -->
        <KeyDescriptor use="signing">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>
        <KeyDescriptor use="signing">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>
        <KeyDescriptor use="encryption">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>

        <ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:1.0:bindings:SOAP-binding" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML1/SOAP/ArtifactResolution" index="1"/>
        <ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/ArtifactResolution" index="2"/>

        <!--
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/SLO"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST-SimpleSign" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST-SimpleSign/SLO"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/Redirect/SLO"/>
        <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST/SLO"/>
        -->

        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST-SimpleSign" req-attr:supportsRequestedAttributes="true" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST-SimpleSign/SSO"/>
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" req-attr:supportsRequestedAttributes="true" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST/SSO"/>
        <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" req-attr:supportsRequestedAttributes="true" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/Redirect/SSO"/>
        <SingleSignOnService Binding="urn:mace:shibboleth:1.0:profiles:AuthnRequest" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/Shibboleth/SSO"/>

    </IDPSSODescriptor>


    <AttributeAuthorityDescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:1.1:protocol">

        <Extensions>
            <shibmd:Scope regexp="false">localdomain</shibmd:Scope>
        </Extensions>

        <!-- First signing certificate is BackChannel, the Second is FrontChannel -->
        <KeyDescriptor use="signing">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>
        <KeyDescriptor use="signing">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>
        <KeyDescriptor use="encryption">
            <ds:KeyInfo>
                    <ds:X509Data>
                        <ds:X509Certificate>
MIIDtTCCAp2gAwIBAgICAMIwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
MDExMjQxMDE2MDBaGA8yMDUwMTEyNDEwMTYwMFowPjE8MDoGA1UEAxMzcHJlcHJv
ZHVjY2lvLmF1dGVudGljYWNpb2dpY2FyNC5leHRyYW5ldC5nZW5jYXQuY2F0MIIB
IjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAsPrUF7Y3Aa672/eG7Q0u3fUv
ZnAZYiyoXN7raQvbD3Ge85rVrqJgnF6m0IPqgiWicIfjUg0ZE6rt6fOh3bvNouI/
WqIm+DfPGk+JU0i5+wrBpOKhPG81UF6NQCRRYHLdN9KypdYd0FYF+1G+/DQjVB9j
xnSSAFgpLH47DHecnNuiHNZMmaKcmZQnrQ4akznmTnnTkWj0vCAxF8mjMpBNMeoQ
KukNY/p8qn99m+KVW0wgbesTRKvXdsrDuX7JMBd1ruPd0Wv2uf67M8sNdYViaT7U
OQViSY7Mpd/ZZB0NW3GKOareYQyMG+xtXozdl1SayiOxO+22x7AWBZciQmA9NwID
AQABo4GPMIGMMAwGA1UdEwEB/wQCMAAwHQYDVR0OBBYEFLkZkFUNzhgaBCK6yM3t
1RaKrzHAMAsGA1UdDwQEAwIE8DAdBgNVHSUEFjAUBggrBgEFBQcDAQYIKwYBBQUH
AwIwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlm
aWNhdGUwDQYJKoZIhvcNAQELBQADggEBAFWQ0gVTrlE/NaEGJzdsOOmkDNDlCQ/p
FmB57ixKTUGJJrP/sjKerCVQHhvIvqjgKj7+cn8iXx7jUrnXDTomY/xIZBfJlQcf
SCBjA/xqVjHYXC+tQTfyl8ApmsZ81EL8UZqPr3Z08BnT8gk58Cy5By9lzo0TkGa4
B8q/wlkQIReR4BM9hqJ8+tnFFklL8j+TgA1jLffoNoQyO326fxIMjeao+usKv3hM
qX1gNVlpx6CvA5Ei9Y5kQmI0T8UWd7TVt6XMdokwwptAfuDvYYDmOec4RDZfqCzH
x3MpwzEExH9WFOBga94eOyp9JL3x/DoT39iRxw8Dc3b/NaiElKy/DM8=
                        </ds:X509Certificate>
                    </ds:X509Data>
            </ds:KeyInfo>

        </KeyDescriptor>

        <AttributeService Binding="urn:oasis:names:tc:SAML:1.0:bindings:SOAP-binding" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML1/SOAP/AttributeQuery"/>
        <!-- <AttributeService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/AttributeQuery"/> -->
        <!-- If you uncomment the above you should add urn:oasis:names:tc:SAML:2.0:protocol to the protocolSupportEnumeration above -->

    </AttributeAuthorityDescriptor>

</EntityDescriptor>
