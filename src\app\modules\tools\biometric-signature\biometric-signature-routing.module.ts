import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { BiometricSignatureRoutes } from 'src/app/core/models/config.model';

const routes: Routes = [
	{
		path: '',
		redirectTo: BiometricSignatureRoutes.TABLE,
		pathMatch: 'full',
	},
	{
		path: BiometricSignatureRoutes.TABLE,
		loadChildren: () => import(`./biometric-signature.module`).then(module => module.BiometricSignatureModule),
	},
	{
		path: BiometricSignatureRoutes.DETAIL_ID,
		loadChildren: () => import(`./modules/biometric-signature-detail/biometric-signature-detail.module`).then(module => module.BiometricSignatureDetailModule),
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class BiometricSignatureRoutingModule { }
