import { Component, OnInit, ViewChild } from '@angular/core';
import { Subject } from 'rxjs';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { LazyLoadEvent } from 'primeng/api';
import {
	PtTableColumn, PtValidations, iCommonError
} from 'pt-ui-components-mf-lib';
import { ManagementsMonitorEndpointsService } from '../../services/managements-monitor-endpoints.service';

import { ManagementsMonitorService } from '../../services/managements-monitor.service';
import { DatePipe } from '@angular/common';
import { 
	ManagementsMonitorRequest, 
	ManagementsMonitorRow, 
	ManagementsMonitorFilter, 
	IManagementsMonitor, 
	MANAGEMENTS_MONITOR_FILTER_STORAGE, 
	MANAGEMENTS_MONITOR_DETAIL_STORAGE, 
	TramitType
} from '../../models/managements-monitor.model';
import { ManagementsMonitorResponse, ManagementsMonitorResponseContent } from '../../models/managements-monitor-endpoints.model';
import { AppRoutes, ManagementRoutes, ManagementsMonitorRoutes, StatesIcons } from 'src/app/core/models/config.model';
import { IDetailData } from '../managements-monitor-detail/models/managements-monitor-detail.model';
import { FilterModalInput } from '../../../../shared/modal-filter/models/modal-filter.model';
import { 
	EXPORT_COLUMNS_DATA, 
	FILTER_FORM_DATA, 
	FilterFormFields, 
	FilterFormValue, 
	TableColumns, 
	TableSortFields
} from './models/managements-monitor-table.model';
import { ModalFilterComponent } from '../../../../shared/modal-filter';
import { TableExportService } from 'src/app/core/services/table-export.service';
import { PtTableComponent } from 'pt-ui-components-mf-lib/lib/components/table/table.component';

@Component({
	selector: 'app-resolucions-table',
	templateUrl: './managements-monitor-table.component.html'
})
export class ManagementsMonitorTableComponent implements OnInit {
	
	@ViewChild('dt', { static: true }) private readonly table: PtTableComponent;

	filterValue: Partial<ManagementsMonitorFilter>;

	// Table
	searchColumns: PtTableColumn[] = [];
	searchRows: ManagementsMonitorRow[] = [];

	// Other
	options: LazyLoadEvent;
	paginationTotal: number = 0;

	private _unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private endpointsService: ManagementsMonitorEndpointsService,
		private router: Router,
		private managementsMonitorService: ManagementsMonitorService,
		private exportService: TableExportService
	) { }

	ngOnInit(): void {
		this.searchColumns = this.setTableColumns();
		this.filterValue = this.setFilterValue();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}
	
	private setTableColumns = (): PtTableColumn[] => [
		{
			id: TableColumns.idTramitacio, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.idTramitacio}`,
			isResizable: true, isSortable: true, width: "14%"
		},
		{
			id: TableColumns.stateRow, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.estat}`,
			isResizable: true, isSortable: true, template: 'link', width: "14%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.presentadorNif, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.presentadorNif}`,
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.presentadorNom, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.presentadorNom}`,
			isResizable: true, isSortable: true, width: "17%"
		},
		{
			id: TableColumns.subjectePassiuNif, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.subjectePassiuNif}`,
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.subjectePassiuNom, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.subjectePassiuNom}`,
			isResizable: true, isSortable: true, width: "17%"
		},
		{
			id: TableColumns.origen, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.origen}`,
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.tramit, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.tramit}`,
			isResizable: true, isSortable: true, width: "8%"
		},	
		{
			id: TableColumns.dadesAddicionals, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.dadesAddicionals}`,
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.dataModificacio, label: `MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.COLUMNS.${TableColumns.dataModificacio}`,
			isResizable: true, isSortable: true, width: "11%",template: "date",
			options: { dateFormat: 'dd/MM/yyyy HH:mm' }
		},
		{
			id: TableColumns.tableActions, label: null, isResizable: false, isSortable: false,
			template: "buttons", width: '4%'
		}
	];
	
	private setFilterValue():Partial<ManagementsMonitorFilter> {
		const data: ManagementsMonitorFilter = this.managementsMonitorService.dataStorageService.getItem(MANAGEMENTS_MONITOR_FILTER_STORAGE);

		return new FilterFormValue(data);
	}
	
	openFilterModal() {
		// Open delete modal
		const modalRef = this.managementsMonitorService.modalService.open(
			ModalFilterComponent,
			{ size: 'xl', windowClass: '' }
		);
		
		const modalInput: FilterModalInput = this.setModalInput();
		modalRef.componentInstance.modalInput = modalInput;

		modalRef.componentInstance.modalOutput.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(filterFormValue: ManagementsMonitorFilter) => {
				this.filterValue = filterFormValue;

				this.table.table.reset();
			}
		);
	}
	
	private setModalInput (): FilterModalInput {
		return {
			formValue: this.filterValue,
			formResetValue: new FilterFormValue(),
			formFieldsData: FILTER_FORM_DATA,
			formGroupValidations: [
				PtValidations.equalsOrLessThan(FilterFormFields.dateToForm, FilterFormFields.dateFromForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan'),
				PtValidations.equalsOrGreaterThan(FilterFormFields.dateFromForm, FilterFormFields.dateToForm, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan'),
			]
		}
	}

	/**
	 * Search
	 * @description Search resolucions by criteria
	 */
	search = (event: LazyLoadEvent): void => {
		// Reset previous data
		this.searchRows = [];
		this.paginationTotal = 0;
		
		this.setOptionsEvent(event);
		
		this.managementsMonitorService.dataStorageService.setItem(MANAGEMENTS_MONITOR_FILTER_STORAGE, this.filterValue);

		const request: ManagementsMonitorRequest = new ManagementsMonitorRequest(this.filterValue, event)
		this.endpointsService.getManagementList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: ManagementsMonitorResponse) => {
				if (response?.content) {
					this.setTableRows(response.content);
				}
			}
		);
	}
	
	private setOptionsEvent(event: LazyLoadEvent) {
		// Request
		if (!event.sortField){
			event.sortField = TableColumns.dataModificacio;
			event.sortOrder = -1;
		}

		if (TableSortFields[event.sortField]){
			event.sortField = TableSortFields[event.sortField];
		}

		this.options = event;
	}

	/**
	 * Table rows
	 * @description Map the table rows
	 */
	private setTableRows(response: ManagementsMonitorResponseContent) {
		this.paginationTotal = response.total;

		this.searchRows = response.results?.map((autoliquidacio: IManagementsMonitor, id: number) => {
			const stateIcon = this.managementsMonitorService.getStateIcon(autoliquidacio?.estat, 'self-assessment');
			autoliquidacio.tramit = this.managementsMonitorService.tramitTranslation(autoliquidacio.tramit);

			// Map autoliquidacio entity into the autoliquidacio table row
			return Object.assign(
				{},
				// Default autoliquidacio entity
				autoliquidacio,
				// Table row model
				{
					stateRow: {
						id: `button${autoliquidacio.idTramitacio}`,
						icon: stateIcon,
						iconPos: 'left',
						label: autoliquidacio.estat ? this.managementsMonitorService.translateService.instant(`MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.STATES.${autoliquidacio.estat}`) : "",
						clickFn: this.showDetailError.bind(this, autoliquidacio?.dadesAddicionals, autoliquidacio?.errors),
						disabled: !this.isStateError(stateIcon)
					},
					tableActions: [{
						id: `detail${autoliquidacio.idTramitacio}`,
						icon: 'sli2-eye',
						class: 'p-button-text',
						componentType: 'button',
						clickFn: this.navigateToDetail.bind(this, autoliquidacio, id)
					}],
				}
			);
		});
	}

	private showDetailError = ( tramit: string, errors?: iCommonError[]) => this.managementsMonitorService.showErrors(tramit, errors);

	private isStateError = (icon: string): boolean => icon === StatesIcons.ERROR;

	private navigateToDetail (row: ManagementsMonitorRow,id: number) {
		const detailData: IDetailData = {
			filterValue : this.filterValue,
			totalRows: this.paginationTotal,
			options: this.options,
			index: id + this.options?.first,
			idEntity: row?.id
		}

		this.managementsMonitorService.dataStorageService.setItem(MANAGEMENTS_MONITOR_DETAIL_STORAGE, detailData);

		this.router.navigate(['/' + AppRoutes.MANAGEMENTS.concat('/',ManagementRoutes.MONITOR,'/',ManagementsMonitorRoutes.DETAIL), row.id]);
	}

	export = () : void =>  {
		const options = {...this.options,first:0, rows: 500}
		const request: ManagementsMonitorRequest = new ManagementsMonitorRequest(this.filterValue, options)
		this.endpointsService.getManagementList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: ManagementsMonitorResponse) => {
				if (response?.content) {
					var datePipe = new DatePipe('en-US');
					let date = datePipe.transform(new Date(), 'yyyy-MM-dd_HH:mm');
					this.exportService.export2Excel(response?.content.results, EXPORT_COLUMNS_DATA, "Export_"+ date, 'Gestions');
				}
			}
		);
	}
}
