import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { TableComponent } from './table.component';
import { RouterModule, Routes } from '@angular/router';
import { ModalFilterModule } from 'src/app/modules/shared/modal-filter';

const routes: Routes = [
	{
		path: '', component: TableComponent,
		data: {
      title: 'MODULE_TRACKING.COMPONENT_LIQUIDACIO.PAGE_TITLE',
    },
	}
];

@NgModule({
	declarations: [
    TableComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		ModalFilterModule,
		TranslateModule.forChild(),
    RouterModule.forChild(routes),
		PtUiComponentsMfLibModule,
	],
})

export class TableModule { }