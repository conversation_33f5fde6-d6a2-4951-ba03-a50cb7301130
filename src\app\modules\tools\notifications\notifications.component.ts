import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { LazyLoadEvent } from 'primeng/api';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { 
	DataStorageService, 
	PtConfirmationModalService, 
	PtTableColumn 
} from 'pt-ui-components-mf-lib';
import { Subject } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { takeUntil } from 'rxjs/operators';
import { ModalNotificacioComponent } from './components/modal-notificacio/modal-notificacio.component';
import { DeleteNotificationResponse, NotificationFilters, NotificationListResponse, NotificationRequest, NotificationResponse, NotificationResponseContent } from './models/notifications-endpoint.model';
import { NotificationModalInput, NotificationModalModeEnum, NotificationTypeEnum, NotificationsRow } from './models/notifications.model';
import { NotificationsEndpointsService } from './services/notifications-endpoint.service';
import { NotificationsService } from './services/notifications.service';
import { StatesIcons } from 'src/app/core/models/config.model';

@Component({
	selector: 'app-notifications',
	templateUrl: './notifications.component.html',
	styleUrls: ['./notifications.component.sass']
})
export class NotificationsComponent implements OnInit, OnDestroy {

	private unsubscribe: Subject<void> = new Subject();

	notificationsResponse: NotificationResponseContent[] = [];

	// Table
	notificationsColumns: PtTableColumn[] = [];
	notificationsRows: NotificationsRow[] = [];
	totalNotifications: number = 0;
	
	// Other
	options: LazyLoadEvent
	filterValue: NotificationFilters;

	constructor(
		private modalService: NgbModal,
		private translateService:TranslateService,
		private endPointService: NotificationsEndpointsService,
		public confirmationService: PtConfirmationModalService,
		private dataStorageService: DataStorageService,
		private notificationsService: NotificationsService
	) {}

	ngOnInit(): void {
		this.setTableColumns();	
		
		this.setFilterData();
	}

	ngOnDestroy(): void {
		this.dataStorageService.deleteItem('notification-filter');
		
		this.unsubscribe.next();
		this.unsubscribe.complete();
	}

	private setTableColumns = (): void => {

		this.notificationsColumns = [
			{ 
				id: "tipoRow", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.NOTIFICATION_TYPE", 
				template: 'tag', isResizable: false, isSortable: false, width: "70px"
			},
			{ 
				id: "tituloCa", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_CA", 
				isResizable: false, isSortable: false, width: "90px"
			},
			{ 
				id: "tituloEs", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_ES", 
				isResizable: false, isSortable: false, width: "90px"
			},
			{ 
				id: "mensajeCa", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_CA", 
				isResizable: false, isSortable: false, width: "180px"
			},
			{ 
				id: "mensajeEs", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_ES", 
				isResizable: false, isSortable: false, width: "180px"
			},
			{ 
				id: "contextoRow", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.CONTEXT", 
				isResizable: false, isSortable: false, width: "60px"
			},
			{ 
				id: "inicio", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.INITIAL_DATE", 
				template:'date', isResizable: false, isSortable: false, width: "70px",
				options: {
					dateFormat: 'dd/MM/yyyy HH:mm'
				}
			},
			{ 
				id: "fin", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.FINAL_DATE", 
				template:'date', isResizable: false, isSortable: false, width: "70px",
				options: {
					dateFormat: 'dd/MM/yyyy HH:mm'
				}
			},
			{ 
				id: "activoRow", label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.STATE", 
				template: 'icon', isResizable: false, isSortable: false, width: "40px"
			},
			{ 
				id: 'tableActions', label: null, isResizable: false, isSortable: false, 
				template: "actions", width: '35px',  
				options: {
					dropdownOptions: [
						{
							id: 'delete',
							label: "UI_COMPONENTS.BUTTONS.DELETE",
							icon: "sli2-trash",
							command: this.deleteNotificationModal.bind(this)
						},
						{
							id: 'edit',
							label: "UI_COMPONENTS.BUTTONS.EDIT",
							icon: "sli2-pencil",
							command: this.editNotification.bind(this)
						}
					]
				}
			}
		];
	}

	private setFilterData () {
		const filterData = this.dataStorageService.getItem('notification-filter') as NotificationFilters;

		this.filterValue = filterData || this.notificationsService.resetFilterData();
	}

	search = async (event: LazyLoadEvent): Promise<void> => {
		// Reset previous data
		this.notificationsRows = [];
		this.totalNotifications = 0;
		
		if ( !event.sortField){
			event.sortField ="fin";
			event.sortOrder = -1;
		}

		// Request
		this.options = event;
		const request: NotificationRequest = new NotificationRequest(this.filterValue, event);
		
		this.endPointService.getNotificationsList(request).pipe(takeUntil(this.unsubscribe)).subscribe(
			(response: NotificationListResponse) => {
				this.notificationsResponse = response?.content?.results;
				this.notificationsRows = response?.content?.results.map(notification => 
					this.setNotificationRow(notification)
				);

				this.totalNotifications = response?.content?.total || 0;
			}
		)
	}

	private setNotificationRow(notificationData: NotificationResponseContent): NotificationsRow {
		const tipo = notificationData?.tipo?.toLowerCase();
		const severity = this.getTypeSeverity(tipo);

		return {
			...notificationData,
			tituloCa: notificationData.tituloCa?.replace(/<[^>]*>/g, ''),
			tituloEs: notificationData.tituloEs?.replace(/<[^>]*>/g, ''),
			mensajeCa: notificationData.mensajeCa?.replace(/<[^>]*>/g, ''),
			mensajeEs: notificationData.mensajeEs?.replace(/<[^>]*>/g, ''),
			contextoRow: this.translateService.instant(`MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.CONTEXT_OPTIONS.${notificationData?.contexto}`),
			tipoRow: {
				icon: this.getTypeIcon(tipo),
				label: this.translateService.instant(`MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.${tipo?.toUpperCase()}`),
				severity: severity,
				class: severity,
			},
			activoRow: {
				icon: notificationData?.activo ? StatesIcons.SUCCESS : StatesIcons.ERROR,
				label: this.translateService.instant(`MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.STATE_OPTIONS.${notificationData?.activo ? 'ACTIVE' : 'INACTIVE'}`),
			}
		}
	}

	notificationFilter() {
		this.openNotificationModal(NotificationModalModeEnum.FILTER);
	}

	addNotification() {
		this.openNotificationModal(NotificationModalModeEnum.NEW);
		
	}

	editNotification (event: any, colId: string, row: NotificationsRow) {

		let notification: NotificationResponseContent = this.getNotificationById(row.id);

		this.openNotificationModal(NotificationModalModeEnum.EDIT, notification);
	}

	private getNotificationById(id: string): NotificationResponseContent {
		return this.notificationsResponse.find(value => value.id === id);
	}
	
	private openNotificationModal(mode: NotificationModalModeEnum, preEditRow?: NotificationResponseContent)  {

		const modalRef = this.modalService.open(
			ModalNotificacioComponent,
			{ size: 'xl', windowClass: '' }
		);
		
		// Modal input data
		const modalInput: NotificationModalInput = {
			title: `MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_MODAL.${mode}`,
			mode: mode,
			notification: mode == NotificationModalModeEnum.FILTER ? this.filterValue : preEditRow
		}

		modalRef.componentInstance.modalInput = modalInput;
		
		// Modal output data
		modalRef.componentInstance.modalOutput.pipe(takeUntil(this.unsubscribe)).subscribe(
			(modalResponse: NotificationResponseContent | NotificationFilters) => {
				if (mode == NotificationModalModeEnum.FILTER) {
					this.filterValue = modalResponse as NotificationFilters;
					
					this.search(this.options);

					this.dataStorageService.setItem('notification-filter', modalResponse);

				} else {
					const method = mode === NotificationModalModeEnum.EDIT ? 'editNotification' : 'addNewNotification';
					
					this.endPointService[method](modalResponse as NotificationResponseContent, preEditRow?.id).pipe(takeUntil(this.unsubscribe)).subscribe(
						(response: NotificationResponse) => {
							if (response?.content) this.search(this.options);
						}
					);
				}
			}
		)
	}
	
	private getTypeIcon(type: string) {
		let icon = {
			warning: StatesIcons.EXCLAMATION_WARNING,
			success: StatesIcons.SUCCESS,
			error: StatesIcons.ERROR,
			info: StatesIcons.INFO,
			cad: StatesIcons.WAITING
		}

		return icon[type] ? icon[type] : '';
	}

	private getTypeSeverity(type: string): string {

		switch (type) {
			case NotificationTypeEnum.error:
				return 'danger';
			case NotificationTypeEnum.cad:
				return ''
			default:
				return type;
		}
	}


	deleteNotificationModal(event: any, colId: string, row: NotificationsRow) {
		this.confirmationService.openModal({
			severity: 'warning',
			title: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.DELETE_MODAL.TITLE',
			subtitle: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.DELETE_MODAL.MSG',
			confirmFn: this.deleteNotification.bind(this, row) 
		});
	}

	private deleteNotification(row: NotificationsRow) {
		this.endPointService.deleteNotification(row.id).pipe(takeUntil(this.unsubscribe)).subscribe(
			(response: DeleteNotificationResponse) => {
				if (response?.content) this.search(this.options)
			}
		);
	}

}
