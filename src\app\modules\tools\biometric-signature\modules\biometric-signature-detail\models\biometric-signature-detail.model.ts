import { LazyLoadEvent } from "primeng/api";
import { DetailCardData, DetailFieldType } from "src/app/modules/shared/detail-card";
import { TabData, TabType } from "src/app/modules/shared/detail-tabs";
import { BiometricSignatureFilter, BiometricSignatureFilterModal, BiometricSignatureRow, TableMode } from "../../biometric-signature-table/models";

export enum Managements {
  RECURS = "RECURS"
}

export const MANAGEMENTS_TABS_TO_EXCLUDE: {[key in Managements]: TabType[]} = {
  RECURS: [TabType.payments,TabType.presentations]
}

export interface IDetailData {
	filterValue: Partial<BiometricSignatureFilterModal>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idEntity?: string,
	row: BiometricSignatureRow,
	mode: TableMode
}

// id
// nom_document
// metadades (enllaç a pàgina emergent amb llista d'atributs)
// nom_dispositiu
// repositori_documental
// nom_signant
// tipus_identificador_signant
// identificador_signant
// tipus_signatura
// aplicacio
// estat_peticio  (apareix el darrer estat, a la part dreta apareixerà l'històric d'estats on l'estat més recent d'aquest històric coincidirà amb l'estat de la petició)
// id_vid_signer
// id_repositori_documental
// data_creacio
// data_actualitzacio
// errors (enllaç a pàgina emergent amb llista d'errors)

export enum DetailCardFields {
	id = 'id',
	nomDocument = 'nom_document',
	metadades = 'metadades',
  nomDispositiu = 'nom_dispositiu',
	reintents = 'reintents',
	repositoriDocumental = 'repositori_documental',
  nomSignant = 'nom_signant',
	aplicacio = 'aplicacio',
	idVidSigner = 'id_vid_signer',
	idRepositoriDocumental = 'id_repositori_documental',
  dataCreacio = 'data_creacio',
	dataActualitzacio = 'data_actualitzacio',
  estatPeticio = 'estat_peticio',
	identificadorSignant = 'identificador_signant',
	tipusIdentificadorSignant = 'tipus_identificador_signant',
	tipusSignatura = 'tipus_signatura',
	errors = 'errors',
}
export type DetailCardFieldsT = keyof typeof DetailCardFields;

const DETAIL_CARD_TRANSLATIONS = "MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.CARD.FIELDS";
export const DETAIL_CARD_FORM_DATA: DetailCardData[] = [
  {
    id: DetailCardFields.id,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.id}`
  },
  // {
  //   id: DetailCardFields.nomDocument,
  //   fieldType: DetailFieldType.text,
	// 	fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomDocument}`
  // },
	{
    id: DetailCardFields.estatPeticio,
    fieldType: DetailFieldType.state,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.estatPeticio}`
  },
	// {
  //   id: DetailCardFields.nomDispositiu,
  //   fieldType: DetailFieldType.text,
	// 	fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomDispositiu}`
  // },
	// {
  //   id: DetailCardFields.idRepositoriDocumental,
  //   fieldType: DetailFieldType.text,
	// 	fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idRepositoriDocumental}`
  // },
	{
    id: DetailCardFields.repositoriDocumental,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.repositoriDocumental}`
  },
	{
    id: DetailCardFields.nomSignant,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomSignant}`
  },
  {
    id: DetailCardFields.tipusIdentificadorSignant,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.tipusIdentificadorSignant}`
  },
  {
    id: DetailCardFields.identificadorSignant,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.identificadorSignant}`
  },
  {
    id: DetailCardFields.tipusSignatura,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.tipusSignatura}`
  },
	{
    id: DetailCardFields.aplicacio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.aplicacio}`
  },
  {
    id: DetailCardFields.dataCreacio,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataCreacio}`
  },
	{
    id: DetailCardFields.dataActualitzacio,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataActualitzacio}`
  },
  {
    id: DetailCardFields.idVidSigner,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idVidSigner}`
  },
	{
    id: DetailCardFields.reintents,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.reintents}`
  },
	{
    id: DetailCardFields.metadades,//(enllaç a pàgina emergent amb llista d'atributs)
    fieldType: DetailFieldType.link,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.metadades}`,
		fieldValue: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.metadades}_value`

  },
	{
    id: DetailCardFields.errors,//(enllaç a pàgina emergent amb llista d'atributs)
    fieldType: DetailFieldType.link,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.errors}`,
		fieldValue: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.errors}_value`,
  },
]

const DETAIL_TABS_TRANSLATIONS = "MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.TABS"
export const DETAIL_TAB_DATA: TabData[] = [
/*   {
    tabType: TabType.documents,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
  }, */
	{
    tabType: TabType.custom,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
    tabCustomTemplate: 'documentsTab'
  },
]
