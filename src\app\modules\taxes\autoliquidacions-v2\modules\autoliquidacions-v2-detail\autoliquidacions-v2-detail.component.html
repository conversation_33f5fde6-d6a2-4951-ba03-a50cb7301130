<app-detail-page
	[pageTitle]="'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.PAGE_TITLE'"
	[statesTableTranslation]="'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'"
	[deleteButtonLabel]="'MODULE_TRACKING.COMPONENT_LIQUIDACIO.BUTTONS.DELETE_SELF_ASSESSMENT'"
	[detailCardTitle]="'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.CARD.TITLE'"

	[procedureData]="selfAssessment"
	[idEntity]="idSelfAssessment"
	[idTramit]="idSelfAssessment"
	[idReceipt]="idReceipt"

	[stateChangeFirstDate]="selfAssessment?.dataAlta"
	[stateChanges]="stateChanges"
	[stateIcon]="stateIcon"
	[stateLabel]="stateLabel"
	[detailCardFields]="detailCardFields"
	[detailTabInput]="detailTabInput"

	[currentIndex]="currentIndex"
	[totalRows]="totalRows"
	[backUrl]="navigateBackUrl"

	[showPresentationButtons]="showPresentationButtons"
	[downloadButtonsData]="downloadButtonsData"
	[showDeleteButton]="showDeleteButton"
	[deleteAllowRoles]="deleteAllowRoles"

	(getProcedureList)="getSelfAssessmentList()"
	(getProcedure)="getSelfAssessment()"
	(showErrors)="onShowErrors()"
	(showDeleteModal)="deleteModal()"

	(incrementIndex)="currentIndex = currentIndex + 1"
	(decrementIndex)="currentIndex = currentIndex - 1"
>
</app-detail-page>
