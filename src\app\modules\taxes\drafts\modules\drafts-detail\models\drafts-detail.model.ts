import { LazyLoadEvent } from "primeng/api";
import { IDraftsFilter, CanviEstat, States } from "../../../models/drafts.model";
import { CommonError } from "pt-ui-components-mf-lib";
import { DetailCardData, DetailFieldType } from "src/app/modules/shared/detail-card";
import { TabData, TabType } from "src/app/modules/shared/detail-tabs";

export interface IDraftsDetail {
	id:string;
	model: string;
	dataAlta:Date;
	dataModificacio:Date;
	nomPresentador: string;
	nifPresentador: string;
	nomTitular: string;
	nomSubjectePassiu: string;
	nifSubjectePassiu: string;
	nifTitular: string;
	errors: CommonError[];
	tipus:string;
	idioma: string;
	estat: States;
	canviEstat: CanviEstat[];
}

export interface IDetailData {
	filterValue: Partial<IDraftsFilter>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idSelfAssessment?: string
}

export enum DetailCardFields {
  id = 'id',
	model = 'model',
	impost = 'impost',
	dataAlta = 'dataAlta',
  dataModificacio = 'dataModificacio',
	nomPresentador = 'nomPresentador',
	nifPresentador = 'nifPresentador',
	nomTitular = 'nomTitular',
	nomSubjectePassiu = 'nomSubjectePassiu',
	nifSubjectePassiu = 'nifSubjectePassiu',
	nifTitular = 'nifTitular',
  estat = 'estat',
	tipus = 'tipus',
	idioma = 'idioma',
  dadesAddicionals = 'dadesAddicionals'
}
export type DetailCardFieldsT = keyof typeof DetailCardFields;

const DETAIL_CARD_TRANSLATIONS = "MODULE_TRACKING.MODULE_DRAFTS.DETAIL.CARD.FIELDS";
export const DETAIL_CARD_FORM_DATA:DetailCardData[] = [
  {
    id: DetailCardFields.id,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.id}`
  },
  {
    id: DetailCardFields.estat,
    fieldType: DetailFieldType.state,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.estat}`
  },
  {
    id: DetailCardFields.impost,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.impost}`
  },
  {
    id: DetailCardFields.tipus,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.tipus}`
  },
  {
    id: DetailCardFields.nomPresentador,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nomPresentador}`
  },
  {
    id: DetailCardFields.nifPresentador,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.nifPresentador}`
  },
  {
    id: DetailCardFields.model,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.model}`
  },
  {
    id: DetailCardFields.dataAlta,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataAlta}`
  },
  {
    id: DetailCardFields.dataModificacio,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataModificacio}`
  },
  {
    id: DetailCardFields.dadesAddicionals,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dadesAddicionals}`,
    fieldContainerClass: 'col-12'
  },
]

export type DetailTabsModel = {
  [K in TabType]?: string[];
}

export const DETAIL_TABS_MODELS:DetailTabsModel  = {
  [TabType.validations]: ["600"],
  [TabType.grouping]: ["600"],
  [TabType.notifications]: ["600"],
}

const DETAIL_TABS_TRANSLATIONS = "MODULE_TRACKING.MODULE_DRAFTS.DETAIL.TABS"
export const DETAIL_TAB_DATA: TabData[] = [
  {
    tabType: TabType.documents,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
  },
  {
    tabType: TabType.custom,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.ERRORS`,
    tabCustomTemplate: 'errorTab'
  },
  {
    tabType: TabType.custom,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.SELFASSESSMENT`,
    tabCustomTemplate: 'selfAssessmentTab'
  }
]

export interface DeleteModalData {
  idDraft: string,
	isCopy: boolean,
	confirmFn: (idReceipt?: string, idSelfAssessment?: string) => void
}
