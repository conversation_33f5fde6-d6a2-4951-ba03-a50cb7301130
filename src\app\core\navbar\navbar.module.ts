import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgbPopoverModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { MenubarModule } from 'primeng/menubar';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { NavbarComponent } from './navbar.component';

@NgModule({
	declarations: [NavbarComponent],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		MenubarModule,
		PtUiComponentsMfLibModule,
		NgbPopoverModule,
		TranslateModule.forChild(),
	],
	exports: [NavbarComponent],
})
export class NavbarModule {}
