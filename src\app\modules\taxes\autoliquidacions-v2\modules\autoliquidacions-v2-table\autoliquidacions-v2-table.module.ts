import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { AutoliquidacionsV2TableComponent } from './autoliquidacions-v2-table.component';
import { RouterModule, Routes } from '@angular/router';
import { ModalFilterModule } from 'src/app/modules/shared/modal-filter';

const routes: Routes = [
	{
		path: '', 
    component: AutoliquidacionsV2TableComponent,
		data: { 
			title: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.PAGE_TITLE', 
		}
	}
];

@NgModule({
	declarations: [
		AutoliquidacionsV2TableComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		ModalFilterModule,
		TranslateModule.forChild(),
		RouterModule.forChild(routes),
		PtUiComponentsMfLibModule
	]
})
export class AutoliquidacionsV2TableModule { }
