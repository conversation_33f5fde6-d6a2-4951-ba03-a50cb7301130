import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { 
	CommonError,
    DataStorageService, 
    PtAuthService, 
    PtConfirmationModalService, 
    PtModalErrorData, 
    PtModalErrorsService,  
    PtSelfAssessmentService
} from 'pt-ui-components-mf-lib';
import { States, TramitType } from '../models/managements-monitor.model';
import { StatesIcons } from 'src/app/core/models/config.model';

@Injectable({
	providedIn: 'root'
})
export class ManagementsMonitorService {

	constructor(
		public confirmationService: PtConfirmationModalService,
		public translateService:TranslateService,
		public modalErrorsService: PtModalErrorsService,
		public dataStorageService: DataStorageService,
		public modalService: NgbModal,
		public selfAssessmentService: PtSelfAssessmentService,
		public authService: PtAuthService
	) { }

	getStateIcon = (
		state: string,
		mode: 'detail' | 'self-assessment'
	): string => {
		switch (state?.toUpperCase()) {
			case States.TRAMITAT:
			case States.CONSOLIDAT:
				return StatesIcons.SUCCESS;
			case States.ESBORRANY:
				return StatesIcons.WAITING_WARNING;
			case States.TRAMITANT:
			case States.CONSOLIDANT:
				return mode === 'self-assessment'
					? StatesIcons.SPINNER
					: StatesIcons.WAITING_WARNING;
			case States.GENERAT:
			case States.PENDENT_PAGAMENT:
				return StatesIcons.CLOSE_INFO;
			case States.CONSOLIDACIO_ERROR:
			case States.ERROR:
			case States.TRAMITACIO_ERROR:
				return StatesIcons.ERROR;
			default:
				return '';
		}
	};

  	showErrors = (tramit: string, errorsRows: CommonError[]): void => { 
		if(errorsRows?.length > 0){
			// Open modal
			if(tramit === 'SIGNATURA'){
				errorsRows.forEach((error: CommonError) => {
					error.technicalDescription = this.translateService.instant('UI_COMPONENTS.EXCEPTIONS.MSG.' + error?.technicalDescription)
				})
			}
			const errorData:PtModalErrorData = {
				techMode: true,
				errors: errorsRows,
			}
			
			this.modalErrorsService.openModal(errorData);
		}
	}

	tramitTranslation(tramit: string): string{
		if(!Object.values(TramitType).includes(tramit as TramitType)){
			return '-'
		}

		return this.translateService.instant(`MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.ROWS.TRAMIT.${tramit}`);
	}
}