import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TearcMonitorComponent } from './tearc-monitor.component';
import { RouterModule, Routes } from '@angular/router';
import { LazyElementsModule } from '@angular-extensions/elements';

const routes: Routes = [
	{
		path: '', component: TearcMonitorComponent,
		data: { 
			title: '',
			isElementVisible: false
		}
	}
];

@NgModule({
	declarations: [
		TearcMonitorComponent
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	],
	exports: [
		RouterModule
	]
})
export class TearcMonitorModule { }
