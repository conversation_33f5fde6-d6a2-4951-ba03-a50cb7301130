import { environment } from 'src/environments/environment';
import { Component } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';

@Component({
	selector: 'app-usuaris',
	templateUrl: './usuaris.component.html',
	styleUrls: ['./usuaris.component.sass']
})
export class UsuarisComponent {

	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlSeguretatCss;

	constructor(
		private webComponentService: WebComponentsService
  ) {
		this.webComponentService.setWebComponentStyle(this.wcUrlCss, 'seguretat');
	}
}
