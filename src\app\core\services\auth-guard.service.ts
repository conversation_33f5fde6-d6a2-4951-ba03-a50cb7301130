import { Injectable, isDevMode } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate } from '@angular/router';
import {
    DataStorageService,
    PtAuthService,
    PtLoginResponse,
    PtLoginService,
    PtUser,
    UserRoles
} from 'pt-ui-components-mf-lib';
import { PT_USER } from '../models/config.model';
import { ValidateSessionTokenService } from './validate-session-token.service';

@Injectable({
    providedIn: 'root'
})
export class AuthGuardService implements CanActivate {

    constructor(
        private authService: PtAuthService,
        private loginService: PtLoginService,
        private validateSessionService: ValidateSessionTokenService,
        private dataStorageService: DataStorageService
    ) {}

    async canActivate(route: ActivatedRouteSnapshot):Promise<boolean> {    
        this.formatDispatcherUrlOnDevMode();

        const allowRoles = route.data.allowRoles;
        const isDispatcher = route.data.isDispatcher;
        const isSimulatedLogin = route.data.isSimulatedLogin;

        if (!this.validateSessionService.isValidToken()) {
            // No valid token -> do login to get a new token
            const response: PtLoginResponse = await this.loginService.login(true);
            const userRol = response?.content?.usuario?.rol;
            
            if (isDispatcher || isSimulatedLogin){
                this.dataStorageService.deleteItem(PT_USER);
            } else {
                // Set pt-user for webcomponents which are not in dispatcher
                this.dataStorageService.setItem(PT_USER,this.setUserData(response));
            }
            
            return this.isAllowed(allowRoles, userRol);
        }

        const sessionStorage = this.authService.getSessionStorageUser();
        
        if (isSimulatedLogin){
            this.dataStorageService.deleteItem(PT_USER);
        } else if (!isDispatcher) {
            // Set pt-user for webcomponents which are not in dispatcher
            this.dataStorageService.setItem(PT_USER,sessionStorage);
        }

        return this.isAllowed(allowRoles, sessionStorage?.rol);
    }
    
    private formatDispatcherUrlOnDevMode() {
        if(isDevMode() && location.pathname.includes('/ca/')) {
            location.pathname = location.pathname.replace('/ca/','');
        }
    }

    private isAllowed(allowRoles: UserRoles[], userRol: string): boolean {
        if (!allowRoles) return true;

        return allowRoles.some(role => role === userRol);
    }

    private setUserData = (response:PtLoginResponse): PtUser => Object.assign(
		{},
		response?.content?.usuario,
		{
			token:response?.content?.tokenJwt,
			tokenExpiration: response?.content?.tokenExpiration
		}
	)
}