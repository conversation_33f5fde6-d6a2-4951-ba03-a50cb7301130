var genericResponsive = {
    mailRegex : /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/,
    renderNewSection: function (element)
    {
        //Buscar el section parent de id
        section = element.closest("section");
        classes = section.attr('class')
        //Crear un section hermano y poner html resultante
        section.after("<section class='padding-xs padding-sm padding-md'><article class='container fullcontainer-xs'><div class='row'>"+
            element.html()+"</div></article></section>");
        $(element).remove();
    },
    dispositiu: function ()
    {
        //Bootstrap definition:
        //Extra small devices Phones ()	Small devices Tablets (≥768px)	Medium devices Desktops (≥992px)	Large devices Desktops (≥1200px)
        if($(window).width() < 768)
        {
            return "mobil";
        }
        else if($(window).width() < 992)
        {
            return "tablet";
        }
        else if($(window).width() < 1200)
        {
            return "escritoriPetit";
        }
        else
        {
            return "escritori";
        }
    },
    esMobil: function(){
        return "mobil" == genericResponsive.dispositiu();
    },
    enviarAmic: function (element)
    {
        function captcha(e){
            $("#myCaptchaResponse").hide();
            var flag = true;
            var nom = $("#nomF").val();
            if(nom == '') {
                $("#msgNameError").show();
                flag = false;
            } else {
                $("#msgNameError").hide();
            }
            var email = $("#mailClientF").val();
            var truEma = genericResponsive.mailRegex.test(email);
            if(truEma == false) {
                flag = false;
                $("#msgEmailError").show();
            } else {
                $("#msgEmailError").hide();
            }
            var capthaValue = $(".formAmic1 .g-recaptcha-response").val();
            if(capthaValue == '') {
                $("#msgCaptchaError").show();
                flag = false;
            } else {
                $("#msgCaptchaError").hide();
            }
            if(flag == true) {
                var postdata = $('.formAmic1').serialize();
                $.ajax({
                    type: "POST",
                    cache: false,
                    async: false,
                    url: "/handleForms",
                    data: postdata,
                    dataType: "json",
                    error: function(){
                        $("#msgKO").show();
                        $(".formAmic1").trigger("reset");
                        $(".formAmic1").toggle();
                        $("#resultMailAmic").toggle();
                        $("#msgCaptchaError").hide();
                    },
                    success: function(data){
                        if (data.res === "ko" && data.error != '') {
                            document.getElementById('msgCaptchaError').innerHTML=data.error;
                            $("#msgCaptchaError").show();
                        } else {
                            if (data.res === "ko" && data.error == '') {
                                $("#msgKO").show();
                            } else {
                                $("#msgOK").show();
                            }
                            //reset formulari
                            $(".formAmic1").trigger("reset");
                            $(".formAmic1").toggle();
                            $("#resultMailAmic").toggle();
                            $("#msgCaptchaError").hide();
                            resetAmic();
                        }
                    }
                });
                e.preventDefault();
            } else {
                e.preventDefault();
            }
        }
        var url = document.URL;
        $("#urlPage").val(unescape(url));
        $("#titolpagina").val($(document).prop('title'));
        $("#sendMailAmic").click(captcha);
        $("#OKMailAmic").click(function() {
            $("#enviarAmicTab").trigger("click");
            setTimeout(function() {
                $("#resultMailAmic").toggle();
                $("#msgOK").hide();
                $(".formAmic1").toggle();
            }, 1000);
        });
    }
    ,
    twitter: function()
    {
        width=575;
        height=400;
        left=($(window).width()-width)/2;
        dalt=($(window).height()-height)/2;
        url= "http://twitter.com/share?text="+encodeURI(document.title)+"&url="+escape(window.location.href);
        opts="status=1"+",width="+width+",height="+height+",top="+dalt+",left="+left;
        window.open(url,"Twitter",opts);
        event.preventDefault();
    },

    linkedin: function ()
    {
        width=575;
        height=400;
        left=($(window).width()-width)/2;
        dalt=($(window).height()-height)/2;
        url= "https://www.linkedin.com/cws/share?"+encodeURI(document.title)+"&url="+escape(window.location.href);
        opts="status=1"+",width="+width+",height="+height+",top="+dalt+",left="+left;
        window.open(url,"Linkedin",opts);
        event.preventDefault();

    },


    facebook: function ()
    {
        width=575;
        height=400;
        left=($(window).width()-width)/2;
        dalt=($(window).height()-height)/2;
        url="https://www.facebook.com/sharer/sharer.php?u="+escape(window.location.href);
        opts="status=1"+",width="+width+",height="+height+",top="+dalt+",left="+left;
        window.open(url,"Facebook",opts);
        event.preventDefault();
    },
    whatsapp: function ()
    {
        width=575;
        height=400;
        left=($(window).width()-width)/2;
        dalt=($(window).height()-height)/2;
        if(this.esMobil()){
            url= "https://wa.me/?text="+escape(window.location.href);
        } else {
            url= "https://web.whatsapp.com/send?text="+escape(window.location.href);
        }
        opts="status=1"+",width="+width+",height="+height+",top="+dalt+",left="+left;
        window.open(url,"WhatsApp",opts);
        event.preventDefault();
    },

    telegram: function()
    {
        width=575;
        height=400;
        left=($(window).width()-width)/2;
        dalt=($(window).height()-height)/2;
        url= "https://telegram.me/share/url?text="+encodeURI(document.title)+"&url="+escape(window.location.href);
        opts="status=1"+",width="+width+",height="+height+",top="+dalt+",left="+left;
        window.open(url,"Telegram",opts);
        event.preventDefault();
    },

    getWidth: function(element) //Función que obtiene el width de una img en IE8 @TODO Hacerla genérica para cualquier propiedad
    {
        var img = new Image();
        img.src = element.prop("src");

        return img.width;

    },
    widthImatge: function(element)
    {
        var width = element.attr("width");
        if(width == null){
            width = element.prop("naturalWidth");
            if(width == null){ //En IE8
                width = genericResponsive.getWidth(element);
            }
        }
        var parent = element.closest("div");

        var who = this.esMobil();

        if (who) {
            parent.addClass("col-xs-12");
        } else {

            if (width >= 750 || width == 0) {
                parent.addClass("col-sm-12");

            } else if (width >= 500) {

                parent.addClass("col-sm-8");

            } else if (width >= 300) {

                parent.addClass("col-sm-6");

            }
            else {

                parent.addClass("col-sm-4");
            }
        }
    },
    reorderList: function(ul)
    {
        var i=0;
        var elementsLlista = 0;
        var llistaElements = ul.find("> li");
        if(llistaElements.length != 0)
        {
            if((llistaElements.length %2) == 1){
                elementsLlista = Math.floor(llistaElements.length/2);
            }
            else{
                elementsLlista = (llistaElements.length/2)-1;
            }
            for (i=0; i<llistaElements.length; i++){
                if(i==elementsLlista){
                    $(llistaElements[i]).addClass("ultim-esq");
                    $(llistaElements[i+1]).addClass("primer-dret");
                }
            }
        }
    },
    heightMenuMobil: function(element){
        if(this.esMobil()){
            var heightTitol = element.height();
            //Anem a Prendre la Referència d'altura de la imatge de l'esquerra
            var heightImatge = $(".coloca button").height();
            //Buscar el container parent de id
            container = element.closest(".container");
            if(heightTitol > heightImatge){
                if(heightTitol > (heightImatge*2)){
                    container.addClass("three-lines");
                    $(".capcelera_basica").addClass("three-lines");
                    $("form.navbar-form.navbar-left.primer").addClass("three-lines");
                } else {
                    container.addClass("two-lines");
                    $(".capcelera_basica").addClass("two-lines");
                    $("form.navbar-form.navbar-left.primer").addClass("two-lines");
                }
            }
        }
    },
    autoHeight: function(element){
        if($(element).hasClass("mob")){
            if(this.esMobil()){
                var maximaAltura = 0;
                $(element).children().each(function() {
                    if(maximaAltura<$(this).height())
                    {
                        maximaAltura = $(this).height();
                        if(console.log(maximaAltura));
                    }
                });
                $(element).children().height(maximaAltura);
            }
        }
        else if($(element).hasClass("tots")){
            var maximaAltura = 0;
            $(element).children().each(function() {
                if(maximaAltura<$(this).height())
                {
                    maximaAltura = $(this).height();
                    if(console.log(maximaAltura));
                }
            });
            $(element).children().height(maximaAltura);
        }
        else {
            if(!this.esMobil()){
                var maximaAltura = 0;
                $(element).children().each(function() {
                    if(maximaAltura<$(this).height())
                    {
                        maximaAltura = $(this).height();
                        if(console.log(maximaAltura));
                    }
                });
                $(element).children().height(maximaAltura);
            }
        }
    },
    treuPaddings: function(element){
        if(this.esMobil()){
            $(element).closest("section").removeClass("padding-xs padding-sm padding-md");
        }else if($(element).hasClass("tots-dis")){
            $(element).closest("section").removeClass("padding-xs padding-sm padding-md");
        }else if($(element).hasClass("desk-dis")){
            $(element).closest("section").removeClass("padding-xs padding-sm padding-md");
        }
    },
    metaAnalitica: function(element){
        var dataNom = $(element).attr("data-nom");
        var value = $(element).text();
        $("meta[name='WT.z_idioma']").after(function(){
            return "<meta name=\"" + dataNom + "\" content=\"" + value + "\" />";
        });
    },
    is_Ie: function(){
        /**
         * The method tests the user agent for Internet explorer 11 or older
         * 
         * @param  It receives no @param.
         * @return {boolean}
         *     
         */
        var ua = window.navigator.userAgent;

        var msie = ua.indexOf('MSIE ');
        if (msie > 0) {
            // IE 10 or older => return version number
            var ie10 = parseInt(ua.substring(msie + 5, ua.indexOf('.', msie)), 10);
            return true;
        }

        var trident = ua.indexOf('Trident/');
        if (trident > 0) {
            // IE 11 => return version number
            var rv = ua.indexOf('rv:');
            var ie11 = parseInt(ua.substring(rv + 3, ua.indexOf('.', rv)), 10);
            return true;
        }

        var edge = ua.indexOf('Edge/');
        if (edge > 0) {
            // Edge => return version number
            var Edge = parseInt(ua.substring(edge + 5, ua.indexOf('.', edge)), 10);
            return false
        }

        // other browser
        return false;
    }
}

/*

function isNumber(n)
{
   return n == parseFloat(n);
}
function isOdd(n)
{
   return isNumber(n) && (Math.abs(n) % 2 == 1);
}
$('ul.llista-subhome').each(function() {

	var i=0;
	var elementsLlista = 0;
	var llistaElements = this.querySelectorAll("li");

	if(isOdd(llistaElements.length)){
		elementsLlista = Math.floor(llistaElements.length/2);
	}
	else{
		elementsLlista = (llistaElements.length/2)-1;
	}
	console.log(elementsLlista);
	for (i=0; i<llistaElements.length; i++){
		if(i==elementsLlista){
			if(navigator.userAgent.toLowerCase().indexOf('firefox') > -1){
				$(llistaElements[i]).addClass("ultim-esq");
				$(llistaElements[i+1]).addClass("primer-dret");
			}
			else if(navigator.userAgent.toLowerCase().indexOf('chrome') > -1){
				if(isOdd(llistaElements.length)){
					$(llistaElements[i]).addClass("ultim-esq");
					$(llistaElements[i+1]).addClass("primer-dret");
				}
				else{
					$(llistaElements[i]).addClass("ultim-esq");
				}
			}
			else{
				$(llistaElements[i]).addClass("ultim-esq");
				$(llistaElements[i+1]).addClass("primer-dret");
			}
		}
	}
});
*/
var controlCaptcha; //callBack function
var widgetAmic; //recatpcha object for "enviar a un amic"
var resetAmic = function() { //reset function for "enviar a un amic"
    grecaptcha.reset(widgetAmic);
}
//FUNCION PARA CREAR RECURSIVAMENTE LAS FILA QUE FALTAN
$(document).ready(function(){
    //Creació section de color
    $(".createDiv").each(function(){
        try{
            genericResponsive.renderNewSection($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga renderNewSection: "+e);
        }
    })
    //Enviar a un amic
    $("#e-amic").each(function(){
        try{
            genericResponsive.enviarAmic($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga enviarAmic: "+e);
        }
    })
    //Funcionalitat twitter
    $(".eventTwitter").each(function(){
        try{
            $(this).bind('click', function(){
                genericResponsive.twitter();
            });
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga twitter: "+e);
        }
    })

    //Funcionalitat facebook
    $(".eventFacebook").each(function(){
        try{
            $(this).bind('click', function(){
                genericResponsive.facebook();
            });

        }
        catch(e)
        {
            if(console)
                console.log("Problema carga facebook: "+e);
        }
    })

    //Funcionalitat Linkedin

    $(".eventLinkedin").each(function(){
        try{
            $(this).bind('click', function(){
                genericResponsive.linkedin();
            });

        }
        catch(e)
        {
            if(console)
                console.log("Problema carga linkedin: "+e);
        }
    })
    //Funcionalitat Whatsapp
    $(".eventWhatsapp").each(function(){
        try{
            $(this).bind('click', function(){
                genericResponsive.whatsapp();
            });

        }
        catch(e)
        {
            if(console)
                console.log("Problema carga Whatsapp: "+e);
        }
    })

    //Funcionalitat telegram
    $(".eventTelegram").each(function(){
        try{
            $(this).bind('click', function(){
                genericResponsive.telegram();
            });
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga telegram: "+e);
        }
    })

    //Funcionalitat widthImatge
    $(".widthResponsive").each(function(){
        var $image = $(this);
        var functionWidthImatge = function(image){
            try {
                genericResponsive.widthImatge($image);
            }catch(e){
                if(console){
                    console.log("Problema carga widthImatge: "+e);
                }
            } 
        }
        var isLoaded = this.complete && this.naturalHeight !== 0;
        if(isLoaded){
            functionWidthImatge($image);
        }else{
            setTimeout(function() {
                functionWidthImatge($image);   
            }, 1000);
        }
    });

    //Reordering list
    $('ul.llistaReorder').each(function() {
        try{
            genericResponsive.reorderList($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga llistaReorder: "+e);
        }
    })

    //HeightMenuMobil
    $(".logoPortal a").each(function() {
        try{
            genericResponsive.heightMenuMobil($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga heightMenuMobil: "+e);
        }
    })
    //AutoHeightElements
    $(".gen-height").each(function() {
        try{
            genericResponsive.autoHeight($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema carga AutoHeightElements: "+e);
        }
    })
    $(".treu-paddings-grid").each(function() {
        try{
            genericResponsive.treuPaddings($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema eliminant paddings de la grid: "+e);
        }
    })
    $(".hiddenAnalitica").each(function(){
        try{
            genericResponsive.metaAnalitica($(this));
        }
        catch(e)
        {
            if(console)
                console.log("Problema creant metadades d'analitica: "+e);
        }
    })
    /**
     * Corrección para substituir los carácteres 'U0023' por '#' en el atributo href de los enlaces
     * Añadiendo la clase 'avoidEscapeU0023' al enlace evitamos que se subsituya U0023 con #
     */
     $('a').each(function() {
        var link = $(this).attr('href');

        if ( !$(this).hasClass("avoidEscapeU0023")
             && typeof link !== 'undefined'
             && link !== null) {
            if (link.search('U0023') !== -1) {
                link = link.replace('U0023', '#');
                $(this).attr('href', link);
                console.log("Replaced U0023 with # in link: "+link);
            }
        }
    });
    //reCatpcha
    var captchaList = document.getElementsByClassName("captchaContainer");
    if (captchaList.length > 0) {
        var locale = document.documentElement.lang || 'ca';
        if (locale.length > 1) {
            locale = locale.substring(0, 2);
        }
        var captchaRenderList = new Array();
        controlCaptcha = function() {
            for (var i = 0; i < captchaList.length; i++) {
                if (captchaList[i].id == "id_envia_amic_form") {
                    widgetAmic = grecaptcha.render(captchaList[i], {
                        "sitekey": ENTORN_KEY.GOOGLE_CAPTCHA,
                        "hl": locale,
                        'expired-callback': resetAmic
                    })
                } else {
                    grecaptcha.render(captchaList[i], {
                        "sitekey": ENTORN_KEY.GOOGLE_CAPTCHA,
                        "hl": locale
                    })
                }
            }
        }
        if (typeof grecaptcha == "undefined") {
            var importgr = document.createElement("script");
            importgr.src = "https://www.google.com/recaptcha/api.js?onload=controlCaptcha&render=explicit";
            importgr.setAttribute('async', '');
            importgr.setAttribute('defer', '');
            document.getElementsByTagName("head")[0].appendChild(importgr);
        }
    }
    /**
     * The function gives a minimal height to a page in explorer
     * 
     * It receives no @param.
     * @return doesn't return anything.
     * 
     */
    function pageMinHeightIE() {
        if(genericResponsive.is_Ie()){
            var headerHeight, subcapcaleraHeight, footerHeight, main
            /* if there is a header, get it`s height, else set to 0 */
            $('header').length > 0 ? headerHeight = $('header').innerHeight() : headerHeight = 0
            /* if there is a .fpca-subcapcalera, get it`s height, else set to 0 */
            $('.fpca-subcapcalera').length > 0 ? subcapcaleraHeight = $('.fpca-subcapcalera').innerHeight() : subcapcaleraHeight = 0
            /* if there is a footer, get it`s height, else set to 0 */
            $('footer').length > 0 ? footerHeight = $('footer').innerHeight() : footerHeight = 0

            /* if there is a #main, set it`s a min-height */
            if($('#main').length > 0){
                var minHeight = $(window).innerHeight() - (headerHeight + subcapcaleraHeight + footerHeight)
                $('#main').css('min-height', minHeight)
            }
        }
    }
    pageMinHeightIE();
    $(window).resize(pageMinHeightIE);

    // Accesibilidad usuario lector de pantalla
    // Poner el foco en el enlace para ir al contenido principal
    if(document.getElementsByClassName("skip-main")[0]!=null){
        /*
            Se comenta este trozo de codigo a raiz de la incidencia https://jira.altran-kas.com/browse/A07ID387-1392
        */
        //document.getElementsByClassName("skip-main")[0].focus();
    }
});
