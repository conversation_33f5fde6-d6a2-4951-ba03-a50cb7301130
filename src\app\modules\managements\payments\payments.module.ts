import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PaymentsComponent } from './payments.component';
import { RouterModule, Routes } from '@angular/router';
import { LazyElementsModule } from '@angular-extensions/elements';
import { PaymentsDetailComponent } from './payments-detail/payments-detail.component';


const routes: Routes = [
	{
		path: '', component: PaymentsComponent,
		data: { 
			title: 'MODULE_TRACKING.COMPONENT_GESTIONS.PAYMENTS_PAGE_TITLE',
			isElementVisible: false
		}
	},
  {
    path: 'detall/:id', component: PaymentsDetailComponent,
    		data: { 
			title: 'MODULE_TRACKING.COMPONENT_GESTIONS.PAYMENTS_DETAIL_TITLE',
			isElementVisible: false
		}
  },
];

@NgModule({
  declarations: [
		PaymentsComponent,
		PaymentsDetailComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(routes),
		LazyElementsModule
	],
	exports: [
		RouterModule
	]
})

export class PaymentsModule { }
