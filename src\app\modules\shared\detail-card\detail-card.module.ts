import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { DetailCardComponent } from './detail-card.component';

@NgModule({
	declarations: [
		DetailCardComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule
	],
  exports: [DetailCardComponent]
})
export class DetailCardModule { }
