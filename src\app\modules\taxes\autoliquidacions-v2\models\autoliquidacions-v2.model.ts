import { LazyLoadEvent } from 'primeng/api';
import { PtTableLinkTemplate, iCommonError } from 'pt-ui-components-mf-lib';

export const AUTOLIQUIDACIONS_V2_FILTER_STORAGE = 'autoliquidacions-v2-filter-storage';
export const AUTOLIQUIDACIONS_V2_DETAIL_STORAGE = 'autoliquidacions-v2-detail-storage';

export enum States {
	GENERAT = 'GENERAT',
	ESBORRANY = 'ESBORRANY',
	NO_PRESENTAT = 'NO_PRESENTAT',
	PRESENTANT = 'PRESENTANT',
	PENDENT_PRESENTACIO ="PENDENT_PRESENTACIO",
	PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
	PRESENTAT = 'PRESENTAT',
	PAGAT = 'PAGAT',
	PAGANT = 'PAGANT',
	PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
	PAGAMENT_ERROR = 'PAGAMENT_ERROR',
	PAGAMENT_CANCELLAT = 'PAGAMENT_CANCELLAT',
	NOTIFICANT_PAGAMENT = "NOTIFICANT_PAGAMENT",
	NOTIFICACIO_ERROR = 'NOTIFICACIO_ERROR',
	PAGAMENT_NOTIFICAT = 'PAGAMENT_NOTIFICAT',
	TRAMITAT = 'TRAMITAT',
	TRAMITANT = 'TRAMITANT',
	TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
	CONSOLIDANT = 'CONSOLIDANT',
	CONSOLIDAT = 'CONSOLIDAT',
	CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
	ERROR = 'ERROR',
	ESBORRANY_VALIDANT = 'ESBORRANY_VALIDANT',
	ESBORRANY_VALIDAT = 'ESBORRANY_VALIDAT',
	ESBORRANY_ERROR = 'ESBORRANY_ERROR',
	ESBORRANY_AGRUPANT = 'ESBORRANY_AGRUPANT',
	ESBORRANY_AGRUPANT_ERROR = "ESBORRANY_AGRUPANT_ERROR",
	ESBORRANY_AGRUPAT = 'ESBORRANY_AGRUPAT',
	DELETE = 'DELETE'
}

export type StatesT = keyof typeof States;
export type StatesValues<States> = States[keyof States];

export enum TaxStates {
	IIIMA = 'IIIMA',
	ITPAJ = 'ITPAJ',
	IGEC = 'IGEC',
	ISBEE = 'ISBEE',
	IANP = 'IANP',
	TFJA = 'TFJA',
	IEGI = 'IEGI',
	IENA = 'IENA',
}
export type TaxStatesT = keyof typeof TaxStates;
export type TaxStatesValues<TaxStates> = TaxStates[keyof TaxStates];

export interface ISelfAssessmentV2 {
	idAutoliquidacio: string,
	idTramit: string,
	idTramitacio: string,
	numJustificant: string,
	impost: string,
	model: string,
	periode: string,
	exercici: string,
	declaracioExempcio: boolean,
	baseImposableTotal: number,
	dataAlta: Date,
	dataModificacio: Date,
	nomPresentador: string;
  nifPresentador: string;
  nomSubjectePassiu: string;
  nifSubjectePassiu: string;
	errors: iCommonError[],
	tipus: string,
	idioma: string,
	estat: string,
	canviEstat: CanviEstat[],
	totalIngressar: number,
	presentacioEstat: States,
	pagamentEstat: States,
	idTramitacioPagament: string,
	encryptedIdMFPT: string,
	idAgrupacio: string
}

export interface SelfAssessmentV2Row extends ISelfAssessmentV2 {
	stateRow: PtTableLinkTemplate;
}

export interface CanviEstat {
	estatAnterior: string,
	estat: string,
	dataCanvi: Date
}

/**
 * Autoliquidacions: search filters
 */
export interface ISelfAssessmentV2Filter {
	dateFromForm: Date;
	dateToForm: Date;
	dateFrom: number;
	dateTo: number;
	idAutoliquidacio: string;
	idTramit: string;
	numJustificant: string;
	model: string;
	periode: string;
	exercici: string;
	declaracioExempcio: boolean;
	nifPresentador: string;
	nifSubjectePassiu: string;
	nifTitular: string;
	estat: string[];
	totalIngressar: number;
	idAgrupacio: string;
	tipus: string;
}

export class SelfAssessmentV2Request {
	filter: Partial<ISelfAssessmentV2Filter>;
	options: LazyLoadEvent;

	constructor(params: Partial<ISelfAssessmentV2Filter>, options: LazyLoadEvent) {
		const df = new Date(params.dateFromForm);
		const dt = new Date(params.dateToForm);
		const from: Date = new Date(df.getFullYear(), df.getMonth(), df.getDate(), 0, 0, 0);
		const to: Date = new Date(dt.getFullYear(), dt.getMonth(), dt.getDate() + 1, 0, 0, 0);

		this.filter = {
			...params,
			dateFrom: from.getTime(),
			dateTo: to.getTime(),
			estat: params?.estat?.length > 0 ? params?.estat : null
		}
		this.options = options
	}
}

/**
 * Enum states
 */
export enum PaymentMethodsV2 {
	BANK_ACCOUNT = 'C',
	CREDIT_CARD = 'T',
	BIZUM = 'B',
}
