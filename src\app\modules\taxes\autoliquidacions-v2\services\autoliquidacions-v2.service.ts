import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { 
	CommonError,
    DataStorageService, 
    PtAuthService, 
    PtConfirmationModalService, 
    PtModalErrorData, 
    PtModalErrorsService,  
    PtSelfAssessmentService
} from 'pt-ui-components-mf-lib';
import { States } from '../models/autoliquidacions-v2.model';
import { StatesIcons } from 'src/app/core/models/config.model';
import { ModalWarningMsgComponent } from 'src/app/modules/shared/modal-warning-msg';
import { DeleteModalData } from '../../autoliquidacions-v1/models/autoliquidacions-v1.model';

@Injectable({
	providedIn: 'root'
})
export class AutoliquidacionsV2Service {

	constructor(
		public confirmationService: PtConfirmationModalService,
		public translateService:TranslateService,
		public modalErrorsService: PtModalErrorsService,
		public dataStorageService: DataStorageService,
		public modalService: NgbModal,
		public selfAssessmentService: PtSelfAssessmentService,
		public authService: PtAuthService
	) { }

	getStateIcon = (
		state: string,
		mode: 'detail' | 'self-assessment'
	): string => {
		switch (state?.toUpperCase()) {
			case States.PRESENTAT:
			case States.CONSOLIDAT:
			case States.PAGAT:
			case States.TRAMITAT:
			case States.ESBORRANY_VALIDAT:
			case States.ESBORRANY_AGRUPAT:
			case States.PAGAMENT_NOTIFICAT:
				return StatesIcons.SUCCESS;
			case States.PRESENTANT:
			case States.CONSOLIDANT:
			case States.PAGANT:
			case States.TRAMITANT:
			case States.NOTIFICANT_PAGAMENT:
				return mode === 'self-assessment'
					? StatesIcons.SPINNER
					: StatesIcons.WAITING_WARNING;
			case States.ESBORRANY:
			case States.PAGAMENT_CANCELLAT:
			case States.ESBORRANY_VALIDANT:
			case States.ESBORRANY_AGRUPANT:
				return StatesIcons.WAITING_WARNING;
			case States.PRESENTACIO_ERROR:
			case States.CONSOLIDACIO_ERROR:
			case States.ERROR:
			case States.PAGAMENT_ERROR:
			case States.TRAMITACIO_ERROR:
			case States.ESBORRANY_ERROR:
			case States.NOTIFICACIO_ERROR:
			case States.ESBORRANY_AGRUPANT_ERROR:
				return StatesIcons.ERROR;
			case States.GENERAT:
			case States.NO_PRESENTAT:
			case States.PENDENT_PAGAMENT:
			case States.PENDENT_PRESENTACIO:
			case States.DELETE:
				return StatesIcons.CLOSE_INFO;
			default:
				return '';
		}
	};

  showErrors = (errorsRows: CommonError[]): void => { 
		if(errorsRows?.length > 0){
			// Open modal
			errorsRows.map((error: CommonError) => {
				if (!error.technicalDescription) {
					error.technicalDescription = error.description || '';
				}
				if (!error.technicalCode) {
					error.technicalCode = error.code || '';
				}
			});			
			const errorData:PtModalErrorData = {
				techMode: true,
				errors: errorsRows,
			}			
			this.modalErrorsService.openModal(errorData);
		}
	}

	deleteModal = (deleteData: DeleteModalData): void => {
		if (!this.selfAssessmentService.isAllowDeleteState(deleteData.state)) {
			// Open modal
			const modalRef = this.modalService.open(ModalWarningMsgComponent, {
				size: 'lg',
				windowClass: '',
			});

			// Modal input data
			modalRef.componentInstance.state = deleteData.state;
			modalRef.componentInstance.stateTraslation = 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.STATES'

			return;
		}

		const msgType = deleteData.idReceipt ? 'RECEIPT' : 'NO_RECEIPT';
		this.confirmationService.openModal({
			severity: 'warning',
			title: 'MODULE_TRACKING.COMPONENT_LIQUIDACIO.BUTTONS.DELETE_SELF_ASSESSMENT',
			subtitle: this.translateService.instant(
				`MODULE_TRACKING.COMPONENT_LIQUIDACIO.CONFIRMATION_MESSAGES.DELETE_SELF_ASSESSMENT_${msgType}`,
				{ numJustificant: deleteData.idReceipt }
			),
			confirmLabel: 'UI_COMPONENTS.BUTTONS.CONFIRM',
			confirmFn: deleteData.confirmFn.bind(
				this,
				deleteData.idReceipt,
				deleteData.idSelfAssessment
			),
		});
	};
}