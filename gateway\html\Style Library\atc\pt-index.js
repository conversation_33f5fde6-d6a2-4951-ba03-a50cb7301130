$(document).ready(function () {
	var url = window.location.href;
	console.log('url.indexOf(/ca/): ', url.indexOf('/ca/'));
	if (url.indexOf('/ca/') !== -1) {
		// Current lang = CA -> Change to ES

		// Change link data
		$(document).find(".enllacIdiom").each(function (index, el) {
			$(el).attr("title", 'es');
			$(el).text('Castellano');
			$(el).attr("href", url.replace('/ca/', '/es/'));
		});
		// Redirect
		// url.replace('/ca/', '/es/');
		// window.location.href =
	} else {
		// Current lang = ES -> Change to CA

		// Change link data
		$(document).find(".enllacIdiom").each(function (index, el) {
			$(el).attr("title", 'ca');
			$(el).text('Català');
			$(el).attr("href", url.replace('/es/', '/ca/'));
		});
		// Redirect
		// url.replace('/es/', '/ca/');
		// window.location.href =
	}

	$('.enllacIdiom').on("click", function (e) {
		console.log('enllacIdiom clicked: ', e);
		var url = window.location.href;
		if (url.indexOf('/ca/') !== -1) {
			// Current lang = CA -> Change to ES

			// Change link data
			$(this).attr("title", 'es');
			$(this).text('Castellano');
			$(this).attr("href", url.replace('/ca/', '/es/'));
			// Redirect
			// url.replace('/ca/', '/es/');
			// window.location.href =
		} else {
			// Current lang = ES -> Change to CA

			// Change link data
			$(this).attr("title", 'ca');
			$(this).text('Català');
			$(this).attr("href", url.replace('/es/', '/ca/'));
			// Redirect
			// url.replace('/es/', '/ca/');
			// window.location.href =
		}
	});
});