import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { ExpedientesRoutes } from 'src/app/core/models/config.model';

const allowRoles = [UserRoles.ADMIN, UserRoles.TECHNICIAN];
const translateKey = 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_CASES';
const routes: Routes = [
	{
		path: '',
		redirectTo: ExpedientesRoutes.LIST,
		pathMatch: 'full',
	},
	{
		path: ExpedientesRoutes.LIST,
		data: {
			title: `${translateKey}.LIST.PAGE_TITLE`,
			//allowRoles,
		},
		loadChildren: () =>
			import('./expediente-list/expediente-list.module').then(
				(m) => m.ExpedienteListModule
			),
	},
	{
		path: ExpedientesRoutes.DETAIL_ID,
		data: {
			title: `${translateKey}.DETAIL.PAGE_TITLE`,
			//allowRoles,
		},
		loadChildren: () =>
			import('./expediente-detail/expediente-detail.module').then(
				(m) => m.ExpedienteDetailModule
			),
	},
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
})
export class ExpedientesRoutingModule {}
