{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "cli": {"analytics": false}, "version": 1, "newProjectRoot": "projects", "projects": {"pt-portal-tracking-gw": {"projectType": "application", "schematics": {"@schematics/angular:component": {"style": "sass"}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular-devkit/build-angular:browser", "options": {"outputPath": "dist/pt-portal-tracking-gw", "index": "src/index.html", "main": "src/main.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.app.json", "aot": true, "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/primeicons/primeicons.css", "node_modules/primeng/resources/themes/bootstrap4-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/bootstrap/dist/css/bootstrap.min.css", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css", "node_modules/pt-ui-components-mf-lib/lib/assets/pt-styles.min.css", "src/styles.sass"], "scripts": []}, "configurations": {"loc": {"optimization": false, "outputHashing": "none", "sourceMap": true, "namedChunks": false, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": false}, "dev": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.dev.ts"}], "optimization": false, "outputHashing": "none", "sourceMap": true, "namedChunks": false, "extractLicenses": false, "vendorChunk": false, "buildOptimizer": false}, "production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "optimization": true, "outputHashing": "none", "sourceMap": false, "namedChunks": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "budgets": [{"type": "initial", "maximumWarning": "2mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "6kb", "maximumError": "10kb"}]}}}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "options": {"browserTarget": "pt-portal-tracking-gw:build"}, "configurations": {"loc": {"browserTarget": "pt-portal-tracking-gw:build:loc", "proxyConfig": "proxy.conf.loc.json"}, "dev": {"browserTarget": "pt-portal-tracking-gw:build:dev", "proxyConfig": "proxy.conf.dev.json"}, "production": {"browserTarget": "pt-portal-tracking-gw:build:production"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "pt-portal-tracking-gw:build"}}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"main": "src/test.ts", "polyfills": "src/polyfills.ts", "tsConfig": "tsconfig.spec.json", "karmaConfig": "karma.conf.js", "assets": ["src/favicon.ico", "src/assets"], "styles": ["src/styles.sass"], "scripts": []}}, "lint": {"builder": "@angular-devkit/build-angular:tslint", "options": {"tsConfig": ["tsconfig.app.json", "tsconfig.spec.json", "e2e/tsconfig.json"], "exclude": ["**/node_modules/**"]}}, "e2e": {"builder": "@angular-devkit/build-angular:protractor", "options": {"protractorConfig": "e2e/protractor.conf.js", "devServerTarget": "pt-portal-tracking-gw:serve"}, "configurations": {"production": {"devServerTarget": "pt-portal-tracking-gw:serve:production"}}}}}}, "defaultProject": "pt-portal-tracking-gw"}