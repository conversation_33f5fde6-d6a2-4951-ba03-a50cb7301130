import { Component, Input, OnInit, OnDestroy } from '@angular/core';
import {
  iResponseDownloadFile,
	PtDocumentsService,
	PtTableColumn,
	PtTableColumnTemplate,
} from 'pt-ui-components-mf-lib';
import { IBiometricSignature } from '../../../../models/biometric-signature.model';
import { TableColumns } from '../../../biometric-signature-table/models';
import { takeUntil } from 'rxjs/operators';
import { BiometricSignatureDetailEndpointsService } from '../../services/biometric-signature-detail-endpoints.service';
import { Subject } from 'rxjs';

@Component({
	selector: 'app-bio-documents-tab',
	templateUrl: './bio-documents-tab.component.html',
})
export class BioDocumentsTabComponent implements OnInit, OnDestroy {
	@Input() set biometricSignature(value: IBiometricSignature) {
		this.setTableRows(value);
	}

	// Table
	tableColumns: PtTableColumn[] = [];
	tableRows: IBiometricSignature[] = [];

  private _unsubscribe: Subject<void> = new Subject();
	
  constructor(
		private documentsService: PtDocumentsService,
		private detailEndpointsService: BiometricSignatureDetailEndpointsService
	) {}

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

	ngOnInit(): void {
		this.tableColumns = this.setTableColumns();
	}

	private setTableColumns(): PtTableColumn[] {
		return [
			{
				id: TableColumns.idRepositoriDocumental,
				label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.idRepositoriDocumental}`,
				isResizable: false,
				isSortable: true,
				width: '25%',
				template: PtTableColumnTemplate.link,
			},
			{
				id: TableColumns.nomDocument,
				label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.nomDocument}`,
				isResizable: false,
				isSortable: true,
			},
		];
	}

	private setTableRows(signature: IBiometricSignature): void {
		this.tableRows = [
			Object.assign({}, signature, {
				[TableColumns.idRepositoriDocumental]: {
					id: `idRepositoriDocumental${signature.id}`,
					label: signature.id_repositori_documental,
					clickFn: this.downloadDocument.bind(this, signature.id_repositori_documental),
				},
			}),
		];
	}

	private downloadDocument(id: string): void {
		this.detailEndpointsService
			.downloadDocument(id)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response: iResponseDownloadFile) => {
				if (response?.content?.base64File) {
					this.documentsService.openFile(
						response.content.base64File,
						response.content.format
					);
				}
			});
	}
}
