apiVersion: v1
kind: ConfigMap
metadata:
  name: pt-portal-tracking-gw-config
  labels:
    name: pt-portal-tracking-gw
    app: pt-app
    type: gateway 
data:
  idp-metadata: |
    <?xml version="1.0" encoding="UTF-8"?>
    <!--
         This is example metadata only. Do *NOT* supply it as is without review,
         and do *NOT* provide it in real time to your partners.
    
         This metadata is not dynamic - it will not change as your configuration changes.
    -->
    <EntityDescriptor  xmlns="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:shibmd="urn:mace:shibboleth:metadata:1.0" xmlns:xml="http://www.w3.org/XML/1998/namespace" xmlns:mdui="urn:oasis:names:tc:SAML:metadata:ui" xmlns:req-attr="urn:oasis:names:tc:SAML:protocol:ext:req-attr" validUntil="2040-11-20T08:39:59.577Z" entityID="https://autenticaciogicar4.extranet.gencat.cat/idp/shibboleth">
    
        <IDPSSODescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol urn:oasis:names:tc:SAML:1.1:protocol urn:mace:shibboleth:1.0">
    
            <Extensions>
                <shibmd:Scope regexp="false">localdomain</shibmd:Scope>
    <!--
        Fill in the details for your IdP here 
    
                <mdui:UIInfo>
                    <mdui:DisplayName xml:lang="en">A Name for the IdP at CPD4</mdui:DisplayName>
                    <mdui:Description xml:lang="en">Enter a description of your IdP at CPD4</mdui:Description>
                    <mdui:Logo height="80" width="80">https://autenticaciogicar4.extranet.gencat.cat/Path/To/Logo.png</mdui:Logo>
                </mdui:UIInfo>
    -->
            </Extensions>
    
            <!-- First signing certificate is BackChannel, the Second is FrontChannel -->
            <KeyDescriptor use="signing">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
            <KeyDescriptor use="signing">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
            <KeyDescriptor use="encryption">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
    
            <ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:1.0:bindings:SOAP-binding" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML1/SOAP/ArtifactResolution" index="1"/>
            <ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/ArtifactResolution" index="2"/>
    
            <!--
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/SLO"/>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST-SimpleSign" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST-SimpleSign/SLO"/>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/Redirect/SLO"/>
            <SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST/SLO"/>
            -->
    
            <SingleSignOnService Binding="urn:mace:shibboleth:1.0:profiles:AuthnRequest" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/Shibboleth/SSO"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST-SimpleSign" req-attr:supportsRequestedAttributes="true" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST-SimpleSign/SSO"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" req-attr:supportsRequestedAttributes="true" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/POST/SSO"/>
            <SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" req-attr:supportsRequestedAttributes="true" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/Redirect/SSO"/>
    
        </IDPSSODescriptor>
    
    
        <AttributeAuthorityDescriptor protocolSupportEnumeration="urn:oasis:names:tc:SAML:1.1:protocol">
    
            <Extensions>
                <shibmd:Scope regexp="false">localdomain</shibmd:Scope>
            </Extensions>
    
            <!-- First signing certificate is BackChannel, the Second is FrontChannel -->
            <KeyDescriptor use="signing">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
            <KeyDescriptor use="signing">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
            <KeyDescriptor use="encryption">
                <ds:KeyInfo>
                        <ds:X509Data>
                            <ds:X509Certificate>
    MIIDqDCCApCgAwIBAgICAMMwDQYJKoZIhvcNAQELBQAwaTEhMB8GA1UEChMYR2Vu
    ZXJhbGl0YXQgZGUgQ2F0YWx1bnlhMQ0wCwYDVQQLEwRDVFRJMREwDwYDVQQDEwhH
    SUNBUi1DQTEiMCAGCSqGSIb3DQEJARYTZ2RpLmN0dGlAZ2VuY2F0LmNhdDAgFw0y
    MDExMjQxMDU3MDBaGA8yMDUwMTEyNDEwNTcwMFowMTEvMC0GA1UEAxMmYXV0ZW50
    aWNhY2lvZ2ljYXI0LmV4dHJhbmV0LmdlbmNhdC5jYXQwggEiMA0GCSqGSIb3DQEB
    AQUAA4IBDwAwggEKAoIBAQDV9xRulf9hsMgeit80boWh9ZbP0tTw7HYaFpMvHVDz
    twNEXSi/KDLvPdcndp1i5eMVxD0i6vhm+ztjpvT+el07giY0Etx+GkDf2lIHLbDg
    YowKonftc8i7nDrHN43KpsHZNKI2vFN58uhOnhL+0/rYzp3KslZ/ufJLKnFfJRtC
    8ufEKxYDBVK0Q+H59nNbP9wrlOd9YOz/mppWqBhHUklFwYrXQgLhVc7oBN+yr0K7
    ShYX6LFZnfvkDa/pvIEQy+KkmpIkyEj6hll8sGe+KoceBz7jCwViVoZEigOBow0V
    qJwVQCztjZlFD8cUyjr3AxFiJRQZI8P6gBf6PyqDh/99AgMBAAGjgY8wgYwwDAYD
    VR0TAQH/BAIwADAdBgNVHQ4EFgQUd2RKLqDt1fja9npAbM3R2PyG0xEwCwYDVR0P
    BAQDAgTwMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjARBglghkgBhvhC
    AQEEBAMCBkAwHgYJYIZIAYb4QgENBBEWD3hjYSBjZXJ0aWZpY2F0ZTANBgkqhkiG
    9w0BAQsFAAOCAQEABkggXXX93M6Mi/dY+PM2lFPmD/WbBZx6ExIl48Z64shyXhmf
    DnG74zPmUtlOyrvWgYR65oOODM/nxG4kJIXLPSnoCoJqZUMdGNhvTgc/NDbI8+Aa
    q3hd6PMxN1EdedAHwmXn0YKIc+UcWOfYgC/yVL4zVwgLZCBFqxNIWImCkQrvEW7I
    4+tvXDlFs9nB7TzQ0Q6RmoJWgtbaOamm+frvRgei66/bHKz6FUpVYFhDNlZgHy7G
    oXC2WDvocq/J5qPLSpRHwBhlsJfI23U+a0XZhdbQ8BpIcig6T69wFPcv+ot3pYtF
    pCGYS84SUYqtqwODeVM13zwGd30Vx0b9HbeBgw==
                            </ds:X509Certificate>
                        </ds:X509Data>
                </ds:KeyInfo>
    
            </KeyDescriptor>
    
            <AttributeService Binding="urn:oasis:names:tc:SAML:1.0:bindings:SOAP-binding" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML1/SOAP/AttributeQuery"/>
            <!-- <AttributeService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://autenticaciogicar4.extranet.gencat.cat/idp/profile/SAML2/SOAP/AttributeQuery"/> -->
            <!-- If you uncomment the above you should add urn:oasis:names:tc:SAML:2.0:protocol to the protocolSupportEnumeration above -->
    
        </AttributeAuthorityDescriptor>
    
    </EntityDescriptor>
    