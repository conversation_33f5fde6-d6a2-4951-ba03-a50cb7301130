.footer-contact {
    background-color: #F5F5F5;
}

.footer-contact .NG-container .footer-contact__wrapper {
    padding: 22px 15px 7px;
}

.footer-contact__description-wrapper {
    width: 100%;
}

@media (min-width: 1025px) {
    .footer-contact__description-wrapper {
        width: 33.33333%;
        flex: .3333333;
    }
}

@media (max-width: 1025px) {
    .footer-contact__wrapper {
        width: 93%;
        margin: auto;
        padding: 22px 0 5px;
    }
}

@media (max-width: 480px) {
    .footer-contact__wrapper {
        width: 92%;
    }
}

.footer-contact__description {
    width: 100%;
    padding-right: 40px;
}
@media (max-width: 1025px) {
    .footer-contact__description {
        padding-right: 0px;
    }
}

.footer-contact__title {
    font-family: 'OpenSans_Bold', Arial, Helvetica, sans-serif;
    font-size: 20px;
    color: #333333;
    margin-bottom: 15px;
}

@media (max-width: 767px) {
    .footer-contact__title {
        text-align: center;
    }
}

.footer-contact__text {
    font-family: 'OpenSans_Semibold', Arial, Helvetica, sans-serif;
    font-size: 14px;
    color: #666666;
    line-height: 1.5;
}

@media (max-width: 767px) {
    .footer-contact__text {
        text-align: center;
    }
}

.footer-contact__list-wrapper {
    width: 100%;
}

@media (min-width: 1025px) {
    .footer-contact__list-wrapper {
        text-align: right;
        flex: .666666;
    }
}

.footer-contact__list {
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    padding: 0 30px;
    padding-right: 0;
    justify-content: space-between;
    /*column-gap: 15px;*/
}

.footer-contact__list-item {
    width: 22%;
    padding-right: 0;
    margin-bottom: 8px;
}

.footer-contact__list-img {
    width: 40px;
    height: 40px;
    margin-right: 8px;
}

.footer-contact__list-txt {
    font-family: 'OpenSans_Semibold', Arial, Helvetica, sans-serif;
    font-size: 11px;
    color: #333333;
}

.footer-contact__list-link:hover .footer-contact__list-txt {
    text-decoration: underline;
}

@media (max-width: 1024px) {
    .footer-contact__list {
        padding-left: 0;
        margin-top: 6px;
        justify-content: space-between;
    }
}



@media (max-width: 767px) {
    .footer-contact__list {
        padding: 0;
    }

    .footer-contact__list-item {
        width: 47%;
        padding-right: 0;
        margin-bottom: 8px;
    }
}

@media (max-width: 425px) {
    .footer-contact__list {
        column-gap: 0;
    }

    .footer-contact__list-item {
        width: 48%;
    }
    .footer-contact__list-img{
        margin-right: 6px;
    }
}