import { PtHttpResponse } from "pt-ui-components-mf-lib";
import { IAutoliquidacio } from "../../../models/autoliquidacions-v1.model";

export interface ResponseGetAutoliquidacio extends PtHttpResponse {
	content:  IAutoliquidacio;
}

export interface RequestDeleteSelfAssessment {
	idReceipt: string,
	idSelfAssessment: string
}

export interface ResponseDeleteAutoliquidacio extends PtHttpResponse {
	content: string;
}