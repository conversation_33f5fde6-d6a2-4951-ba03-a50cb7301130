import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { 
	CommonError,
    DataStorageService, 
    PtAuthService, 
    PtConfirmationModalService, 
    PtModalErrorData, 
    PtModalErrorsService,  
    PtSelfAssessmentService
} from 'pt-ui-components-mf-lib';
import { States } from '../models/drafts.model';
import { StatesIcons } from 'src/app/core/models/config.model';
import { ModalWarningMsgComponent } from 'src/app/modules/shared/modal-warning-msg';
import { DeleteModalData } from '../modules/drafts-detail/models/drafts-detail.model';

@Injectable({
	providedIn: 'root'
})
export class DraftsService {

	constructor(
		public confirmationService: PtConfirmationModalService,
		public translateService:TranslateService,
		public modalErrorsService: PtModalErrorsService,
		public dataStorageService: DataStorageService,
		public modalService: NgbModal,
		public selfAssessmentService: PtSelfAssessmentService,
		public authService: PtAuthService
	) { }

	getStateIcon = (
		state: string,
		mode: 'detail' | 'self-assessment'
	): string => {
		switch (state?.toUpperCase()) {
			case States.PRESENTAT:
			case States.CONSOLIDAT:
			case States.PAGAT:
			case States.TRAMITAT:
			case States.ESBORRANY_VALIDAT:
			case States.ESBORRANY_AGRUPAT:
			case States.PAGAMENT_NOTIFICAT:
				return StatesIcons.SUCCESS;
			case States.PRESENTANT:
			case States.CONSOLIDANT:
			case States.PAGANT:
			case States.TRAMITANT:
			case States.NOTIFICANT_PAGAMENT:
				return mode === 'self-assessment'
					? StatesIcons.SPINNER
					: StatesIcons.WAITING_WARNING;
			case States.ESBORRANY:
			case States.PAGAMENT_CANCELLAT:
			case States.ESBORRANY_VALIDANT:
			case States.ESBORRANY_AGRUPANT:
				return StatesIcons.WAITING_WARNING;
			case States.PRESENTACIO_ERROR:
			case States.CONSOLIDACIO_ERROR:
			case States.ERROR:
			case States.PAGAMENT_ERROR:
			case States.TRAMITACIO_ERROR:
			case States.ESBORRANY_ERROR:
			case States.NOTIFICACIO_ERROR:
			case States.ESBORRANY_AGRUPANT_ERROR:
				return StatesIcons.ERROR;
			case States.NO_PRESENTAT:
			case States.PENDENT_PAGAMENT:
			case States.PENDENT_PRESENTACIO:
			case States.DELETE:
			case States.GENERAT:
				return StatesIcons.CLOSE_INFO;
			default:
				return '';
		}
	};

  showErrors = (errorsRows: CommonError[]): void => { 
		if(errorsRows?.length > 0){
			// Open modal
			const errorData:PtModalErrorData = {
				techMode: true,
				errors: errorsRows,
			}
			
			this.modalErrorsService.openModal(errorData);
		}
	}

	deleteModal = (deleteData: DeleteModalData): void => {
		if (!deleteData.isCopy) {
			// Open modal
			const modalRef = this.modalService.open(ModalWarningMsgComponent, {
				size: 'lg',
				windowClass: '',
			});

			// Modal input data
			modalRef.componentInstance.title ='MODULE_TRACKING.MODULE_DRAFTS.DETAIL.DELETE_MODAL.TITLE_ERROR'
			modalRef.componentInstance.subtitle ='MODULE_TRACKING.MODULE_DRAFTS.DETAIL.DELETE_MODAL.SUBTITLE_ERROR'

			return;
		}

		this.confirmationService.openModal({
			severity: 'warning',
			title: 'MODULE_TRACKING.MODULE_DRAFTS.DETAIL.DELETE_MODAL.TITLE',
			subtitle: this.translateService.instant(
				`MODULE_TRACKING.MODULE_DRAFTS.DETAIL.DELETE_MODAL.SUBTITLE`,
				{ id: deleteData.idDraft }
			),
			confirmLabel: 'UI_COMPONENTS.BUTTONS.CONFIRM',
			confirmFn: deleteData.confirmFn.bind(
				this
			),
		});
	};

	openCopyModal = (): void => {
		const modalRef = this.modalService.open(ModalWarningMsgComponent, {
			size: 'lg',
			windowClass: '',
		});

		// Modal input data
		modalRef.componentInstance.title ='MODULE_TRACKING.MODULE_DRAFTS.DETAIL.COPY_MODAL.TITLE_ERROR'
		modalRef.componentInstance.subtitle ='MODULE_TRACKING.MODULE_DRAFTS.DETAIL.COPY_MODAL.SUBTITLE_ERROR'
	};
}