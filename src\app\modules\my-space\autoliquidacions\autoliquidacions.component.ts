import { Component, OnInit } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-autoliquidacions',
	templateUrl: './autoliquidacions.component.html',
	styleUrls: ['./autoliquidacions.component.sass']
})
export class AutoliquidacionsComponent {

	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlElMeuEspaiCss;

	constructor(
		private webComponentService: WebComponentsService
  ) {
		this.webComponentService.setWebComponentStyle(this.wcUrlCss, 'area-privada');
	}

}
