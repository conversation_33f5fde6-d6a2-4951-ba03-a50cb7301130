import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { AutoliquidacionsV2DetailComponent } from './autoliquidacions-v2-detail.component';
import { RouterModule, Routes } from '@angular/router';
import { DetailPageModule } from '../../../../shared/detail-page';

const routes: Routes = [
	{
		path: '', 
    component: AutoliquidacionsV2DetailComponent,
		data: { 
			title: ''
		}
	}
];

@NgModule({
	declarations: [
		AutoliquidacionsV2DetailComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		DetailPageModule,
    RouterModule.forChild(routes)
	]
})
export class AutoliquidacionsV2DetailModule { }
