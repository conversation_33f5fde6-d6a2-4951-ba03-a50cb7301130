<pt-confirmation-message [_data]="confirmationData" ngbAutofocus>
	<pt-card 
	[_title] = "modalInput.title"
	_styleClass="pt-card-secondary">

		<!-- Formulario -->
		<form [formGroup]="notificationForm" class="pt-gray-form-section">
			<div class="row">
				<!-- Initial Date-->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-datepicker 
						_id="inicio"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.INITIAL_DATE"
						[_formGroup]="notificationForm" 
						_formControlName="inicio"
						[_showTime]="true"
						[_yearNavigator]="true"
						[_yearRange]="yearRange"
						(_changeEvent) = "setYearRange($event)">
					</pt-datepicker>
				</div>
	
				<!-- Final Date-->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-datepicker 
						_id="fin"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.FINAL_DATE"
						[_formGroup]="notificationForm" 
						_formControlName="fin"
						[_showTime]="true"
						[_yearNavigator]="true"
						[_yearRange]="yearRange"
						(_changeEvent) = "setYearRange($event)">
					</pt-datepicker>
				</div>
				
				<!-- Context -->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-select 
						_id="contexto" 
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.CONTEXT"
						[_formGroup]="notificationForm" 
						_formControlName="contexto" 
						[_options]="contextOptions">
					</pt-select>
				</div>
				
				<!-- Notification type -->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-select 
						_id="tipo" 
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.NOTIFICATION_TYPE"
						[_formGroup]="notificationForm" 
						_formControlName="tipo" 
						[_options]="notificationTypeOptions">
					</pt-select>
				</div>
	
				<!-- State -->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-select 
						_id="activo" 
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.STATE"
						[_formGroup]="notificationForm" 
						_formControlName="activo" 
						[_options]="stateOptions">
					</pt-select>
				</div>

				<!-- Title Ca -->
				<div class="col-12 col-sm-6 col-md-6 col-lg-6">
					<pt-editor-text
						_id="tituloCa"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_CA"
						_height="100px"
						[_formGroup]="notificationForm" 
						_formControlName="tituloCa">
					</pt-editor-text>
					<!-- <pt-input-text 
						_id="tituloCa"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_CA"
						[_formGroup]="notificationForm" 
						_formControlName="tituloCa">
					</pt-input-text> -->
				</div>

				 <!-- Title Es -->
				 <div class="col-12 col-sm-6 col-md-6 col-lg-6">
					<pt-editor-text 
						_id="tituloEs"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_ES"
						_height="100px"
						[_formGroup]="notificationForm" 
						_formControlName="tituloEs">
					</pt-editor-text>
					<!-- <pt-input-text 
						_id="tituloEs"
						_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.TITLE_ES"
						[_formGroup]="notificationForm" 
						_formControlName="tituloEs">
					</pt-input-text> -->
				</div>

				<ng-container *ngIf="modalInput.mode !== notificationEnum.FILTER">
					<!-- MSG CA-->
					<div class="col-12 col-sm-6 col-md-6 col-lg-6">
						<pt-editor-text
							_id="mensajeCa"
							_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_CA"
							[_formGroup]="notificationForm" 
							_formControlName="mensajeCa">
						</pt-editor-text>
						<!-- <pt-textarea 
							_id="mensajeCa"
							_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_CA"
							[_formGroup]="notificationForm" 
							_formControlName="mensajeCa">
						</pt-textarea> -->
					</div>
		
					<!-- MSG ES -->
					<div class="col-12 col-sm-6 col-md-6 col-lg-6">
						<pt-editor-text 
							_id="mensajeEs"
							_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_ES"
							[_formGroup]="notificationForm" 
							_formControlName="mensajeEs">
						</pt-editor-text>
						<!-- <pt-textarea 
							_id="mensajeEs"
							_label="MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.TABLE.COLUMNS.MSG_ES"
							[_formGroup]="notificationForm" 
							_formControlName="mensajeEs">
						</pt-textarea> -->
					</div>
				</ng-container>
			</div>
		</form>
	
		<!-- Form buttons -->
		<div class="action-buttons text-right">
			<!-- Clear filters -->
			<pt-button 
				*ngIf="modalInput.mode === notificationEnum.FILTER"
				_label="UI_COMPONENTS.BUTTONS.CLEAR" 
				_type="button" 
				_class="p-button-outlined" 
				(_clickFn)="clearFilter()">
			</pt-button>
	
			<!-- Submit -->
			<pt-button 
				[_label]="modalInput.mode === notificationEnum.FILTER ? 'UI_COMPONENTS.BUTTONS.SEARCH' : 'UI_COMPONENTS.BUTTONS.CONTINUE'" 
				_type="submit" 
				_class="p-button-primary"
				[_icon]="modalInput.mode === notificationEnum.FILTER && 'sli2-magnifier'"
				(_clickFn)="submit()" 
				[_disabled]="notificationForm.invalid">
			</pt-button>
		</div>
	
	</pt-card>
</pt-confirmation-message>
