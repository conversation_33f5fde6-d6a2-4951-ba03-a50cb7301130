import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { ModalFilterComponent } from './modal-filter.component';
import { FilterInputsModule } from '../filter-inputs/filter-inputs.module';

@NgModule({
	declarations: [
		ModalFilterComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		FilterInputsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule
	],
  exports: [ModalFilterComponent]
})
export class ModalFilterModule { }
