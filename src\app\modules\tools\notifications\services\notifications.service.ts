import { Injectable } from '@angular/core';
import { NotificationFilters } from '../models/notifications-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class NotificationsService {

	resetFilterData(): NotificationFilters {
		const inicioDate = new Date();
		inicioDate.setFullYear(inicioDate.getFullYear()-2);
		const finDate = new Date();
		finDate.setFullYear(finDate.getFullYear()+2);

		return {
			tituloCa: null,
			tituloEs: null,
			tipo: null,
			contexto: null,
			inicio: inicioDate.getTime(),
			fin: finDate.getTime(),
			activo: null,
		};
	}
}