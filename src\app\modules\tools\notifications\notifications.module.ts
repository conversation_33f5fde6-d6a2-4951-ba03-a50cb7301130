import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { PtUiComponentsMfLibModule, UserRoles } from 'pt-ui-components-mf-lib';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NotificationsComponent } from './notifications.component';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';
import { ModalNotificacioComponent } from './components/modal-notificacio/modal-notificacio.component';

const routes: Routes = [
	{
		path: '', 
		component: NotificationsComponent,
		canActivate: [AuthGuardService],
		data: { 
			title: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.PAGE_TITLE',
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		}
	}
];

@NgModule({
	declarations: [
		NotificationsComponent,
		ModalNotificacioComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule,
		LazyElementsModule,
		RouterModule.forChild(routes)
	]
})
export class NotificationsModule { }
