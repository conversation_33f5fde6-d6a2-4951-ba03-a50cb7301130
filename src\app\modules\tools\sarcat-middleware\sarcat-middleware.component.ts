import { Component } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-sarcat-middleware',
	templateUrl: './sarcat-middleware.component.html',
	styleUrls: ['./sarcat-middleware.component.sass'],
})
export class SarcatMiddlewareComponent {
	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlSarcatMiddlewareCss;

	constructor(private webComponentService: WebComponentsService) {
		this.webComponentService.setWebComponentStyle(
			this.wcUrlCss,
			'sarcat-middleware'
		);
	}
}
