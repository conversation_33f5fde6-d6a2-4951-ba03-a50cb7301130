import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { ManagementsMonitorTableComponent } from './managements-monitor-table.component';
import { RouterModule, Routes } from '@angular/router';
import { ModalFilterModule } from 'src/app/modules/shared/modal-filter';

const routes: Routes = [
	{
		path: '', 
    component: ManagementsMonitorTableComponent,
		data: { 
			title: 'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.PAGE_TITLE', 
		}
	}
];

@NgModule({
	declarations: [
		ManagementsMonitorTableComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		ModalFilterModule,
		TranslateModule.forChild(),
		RouterModule.forChild(routes),
		PtUiComponentsMfLibModule
	]
})
export class ManagementsMonitorTableModule { }
