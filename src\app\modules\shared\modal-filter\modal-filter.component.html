<pt-confirmation-message
	[_data]="confirmationData"
	_wrapperClass="pt-confirmation-visible-overflow"
	ngbAutofocus
>
	<pt-card
		[_title]="modalInput?.filterTitle || 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.FILTER.TITLE'"
		_styleClass="pt-card-secondary"
	>
		<!-- Formulario -->
		<form [formGroup]="filterForm" class="pt-gray-form-section">
			<div *ngFor="let fieldRow of fieldRows" class="row">

				<!-- Fields -->
				<div 
					*ngFor="let field of fieldRow" 
					[ngClass]="field.containerClass || 'col-12 col-sm-6 col-md-6 col-lg-3'"
				>
					<app-filter-inputs
						[formGroup]="filterForm"
						[fieldData]="field" 
						>
					</app-filter-inputs>
				</div>
			</div>
		</form>

		<!-- Form buttons -->
		<div class="action-buttons text-right">
			<!-- Clear filters -->
			<pt-button
				_label="UI_COMPONENTS.BUTTONS.CLEAR"
				_type="button"
				_class="p-button-outlined"
				(_clickFn)="clearFilter()"
			>
			</pt-button>

			<!-- Submit -->
			<pt-button
				_label="UI_COMPONENTS.BUTTONS.SEARCH"
				_type="submit"
				_icon="sli2-magnifier"
				_class="p-button-primary"
				(_clickFn)="search()"
				[_disabled]="filterForm.invalid"
			>
			</pt-button>
		</div>
	</pt-card>
</pt-confirmation-message>