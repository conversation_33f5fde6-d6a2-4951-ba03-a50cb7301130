import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import {
	AutoliquidacioResponse
} from '../models/autoliquidacions-v1-endpoints.model';
import { AutoliquidacioRequest } from '../models/autoliquidacions-v1.model';

@Injectable({
	providedIn: 'root'
})
export class AutoliquidacionsV1EndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search autoliquidacions by criteria
	getSelfAssessmentList(criteria?: AutoliquidacioRequest | object): Observable<AutoliquidacioResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: "/tracking/autoliquidacio/llistat",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}

}