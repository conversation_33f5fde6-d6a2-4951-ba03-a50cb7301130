import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { RequestDeleteSelfAssessment, ResponseDeleteAutoliquidacio, ResponseGetAutoliquidacio } from '../models/detail-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class DetailEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: DELETE > Delete self assessment
	deleteAssessment(body: RequestDeleteSelfAssessment): Observable<ResponseDeleteAutoliquidacio> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/autoliquidacio/${body.idSelfAssessment}`,
			method: 'delete'
		}
		return this.httpService.delete(requestOptions);
	}

	// Request: GET > Get self assessment by id
	getSelfAssessment(idSelfAssessment: string): Observable<ResponseGetAutoliquidacio> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/autoliquidacio/${idSelfAssessment}`,
			method: 'get'
		}
		return this.httpService.get(requestOptions);
	}

}