import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { AppRoutes } from './core/models/config.model';
import { AuthGuardService } from './core/services/auth-guard.service';

const routes: Routes = [
	{
		path: AppRoutes.OLD_SELFASSESSMENT_TABLE,
		redirectTo: AppRoutes.TAXES,
		pathMatch: 'full',
	},
	{
		path: AppRoutes.TAXES,
		loadChildren: () =>
			import(`./modules/taxes/taxes-routing.module`).then(
				(module) => module.TaxesRoutingModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.MANAGEMENTS,
		loadChildren: () =>
			import(`./modules/managements/managements-routing.module`).then(
				(module) => module.ManagementsRoutingModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.LIQUIDACIONS,
		loadChildren: () =>
			import(
				`./modules/managements/pagament-liquidacions/pagament-liquidacions.module`
			).then((module) => module.PagamentLiquidacionsModule),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.PAGAMENTS,
		loadChildren: () =>
			import(`./modules/managements/payments/payments.module`).then(
				(module) => module.PaymentsModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.RESOLUCIONS,
		loadChildren: () =>
			import(
				`./modules/managements/tearc-monitor/tearc-monitor.module`
			).then((module) => module.TearcMonitorModule),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.USERS,
		loadChildren: () =>
			import(`./modules/users/users-routing.module`).then(
				(module) => module.UsersRoutingModule
			),
	},
	{
		path: AppRoutes.USERS_TABLE,
		loadChildren: () =>
			import(`./modules/users/usuaris/usuaris.module`).then(
				(module) => module.UsuarisModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN],
		},
	},
	{
		path: AppRoutes.MY_SPACE_CONF,
		loadChildren: () =>
			import(`./modules/my-space/my-space-routing.module`).then(
				(module) => module.MySpaceRoutingModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: AppRoutes.PRIVATE_AREA_CONF,
		redirectTo: AppRoutes.MY_SPACE_CONF,
		pathMatch: 'prefix',
	},
	{
		path: AppRoutes.TOOLS,
		loadChildren: () =>
			import(`./modules/tools/tools-routing.module`).then(
				(module) => module.ToolsRoutingModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN, UserRoles.INSPECTOR],
		},
	},
	{
		path: AppRoutes.SARCAT_MIDDLEWARE,
		loadChildren: () =>
			import(`./modules/tools/sarcat-middleware/sarcat-middleware.module`).then(
				(module) => module.SarcatMiddlewareModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
		},
	},
	{
		path: '**',
		loadChildren: () =>
			import(`./modules/dispatcher/dispatcher.module`).then(
				(module) => module.TrackingDispatcherModule
			),
		canActivate: [AuthGuardService],
		data: {
			allowRoles: [
				UserRoles.ADMIN,
				UserRoles.TECHNICIAN,
				UserRoles.INSPECTOR,
			],
			isDispatcher: true,
		},
	},
];

@NgModule({
	imports: [RouterModule.forRoot(routes)],
	exports: [RouterModule],
})
export class AppRoutingModule {}
