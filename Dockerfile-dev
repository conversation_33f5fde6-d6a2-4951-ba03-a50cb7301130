FROM docker-registry.ctti.extranet.gencat.cat/gencatcloud/gicar-nginx-openshift:1.0.0

USER root

# Copy nginx config files
COPY config/default-dev.conf /etc/nginx/conf.d/default.conf

# Copy idp-metadata.xml config file
COPY config/idp-metadata-dev.xml /gicar/idp-metadata.xml

# Copy certificates
COPY certificates/* /etc/shibboleth/

# Copy static files
RUN rm -rf /etc/nginx/html
RUN ln -s /data/html /etc/nginx/html

# Changing file permissions
RUN chmod g+w /etc/nginx/conf.d/default.conf

USER 1001
