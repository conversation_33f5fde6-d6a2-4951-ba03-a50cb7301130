import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-expediente-list',
	template: `
	<aeat-conector-expediente-list *axLazyElement (detailAction)="navigateToDetail($event)"></aeat-conector-expediente-list>
	`,
})
export class ExpedienteListComponent implements OnInit {
	constructor(private webComponentService: WebComponentsService,
		private router: Router
	) {
		//Empty
	}

	ngOnInit(): void {
		this.webComponentService.setWebComponentStyle(environment.wcUrlAEATConectorCss, 'aeat-conector');
	}

	navigateToDetail(idEni: CustomEvent): void {
		this.router.navigate(['/secured/eines/expedients', 'detall', idEni.detail.idEni, idEni.detail.idPeticion]);
	}
}
