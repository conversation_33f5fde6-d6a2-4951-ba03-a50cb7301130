import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
	PtComponentEventOutput, 
	PtMessageI, 
	PtSelectCardModel, 
	PtSelectModel, 
	PtValidations 
} from 'pt-ui-components-mf-lib';
import { AvisoEnum, NotificationModalInput, NotificationModalModeEnum, NotificationsRow } from '../../models/notifications.model';
import { NotificationsService } from '../../services/notifications.service';
import { NewNotificationRequest, NotificationResponseContent } from '../../models/notifications-endpoint.model';



@Component({
	selector: 'app-modal-notificacio',
	templateUrl: './modal-notificacio.component.html',
	styleUrls: ['./modal-notificacio.component.sass']
})
export class ModalNotificacioComponent implements OnInit {

	@Input() modalInput: NotificationModalInput
	
	@Output() modalOutput: EventEmitter<NotificationsRow> = new EventEmitter<NotificationsRow>();

	// Options
	notificationTypeOptions: PtSelectCardModel[] = [
		{ id: 'error', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.ERROR" },
		{ id: 'success', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.SUCCESS" },
		{ id: 'warning', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.WARNING" },
		{ id: 'info', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.INFO" }
	]

	contextOptions: PtSelectCardModel[] = [];
	stateOptions: PtSelectModel[] = [
		{ id: 'true', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.STATE_OPTIONS.ACTIVE" },
		{ id: 'false', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.STATE_OPTIONS.INACTIVE"},
	]
	
	// Filter
	notificationForm:FormGroup;
	
	// Other
	confirmationData: PtMessageI;
	yearRange:string;
	notificationEnum = NotificationModalModeEnum;

	constructor(
		private fb: FormBuilder,
		public activeModal: NgbActiveModal,
		private translateService: TranslateService,
		private notificationsService: NotificationsService
	) { }

	ngOnInit(): void {
		this.setUpModalData();

		this.setContextOptions();
		
		this.setForm();
		
		this.setYearRange();
	}

	private setUpModalData(): void {
		this.confirmationData = {
			severity: null,
			title: null,
			closableFn: this.closeModal.bind(this)
		};

		if(this.modalInput.mode === NotificationModalModeEnum.FILTER){
			this.notificationTypeOptions.push({ id: 'cad', label: "MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_OPTIONS.CAD" })
		}
	}
	
	private setContextOptions = (): void => {
		const statesList = Object.keys(AvisoEnum);
		statesList.forEach(state => this.contextOptions.push(
			{ id: state, label: this.translateService.instant(`MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.CONTEXT_OPTIONS.${state}`) }
		));
	}

	private setForm = (): void => {
		let notificationInput = this.modalInput?.notification as NotificationsRow;
		const requiredValidation = this.modalInput.mode !== NotificationModalModeEnum.FILTER && Validators.required;

		this.notificationForm = this.fb.group({
			tipo: [notificationInput?.tipo, requiredValidation],
			tituloCa: [notificationInput?.tituloCa, requiredValidation],
			tituloEs: [notificationInput?.tituloEs, requiredValidation],
			mensajeCa: [notificationInput?.mensajeCa, requiredValidation],
			mensajeEs: [notificationInput?.mensajeEs, requiredValidation],
			contexto: [notificationInput?.contexto, requiredValidation],
			inicio: [notificationInput?.inicio && new Date(notificationInput?.inicio), Validators.required],
			fin: [notificationInput?.fin && new Date(notificationInput?.fin), Validators.required],
			activo: [notificationInput?.activo?.toString(), requiredValidation],
		},
		{
			validators: [
				PtValidations.equalsOrLessThan('fin', 'inicio', 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_MODAL.VALIDATIONS.LESS_EQUAL'),
				PtValidations.equalsOrGreaterThan('inicio', 'fin', 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_MODAL.VALIDATIONS.GREATER_EQUAL'),
			]
		});
	}
	
	setYearRange(event?: PtComponentEventOutput) {
		const rangeDate = new Date();
		this.yearRange = `${rangeDate.getFullYear()-11}:${rangeDate.getFullYear()+9}`;
	}

	clearFilter = () =>{
		const resetValue = {
			...this.notificationsService.resetFilterData(),
			inicio: new Date(this.notificationsService.resetFilterData().inicio), 
			fin: new Date(this.notificationsService.resetFilterData().fin), 
		}
		this.notificationForm.reset(resetValue);
	} 

	submit(){

		let output: NotificationResponseContent | NewNotificationRequest = {
			...this.notificationForm.value,
			inicio: new Date(this.notificationForm.value.inicio)?.getTime(),
			fin: new Date(this.notificationForm.value.fin)?.getTime(),
		};

		if(this.modalInput.mode !== NotificationModalModeEnum.FILTER){
			output = new NewNotificationRequest(output)
		}

		this.modalOutput.emit(output);
		this.activeModal.close();
	}

	closeModal = () => this.activeModal.close();
}
