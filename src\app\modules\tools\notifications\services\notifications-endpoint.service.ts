import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { 
	DeleteNotificationResponse, 
	NotificationListResponse, 
	NotificationResponse, 
	NotificationRequest,
	NotificationResponseContent
} from '../models/notifications-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class NotificationsEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search notifications by filter
	getNotificationsList(request: NotificationRequest): Observable<NotificationListResponse> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlAreaPrivada,
			url: `/avis/llistat`,
			method: 'post',
			body: request
		};
		return this.httpService.post(httpRequest);
	}

	// Request: POST > Add new notification
	addNewNotification(request: NotificationResponseContent, id?:string): Observable<NotificationResponse> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlAreaPrivada,
			url: `/avis`,
			method: 'post',
			body: request
		};
		return this.httpService.post(httpRequest);
	}

	// Request: PUT > Edit notification
	editNotification(request: NotificationResponseContent, id:string): Observable<NotificationResponse> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlAreaPrivada,
			url: `/avis/${id}`,
			method: 'put',
			body: request
		};
		return this.httpService.put(httpRequest);
	}

	// Request: DELETE > Delete notification
	deleteNotification(id:string): Observable<DeleteNotificationResponse> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlAreaPrivada,
			url: `/avis/${id}`,
			method: 'delete'
		};
		return this.httpService.delete(httpRequest);
	}
}