import { Injectable } from '@angular/core';
import {
	CommonErrorTypeEnum,
    PtModalErrorData,
    PtModalErrorsService} from 'pt-ui-components-mf-lib';
import { ErrorDTO, Metadades, States } from './models/biometric-signature.model';
import { StatesIcons } from 'src/app/core/models/config.model';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { MetadadesModalComponent } from './components';

@Injectable({
	providedIn: 'root'
})
export class BiometricSignatureService {

	constructor(
		private modalErrorsService: PtModalErrorsService,
		private modalService: NgbModal,
	) { }

	getStateIcon = (
		state: string
	): string => {
		switch (state?.toUpperCase()) {
			case States.NOTIFICAT:
			case States.SIGNAT:
				return StatesIcons.SUCCESS;
			case States.PENDENT:
			case States.ENVIAT:
				return StatesIcons.WAITING_WARNING;
			case States.CADUCAT:
			case States.REBUTJAT:
				return StatesIcons.EXCLAMATION_WARNING;
			case States.GENERAT:
				return StatesIcons.CLOSE_INFO;
			case States.ERROR_HISTORIFICACIO:
			case States.ERROR_NOTIFICACIO:
			case States.ERROR_PETICIO:

			case States.CANCELAT:
				return StatesIcons.ERROR;
			default:
				return '';
		}
	};

	showErrors = (errorsRows: ErrorDTO[]): void => {
		// Open modal
		const errorData: PtModalErrorData = {
			techMode: false,
			errors: errorsRows.map(error => {
				return {
					code: '',
					date: error.data_creacio.toString(),
					description: error.error_missatge,
					type:	CommonErrorTypeEnum.REALIZAR_ENCUESTA_NOK,
					technicalCode: '',
					technicalDescription: '',
					trackingId: '',
					stackTrace: '',
				}
			}),
		}

		this.modalErrorsService.openModal(errorData);
	}

	showMetadades = (metadades: Metadades[]): void => {
		// Open modal
		const modalInstance: NgbModalRef = this.modalService.open(
			MetadadesModalComponent,
			{ size: 'xl', windowClass: '', backdrop: 'static' }
		);
		modalInstance.componentInstance.metadades = metadades;
	}
}
