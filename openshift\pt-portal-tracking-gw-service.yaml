apiVersion: v1
kind: Service
metadata:
  name: pt-portal-tracking-gw
  labels:
    name: pt-portal-tracking-gw
    app: pt-app
    type: gateway 
spec:
  ports:
  - name: 1080-tcp
    port: 1080
    protocol: TCP
    targetPort: 1080
  - name: 1043-tcp
    port: 1043
    protocol: TCP
    targetPort: 1043
  selector:
    name: pt-portal-tracking-gw
    app: pt-app
  type: ClusterIP
  sessionAffinity: ClientIP
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 300
