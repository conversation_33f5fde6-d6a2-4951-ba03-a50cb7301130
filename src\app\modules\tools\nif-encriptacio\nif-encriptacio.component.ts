import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Card2Mode, PtValidations } from 'pt-ui-components-mf-lib';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { NifEncriptacioEndpointsService } from './services/nif-encriptacio-endpoint.service';
import { EncryptNifResponse } from './models/nif-encriptacio-endpoint.model';

@Component({
	selector: 'app-nif-encriptacio',
	templateUrl: './nif-encriptacio.component.html',
	styleUrls: ['./nif-encriptacio.component.sass']
})
export class NifEncriptacioComponent implements OnInit,OnDestroy {

	private unsubscribe: Subject<void> = new Subject();

	Card2Mode = Card2Mode; 
	componentForm: FormGroup;
	// optionsList: PtRadioButtonModel[] = [
	// 	{ id: '1', label: 'Encriptar' },
	// 	{ id: '2', label: 'Desencriptar'}
	// ];

	constructor(
		private fb: FormBuilder,
		private endPointService: NifEncriptacioEndpointsService
	) { }

	ngOnInit(): void {
		this.componentForm = this.setForm();
	}

	ngOnDestroy(): void {
		this.unsubscribe.next();
		this.unsubscribe.complete();
	}

	setForm = (): FormGroup => {
		return this.fb.group({
			encryptionType: ['1'],
			nif: [null,PtValidations.dniNieCif],
			encryptedNif: [{value:null,disabled: true}],
		});
	}

	onRadioButtonChange () {
		this.clearForm();
	}

	encryptNif() {
		const nif = this.componentForm.get('nif').value
		
		this.endPointService.encryptNif(nif).pipe(takeUntil(this.unsubscribe)).subscribe(
			(response:EncryptNifResponse) => {
				if (response?.content) this.componentForm.get('encryptedNif').setValue(response.content);
			} 
		);
	}
	
	// decryptNif() {
	// 	const nif = this.componentForm.get('encryptedNif').value
	// 	const response = this.endPointService.decryptNif(nif);
	// 	this.componentForm.get('nif').setValue(response);
	// }

	clearForm(){
		this.componentForm.get('nif').reset()
		this.componentForm.get('encryptedNif').reset()
	}

}
