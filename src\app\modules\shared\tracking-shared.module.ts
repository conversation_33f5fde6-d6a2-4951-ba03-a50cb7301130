import { NgModule } from '@angular/core';
import { FilterInputsModule } from './filter-inputs/filter-inputs.module';
import { ModalFilterModule } from './modal-filter/modal-filter.module';
import { DetailCardModule } from './detail-card';
import { DetailStateTableModule } from './detail-state-table';
import { DetailTabsModule } from './detail-tabs';
import { ModalWarningMsgModule } from './modal-warning-msg';

const componentModules = [ 
  FilterInputsModule,
  ModalFilterModule,
  DetailCardModule,
  DetailStateTableModule,
  DetailTabsModule,
  ModalWarningMsgModule
]

@NgModule({
	imports: [
    ...componentModules
	],
  exports: [...componentModules]
})
export class TrackingSharedModule { }
