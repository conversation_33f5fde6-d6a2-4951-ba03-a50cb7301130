import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { XlsxService } from './xlsx.service';
import { IAutoliquidacio, States } from '../../modules/taxes/autoliquidacions-v1/models/autoliquidacions-v1.model';
import { ExportColumnType, ExportColumnsData } from '../models/export-table.model';

@Injectable({
	providedIn: 'root'
})
export class TableExportService {

	constructor(
		private translateService: TranslateService,
		private xlsxService: XlsxService
	) {}

	public export2Excel = async (rows: any[], headers:ExportColumnsData[], fileName: string, sheetName: string): Promise<void> => {
		const data: Array<Object> = [];

		// Salimos si no hay datos
		if (!rows || !rows.length) return;

		// Añadimos una linea por cada autoliquidación
		rows.forEach(rowData => {
			let row = {};
			headers.forEach(header => {
				// Valor vacío si nulo				
				let cell = rowData?.[header.id] ?? '';
				
				// Formateamos valores concretos
				switch (header.columnType) {
					// Texto
					case ExportColumnType.text:
						cell = header.attr ? this.getValue(cell,header.attr) : cell;
						break;
					// Texto
					case ExportColumnType.boolean:
						cell = Boolean(cell);
						break;
					// Array
					case ExportColumnType.array:
						if (cell?.length > 0) {
							const cellCopy = cell.slice(header.arrayPos,1)[0];
							cell = this.getValue(cellCopy, header.attr);
						}
						break;
					// A revisar
					case ExportColumnType.toReview:
						cell = this.toReview(rowData);
						break;
					// Fechas
					case ExportColumnType.date:
						cell = this.formatDate(cell);
						break;
					// Calculamos estado presentacion
					case ExportColumnType.presentationState:
						cell = this.presentationState(rowData);
						cell = this.translateService.instant(`${header.translation}.${cell}`);
						break;
					// Traducimos estados
					case ExportColumnType.translation:
						cell = cell ? this.translateService.instant(`${header.translation}.${cell}`) : "";
						break;
					// Traducimos estados
					case ExportColumnType.textToBoolean:
						cell = cell ? "TRUE" : "FALSE";
						cell = cell ? this.translateService.instant(`${header.translation}.${cell}`) : "";
						break;
					// Cambios de estado
					case ExportColumnType.stateChanges:
						cell = this.stateChanges(rowData);
						break;
					// Errores
					case ExportColumnType.errors:
						if (cell?.length > 0) {
							const cellCopy = cell.slice(header.arrayPos || -1, 1)[0];
							const errorCode = cellCopy?.technicalCode || cellCopy?.code;
							const errorDesc = cellCopy?.technicalDescription || cellCopy?.description;
							const errorDescKey = `UI_COMPONENTS.MODAL_ERRORS.MAPPED_ERRORS.${errorCode}`;
							const errorDescTrans = this.translateService.instant(errorDescKey);
							cell = (errorDescKey == errorDescTrans ? errorDesc : errorDescTrans);
						} 

						break;

					// Formato por defecto
					default:
						break;
				}

				// Pasamos a String y seteamos
				row[header.columnTitle] = cell?.toString() ?? "";
			});

			// Añadimos la row al listado
			data.push(row);
		});
	
		this.xlsxService.saveFile(data,fileName,sheetName);
	}

	private formatDate = (d: string): string => {
		let result = '';
		if (d) {
			let date = new Date(d);
			result = date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear();
		}
		return result;
	}

	private getValue = (cell: any, attr: string | number): string => cell ? cell[attr] : '';
	
	// A revisar por cosas raras
	private toReview = (aut: IAutoliquidacio): string => {
		let toR = false;
		// Estado de presentacion Presentado o Pagado y presentado
		const estPresFinal: Array<States> = [States.PROCESSED, States.PRESENTED]
		// Estado de autoliquidcion Consolidado o Consolidado con error
		const estFinal: Array<States> = [States.TRAMITATED, States.TRAMITATED_ERROR]
		// Cambios de estado
		let ce = aut.stateChanges ? aut.stateChanges.map(c => c.current).join(",") : '';
		let cambEst: Array<string> = [
			// No presentada
			[''].join(','),
			// Solo presentar
			[States.PRESENTING].join(','),
			[States.PRESENTING, States.PRESENTED].join(','),
			[States.PRESENTING, States.PRESENTED, States.TRAMITATING].join(','),
			[States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
			// Solo presentar con 1 error de pago
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
			// Solo presentar con 2 error de pago
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PRESENTING, States.PRESENTED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
			// Presentar y pagar
			[States.PAYING].join(','),
			[States.PAYING, States.PAID].join(','),
			[States.PAYING, States.PAID, States.PRESENTING].join(','),
			[States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED].join(','),
			[States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING].join(','),
			[States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
			// Presentar y pagar con 1 error
			[States.PAYING, States.PAYING_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
			// Presentar y pagar con 2 error
			[States.PAYING, States.PAYING_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR].join(','),
			[States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAYING_ERROR, States.PAYING, States.PAID, States.PRESENTING, States.PROCESSED, States.TRAMITATING, States.TRAMITATED_ERROR, States.TRAMITATED].join(','),
		];


		// Si tiene combinaciones de estados finales y de presentacion raros
		//toR = estPresFinal.includes(aut.presentationState) && !estFinal.includes(aut.state);

		// Si no tiene un ciclo de vida de estados normal
		toR = toR || ( aut.presentationState !== States.NO_PRESENTED && !cambEst.includes(ce));

		// if (aut.stateChanges && aut.stateChanges.length > 0) {
		// 	// Estados por los que ha pasado
		// 	r = aut.stateChanges.map(c => this.translateService.instant(`MODULE_TRACKING.STATES.${c.current}`)).join(",");
		// }

		return toR ? 'SI' : 'NO';
	}
	
	// Calculamos string de cambios de estado
	private stateChanges = (aut: IAutoliquidacio): string => {
		let r = "";
		if (aut.stateChanges && aut.stateChanges.length > 0) {
			// Estados por los que ha pasado
			r = aut.stateChanges.map((c,i) => {
				if (i ===0) {
					return `${this.translateService.instant(`MODULE_TRACKING.STATES.${c.before}`)},${this.translateService.instant(`MODULE_TRACKING.STATES.${c.current}`)}`
				} else {
					return this.translateService.instant(`MODULE_TRACKING.STATES.${c.current}`)
				}
			}).join(",");
		}
		return r;
	}

	// Calculamos estado de la presentación
	public presentationState = (aut: IAutoliquidacio): States => {
		// Por defecto no presentada
		let state =  States.NO_PRESENTED;

		if (aut.presentationState) {
			state = States[aut.presentationState];
		}else if (aut.stateChanges && aut.stateChanges.length > 0) {
			// Estados por los que ha pasado
			let changes = aut.stateChanges.map(c => c.current);
			// Si pagada y presentada
			if (changes.includes(States.PROCESSED)) {
				state = States.PROCESSED;
			// Si solo presentada
			} else if (changes.includes(States.PRESENTED)) {
				state = States.PRESENTED;
			// Si solo pagada
			} else if (changes.includes(States.PAID)) {
				state = States.PAID;
			} 
		}

		return state;
	}

}