import { Component, OnInit } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core'
import { DataStorageService, IConfirmationModal, PtConfirmationModalService, PtTableColumn, PtTableColumnTemplate, PtTableSelection } from 'pt-ui-components-mf-lib';
import { LazyLoadEvent } from 'primeng/api';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ModalMaintenanceConfComponent } from './components/modal-maintenance-conf/modal-maintenance-conf.component';
import { ConfFilters, ConfModalInput, ConfModalModeEnum, ConfRow, ConfSateEnum } from './models/maintenance-conf.model';
import { MaintenanceConfEndpointsService } from './services/maintenance-conf-endpoint.service';
import { MaintenanceConfService } from './services/maintenance-conf.service';
import { ConfListResponse, ConfRequest, ConfResponseContent, CreateConfRequest } from './models/maintenance-conf-endpoint.model';
import { StatesIcons } from 'src/app/core/models/config.model';

const MAINTENANCE_FILTER_STORAGE_NAME = 'maintenance-config-filter';

@Component({
	selector: 'app-maintenance-conf',
	templateUrl: './maintenance-conf.component.html',
	styleUrls: ['./maintenance-conf.component.sass']
})
export class MaintenanceConfComponent implements OnInit {

	// Table
	maintenanceColumns: PtTableColumn[] = [];
	maintenanceRows: ConfRow[] = [];
	totalMaintenanceRows: number = 0;
	selectedRows: ConfRow[] = [];

	// Other
	options: LazyLoadEvent
	filterValue: ConfFilters;
	PtTableSelection = PtTableSelection;
	statesIcons = StatesIcons;

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		public confirmationService: PtConfirmationModalService,
		private modalService: NgbModal,
		private translateService:TranslateService,
		private endPointService: MaintenanceConfEndpointsService,
		private dataStorageService: DataStorageService,
		private gestionsService: MaintenanceConfService,
		private confirmationModal: PtConfirmationModalService
	) {}

	ngOnInit(): void {
		this.setTableColumns();	
		
		this.setFilterData();

		this.search();
	}

	ngOnDestroy(): void {
		this.dataStorageService.deleteItem(MAINTENANCE_FILTER_STORAGE_NAME);
		
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	private setTableColumns = (): void => {

		this.maintenanceColumns = [
			{ 
				id: "aplicacio", label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.application", 
				template: PtTableColumnTemplate.translate,isResizable: false, isSortable: true, width: "25%",
				options: {
					translation: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.CONTEXT_OPTIONS'
				}
			},
			{ 
				id: "stateRow", label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.stateRow", 
				template: PtTableColumnTemplate.icon, isResizable: false, isSortable: false, width: "25%"
			},
			{ 
				id: "mantenimentInici", label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.maintenanceStart", 
				template: PtTableColumnTemplate.date, isResizable: false, isSortable: false, width: "20%",
				options: {
					dateFormat: 'dd/MM/yyyy HH:mm'
				}
			},
			{ 
				id: "mantenimentFinal", label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.maintenanceEnd", 
				template: PtTableColumnTemplate.date, isResizable: false, isSortable: false, width: "20%",
				options: {
					dateFormat: 'dd/MM/yyyy HH:mm'
				}
			},
			{ 
				id: "nouCapcaleraRow", label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.nouCapcalera", 
				template: PtTableColumnTemplate.icon, isResizable: false, isSortable: false, width: "20%",
			},
			{ 
				id: 'tableActions', label: null, isResizable: false, isSortable: false, 
				template: PtTableColumnTemplate.actions, width: '10%',  
				options: {
					dropdownOptions: [
						{
							id: 'updateConf',
							label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.BUTTONS.UPDATE",
							icon: "sli2-pencil",
							command: this.updateConf.bind(this)
						},
						// {
						// 	id: 'deleteConf',
						// 	label: "MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.BUTTONS.DELETE",
						// 	icon: "sli2-trash",
						// 	command: this.deleteConf.bind(this)
						// }
					]
				}
			}
		];
	}

	private setFilterData () {
		const filterData = this.dataStorageService.getItem(MAINTENANCE_FILTER_STORAGE_NAME) as ConfFilters;

		this.filterValue = filterData || this.gestionsService.resetMaintenanceFilterData();
	}

	search = async (event?: LazyLoadEvent): Promise<void> => {
		// Reset previous data
		this.maintenanceRows = [];
		this.totalMaintenanceRows = 0;
		
		// if (!event.sortField){
		// 	event.sortField ="fin";
		// 	event.sortOrder = -1;
		// }

		// // Request
		// this.options = event;
		const request: ConfRequest = new ConfRequest(this.filterValue, event);
		
		this.endPointService.getConfList(request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: ConfListResponse) => {
				this.maintenanceRows = response?.content?.results?.map(row => this._setConfRow(row));

				this.totalMaintenanceRows = response?.content?.total || 0;
			}
		)
	}

	private _setConfRow(confData: ConfResponseContent): ConfRow {
		const stateEnum = this._getStateEnum(confData);
		const translation = stateEnum ? this.translateService.instant(`MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.STATES.${stateEnum}`) : '';
		let nouCapcaleraIcon: string = "";

		if (confData.nouCapcalera === true) {
			nouCapcaleraIcon = this.statesIcons.SUCCESS;
		}
		else {
			nouCapcaleraIcon = this.statesIcons.ERROR;
		}

		return {
			...confData,
			stateRow: {
				icon: this.gestionsService.getStateIcon(stateEnum),
    		label: translation
			},
			nouCapcaleraRow: {
				icon: nouCapcaleraIcon,
				label: '  '
			}
		}
	}

	private _getStateEnum (confData: ConfResponseContent): ConfSateEnum {
		const currentDate = new Date();
		const startMantenimentDate = new Date(confData.mantenimentInici);
		
		if (confData.isManteniment) return ConfSateEnum.IN_MAINTENANCE;

		return startMantenimentDate && currentDate < startMantenimentDate ? 
			ConfSateEnum.SCHEDULED_MAINTENANCE : ConfSateEnum.ACTIVE;
	}

	confFilter() {

		this._openConfModal(ConfModalModeEnum.FILTER);
	
	}

	createConf () {

		this._openConfModal(ConfModalModeEnum.CREATE_CONF);

	}
	
	updateConf (event?: any, colId?: string, row?: ConfRow) {

		this._openConfModal(ConfModalModeEnum.UPDATE_CONF, row);

	}

	deleteConf (event?: any, colId?: string, row?: ConfRow) {
		const confirmationData: IConfirmationModal = {
			severity: 'warning',
			title: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.BUTTONS.DELETE',
			subtitle: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.MODAL.DELETE_SUBTITLE',
			confirmFn: () => {
				this._callEndpoint(ConfModalModeEnum.DELETE_CONF,row.id);
			},
		}

		this.confirmationModal.openModal(confirmationData);
	}

	private _openConfModal(mode: ConfModalModeEnum, confRow?: ConfRow)  {

		const modalRef = this.modalService.open(
			ModalMaintenanceConfComponent,
			{ size: 'xl', windowClass: '' }
		);
		
		// Modal input data
		const modalInput: ConfModalInput = {
			title: `MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.MODAL.MODES.${mode}`,
			mode: mode,
			maintenance: mode === ConfModalModeEnum.FILTER ? this.filterValue : confRow
		}

		modalRef.componentInstance.modalInput = modalInput;
		
		// Modal output data
		modalRef.componentInstance.modalOutput.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(modalResponse: ConfResponseContent | ConfFilters) => {
				this._handleModalOutput(modalResponse,mode,confRow);
			}
		)
	}

	private _handleModalOutput(
		modalResponse: ConfResponseContent | ConfFilters,
		mode: ConfModalModeEnum,
		confRow?: ConfRow
	) {
		if (mode === ConfModalModeEnum.FILTER) {
			this.filterValue = modalResponse as ConfFilters;
			this.dataStorageService.setItem(MAINTENANCE_FILTER_STORAGE_NAME, modalResponse);

			this.search(this.options);
		} else {
			this[`_${mode.toLowerCase()}ConfEndpoint`](modalResponse as ConfResponseContent,confRow);
		}
	}

	private _updateConfEndpoint (
		modalResponse: ConfResponseContent, 
		confRow: ConfRow
	) {
		const rows = confRow ? [confRow] : this.selectedRows;
		const request = rows.map(row => {
			return {
				id: row.id,
				aplicacio: row.aplicacio,
				mantenimentIniciMils: modalResponse.mantenimentInici,
				mantenimentFinalMils: modalResponse.mantenimentFinal
			}
		})

		this._callEndpoint(ConfModalModeEnum.UPDATE_CONF,request);
	}

	private _createConfEndpoint (
		modalResponse: ConfResponseContent
	) {
		const request: CreateConfRequest =  {
			aplicacio: modalResponse.aplicacio,
			mantenimentIniciMils: modalResponse.mantenimentInici,
			mantenimentFinalMils: modalResponse.mantenimentFinal
		}
		
		this._callEndpoint(ConfModalModeEnum.CREATE_CONF,request);
	}

	private _callEndpoint (endpoint: ConfModalModeEnum, request: any) {
		this.endPointService[`${endpoint.toLowerCase()}Conf`](request).pipe(
			takeUntil(this._unsubscribe)
		).subscribe((response) => {
			if (response) {
				this.search();
			}
		});
	}
}
