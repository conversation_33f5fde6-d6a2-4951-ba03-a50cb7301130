/*
 * Bootstrap v3.1.0 (http://getbootstrap.com)
 * Copyright 2011-2014 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/master/LICENSE)
 */
if("undefined"==typeof jQuery){throw new Error("Bootstrap requires jQuery");}+function(a){function b(){var a=document.createElement("bootstrap"),b={WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd otransitionend",transition:"transitionend"};for(var c in b){if(void 0!==a.style[c]){return{end:b[c]};}}return !1;}a.fn.emulateTransitionEnd=function(b){var c=!1,d=this;a(this).one(a.support.transition.end,function(){c=!0;});var e=function(){c||a(d).trigger(a.support.transition.end);};return setTimeout(e,b),this;},a(function(){a.support.transition=b();});}(jQuery),+function(a){var b='[data-dismiss="alert"]',c=function(c){a(c).on("click",b,this.close);};c.prototype.close=function(b){function c(){f.trigger("closed.bs.alert").remove();}var d=a(this),e=d.attr("data-target");e||(e=d.attr("href"),e=e&&e.replace(/.*(?=#[^\s]*$)/,""));var f=a(e);b&&b.preventDefault(),f.length||(f=d.hasClass("alert")?d:d.parent()),f.trigger(b=a.Event("close.bs.alert")),b.isDefaultPrevented()||(f.removeClass("in"),a.support.transition&&f.hasClass("fade")?f.one(a.support.transition.end,c).emulateTransitionEnd(150):c());};var d=a.fn.alert;a.fn.alert=function(b){return this.each(function(){var d=a(this),e=d.data("bs.alert");e||d.data("bs.alert",e=new c(this)),"string"==typeof b&&e[b].call(d);});},a.fn.alert.Constructor=c,a.fn.alert.noConflict=function(){return a.fn.alert=d,this;},a(document).on("click.bs.alert.data-api",b,c.prototype.close);}(jQuery),+function(a){var b=function(c,d){this.$element=a(c),this.options=a.extend({},b.DEFAULTS,d),this.isLoading=!1;};b.DEFAULTS={loadingText:"loading..."},b.prototype.setState=function(b){var c="disabled",d=this.$element,e=d.is("input")?"val":"html",f=d.data();b+="Text",f.resetText||d.data("resetText",d[e]()),d[e](f[b]||this.options[b]),setTimeout(a.proxy(function(){"loadingText"==b?(this.isLoading=!0,d.addClass(c).attr(c,c)):this.isLoading&&(this.isLoading=!1,d.removeClass(c).removeAttr(c));},this),0);},b.prototype.toggle=function(){var a=!0,b=this.$element.closest('[data-toggle="buttons"]');if(b.length){var c=this.$element.find("input");"radio"==c.prop("type")&&(c.prop("checked")&&this.$element.hasClass("active")?a=!1:b.find(".active").removeClass("active")),a&&c.prop("checked",!this.$element.hasClass("active")).trigger("change");}a&&this.$element.toggleClass("active");};var c=a.fn.button;a.fn.button=function(c){return this.each(function(){var d=a(this),e=d.data("bs.button"),f="object"==typeof c&&c;e||d.data("bs.button",e=new b(this,f)),"toggle"==c?e.toggle():c&&e.setState(c);});},a.fn.button.Constructor=b,a.fn.button.noConflict=function(){return a.fn.button=c,this;},a(document).on("click.bs.button.data-api","[data-toggle^=button]",function(b){var c=a(b.target);c.hasClass("btn")||(c=c.closest(".btn")),c.button("toggle"),b.preventDefault();});}(jQuery),+function(a){var b=function(b,c){this.$element=a(b),this.$indicators=this.$element.find(".carousel-indicators"),this.options=c,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter",a.proxy(this.pause,this)).on("mouseleave",a.proxy(this.cycle,this));};b.DEFAULTS={interval:5000,pause:"hover",wrap:!0},b.prototype.cycle=function(b){return b||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(a.proxy(this.next,this),this.options.interval)),this;},b.prototype.getActiveIndex=function(){return this.$active=this.$element.find(".item.active"),this.$items=this.$active.parent().children(),this.$items.index(this.$active);},b.prototype.to=function(b){var c=this,d=this.getActiveIndex();return b>this.$items.length-1||0>b?void 0:this.sliding?this.$element.one("slid.bs.carousel",function(){c.to(b);}):d==b?this.pause().cycle():this.slide(b>d?"next":"prev",a(this.$items[b]));},b.prototype.pause=function(b){return b||(this.paused=!0),this.$element.find(".next, .prev").length&&a.support.transition&&(this.$element.trigger(a.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this;},b.prototype.next=function(){return this.sliding?void 0:this.slide("next");},b.prototype.prev=function(){return this.sliding?void 0:this.slide("prev");},b.prototype.slide=function(b,c){var d=this.$element.find(".item.active"),e=c||d[b](),f=this.interval,g="next"==b?"left":"right",h="next"==b?"first":"last",i=this;if(!e.length){if(!this.options.wrap){return;}e=this.$element.find(".item")[h]();}if(e.hasClass("active")){return this.sliding=!1;}var j=a.Event("slide.bs.carousel",{relatedTarget:e[0],direction:g});return this.$element.trigger(j),j.isDefaultPrevented()?void 0:(this.sliding=!0,f&&this.pause(),this.$indicators.length&&(this.$indicators.find(".active").removeClass("active"),this.$element.one("slid.bs.carousel",function(){var b=a(i.$indicators.children()[i.getActiveIndex()]);b&&b.addClass("active");})),a.support.transition&&this.$element.hasClass("slide")?(e.addClass(b),e[0].offsetWidth,d.addClass(g),e.addClass(g),d.one(a.support.transition.end,function(){e.removeClass([b,g].join(" ")).addClass("active"),d.removeClass(["active",g].join(" ")),i.sliding=!1,setTimeout(function(){i.$element.trigger("slid.bs.carousel");},0);}).emulateTransitionEnd(1000*d.css("transition-duration").slice(0,-1))):(d.removeClass("active"),e.addClass("active"),this.sliding=!1,this.$element.trigger("slid.bs.carousel")),f&&this.cycle(),this);};var c=a.fn.carousel;a.fn.carousel=function(c){return this.each(function(){var d=a(this),e=d.data("bs.carousel"),f=a.extend({},b.DEFAULTS,d.data(),"object"==typeof c&&c),g="string"==typeof c?c:f.slide;e||d.data("bs.carousel",e=new b(this,f)),"number"==typeof c?e.to(c):g?e[g]():f.interval&&e.pause().cycle();});},a.fn.carousel.Constructor=b,a.fn.carousel.noConflict=function(){return a.fn.carousel=c,this;},a(document).on("touchstart",".fpca_diapositives [data-slide], .fpca_diapositives [data-slide-to]",function(b){var c, d=a(this), e=a(d.attr("data-target")||(c=d.attr("href"))&&c.replace(/.*(?=#[^\s]+$)/, "")), f=a.extend({}, e.data(), d.data()), g=d.attr("data-slide-to");	g&&(f.interval=!1),	e.carousel(f), (g=d.attr("data-slide-to"))&&e.data("bs.carousel").to(g), b.preventDefault(), e.carousel("pause")}),a(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",function(b){var c,d=a(this),e=a(d.attr("data-target")||(c=d.attr("href"))&&c.replace(/.*(?=#[^\s]+$)/,"")),f=a.extend({},e.data(),d.data()),g=d.attr("data-slide-to");g&&(f.interval=!1),e.carousel(f),(g=d.attr("data-slide-to"))&&e.data("bs.carousel").to(g),b.preventDefault();}),a(window).on("load",function(){a('[data-ride="carousel"]').each(function(){var b=a(this);b.carousel(b.data());});});}(jQuery),+function(a){var b=function(c,d){this.$element=a(c),this.options=a.extend({},b.DEFAULTS,d),this.transitioning=null,this.options.parent&&(this.$parent=a(this.options.parent)),this.options.toggle&&this.toggle();};b.DEFAULTS={toggle:!0},b.prototype.dimension=function(){var a=this.$element.hasClass("width");return a?"width":"height";},b.prototype.show=function(){if(!this.transitioning&&!this.$element.hasClass("in")){var b=a.Event("show.bs.collapse");if(this.$element.trigger(b),!b.isDefaultPrevented()){var c=this.$parent&&this.$parent.find("> .panel > .in");if(c&&c.length){var d=c.data("bs.collapse");if(d&&d.transitioning){return;}c.collapse("hide"),d||c.data("bs.collapse",null);}var e=this.dimension();this.$element.removeClass("collapse").addClass("collapsing")[e](0),this.transitioning=1;var f=function(){this.$element.removeClass("collapsing").addClass("collapse in")[e]("auto"),this.transitioning=0,this.$element.trigger("shown.bs.collapse");};if(!a.support.transition){return f.call(this);}var g=a.camelCase(["scroll",e].join("-"));this.$element.one(a.support.transition.end,a.proxy(f,this)).emulateTransitionEnd(350)[e](this.$element[0][g]);}}},b.prototype.hide=function(){if(!this.transitioning&&this.$element.hasClass("in")){var b=a.Event("hide.bs.collapse");if(this.$element.trigger(b),!b.isDefaultPrevented()){var c=this.dimension();this.$element[c](this.$element[c]())[0].offsetHeight,this.$element.addClass("collapsing").removeClass("collapse").removeClass("in"),this.transitioning=1;var d=function(){this.transitioning=0,this.$element.trigger("hidden.bs.collapse").removeClass("collapsing").addClass("collapse");};return a.support.transition?void this.$element[c](0).one(a.support.transition.end,a.proxy(d,this)).emulateTransitionEnd(350):d.call(this);}}},b.prototype.toggle=function(){this[this.$element.hasClass("in")?"hide":"show"]();};var c=a.fn.collapse;a.fn.collapse=function(c){return this.each(function(){var d=a(this),e=d.data("bs.collapse"),f=a.extend({},b.DEFAULTS,d.data(),"object"==typeof c&&c);!e&&f.toggle&&"show"==c&&(c=!c),e||d.data("bs.collapse",e=new b(this,f)),"string"==typeof c&&e[c]();});},a.fn.collapse.Constructor=b,a.fn.collapse.noConflict=function(){return a.fn.collapse=c,this;},a(document).on("click.bs.collapse.data-api","[data-toggle=collapse]",function(b){var c,d=a(this),e=d.attr("data-target")||b.preventDefault()||(c=d.attr("href"))&&c.replace(/.*(?=#[^\s]+$)/,""),f=a(e),g=f.data("bs.collapse"),h=g?"toggle":d.data(),i=d.attr("data-parent"),j=i&&a(i);g&&g.transitioning||(j&&j.find('[data-toggle=collapse][data-parent="'+i+'"]').not(d).addClass("collapsed"),d[f.hasClass("in")?"addClass":"removeClass"]("collapsed")),f.collapse(h);});}(jQuery),+function(a){function b(b){a(d).remove(),a(e).each(function(){var d=c(a(this)),e={relatedTarget:this};d.hasClass("open")&&(d.trigger(b=a.Event("hide.bs.dropdown",e)),b.isDefaultPrevented()||d.removeClass("open").trigger("hidden.bs.dropdown",e));});}function c(b){var c=b.attr("data-target");c||(c=b.attr("href"),c=c&&/#[A-Za-z]/.test(c)&&c.replace(/.*(?=#[^\s]*$)/,""));var d=c&&a(c);return d&&d.length?d:b.parent();}var d=".dropdown-backdrop",e="[data-toggle=dropdown]",f=function(b){a(b).on("click.bs.dropdown",this.toggle);};f.prototype.toggle=function(d){var e=a(this);if(!e.is(".disabled, :disabled")){var f=c(e),g=f.hasClass("open");if(b(),!g){"ontouchstart" in document.documentElement&&!f.closest(".navbar-nav").length&&a('<div class="dropdown-backdrop"/>').insertAfter(a(this)).on("click",b);var h={relatedTarget:this};if(f.trigger(d=a.Event("show.bs.dropdown",h)),d.isDefaultPrevented()){return;}f.toggleClass("open").trigger("shown.bs.dropdown",h),e.focus();}return !1;}},f.prototype.keydown=function(b){if(/(38|40|27)/.test(b.keyCode)){var d=a(this);if(b.preventDefault(),b.stopPropagation(),!d.is(".disabled, :disabled")){var f=c(d),g=f.hasClass("open");if(!g||g&&27==b.keyCode){return 27==b.which&&f.find(e).focus(),d.click();}var h=" li:not(.divider):visible a",i=f.find("[role=menu]"+h+", [role=listbox]"+h);if(i.length){var j=i.index(i.filter(":focus"));38==b.keyCode&&j>0&&j--,40==b.keyCode&&j<i.length-1&&j++,~j||(j=0),i.eq(j).focus();}}}};var g=a.fn.dropdown;a.fn.dropdown=function(b){return this.each(function(){var c=a(this),d=c.data("bs.dropdown");d||c.data("bs.dropdown",d=new f(this)),"string"==typeof b&&d[b].call(c);});},a.fn.dropdown.Constructor=f,a.fn.dropdown.noConflict=function(){return a.fn.dropdown=g,this;},a(document).on("click.bs.dropdown.data-api",b).on("click.bs.dropdown.data-api",".dropdown form",function(a){a.stopPropagation();}).on("click.bs.dropdown.data-api",e,f.prototype.toggle).on("keydown.bs.dropdown.data-api",e+", [role=menu], [role=listbox]",f.prototype.keydown);}(jQuery),+function(a){var b=function(b,c){this.options=c,this.$element=a(b),this.$backdrop=this.isShown=null,this.options.remote&&this.$element.find(".modal-content").load(this.options.remote,a.proxy(function(){this.$element.trigger("loaded.bs.modal");},this));};b.DEFAULTS={backdrop:!0,keyboard:!0,show:!0},b.prototype.toggle=function(a){return this[this.isShown?"hide":"show"](a);},b.prototype.show=function(b){var c=this,d=a.Event("show.bs.modal",{relatedTarget:b});this.$element.trigger(d),this.isShown||d.isDefaultPrevented()||(this.isShown=!0,this.escape(),this.$element.on("click.dismiss.bs.modal",'[data-dismiss="modal"]',a.proxy(this.hide,this)),this.backdrop(function(){var d=a.support.transition&&c.$element.hasClass("fade");c.$element.parent().length||c.$element.appendTo(document.body),c.$element.show().scrollTop(0),d&&c.$element[0].offsetWidth,c.$element.addClass("in").attr("aria-hidden",!1),c.enforceFocus();var e=a.Event("shown.bs.modal",{relatedTarget:b});d?c.$element.find(".modal-dialog").one(a.support.transition.end,function(){c.$element.focus().trigger(e);}).emulateTransitionEnd(300):c.$element.focus().trigger(e);}));},b.prototype.hide=function(b){b&&b.preventDefault(),b=a.Event("hide.bs.modal"),this.$element.trigger(b),this.isShown&&!b.isDefaultPrevented()&&(this.isShown=!1,this.escape(),a(document).off("focusin.bs.modal"),this.$element.removeClass("in").attr("aria-hidden",!0).off("click.dismiss.bs.modal"),a.support.transition&&this.$element.hasClass("fade")?this.$element.one(a.support.transition.end,a.proxy(this.hideModal,this)).emulateTransitionEnd(300):this.hideModal());},b.prototype.enforceFocus=function(){a(document).off("focusin.bs.modal").on("focusin.bs.modal",a.proxy(function(a){this.$element[0]===a.target||this.$element.has(a.target).length||this.$element.focus();},this));},b.prototype.escape=function(){this.isShown&&this.options.keyboard?this.$element.on("keyup.dismiss.bs.modal",a.proxy(function(a){27==a.which&&this.hide();},this)):this.isShown||this.$element.off("keyup.dismiss.bs.modal");},b.prototype.hideModal=function(){var a=this;this.$element.hide(),this.backdrop(function(){a.removeBackdrop(),a.$element.trigger("hidden.bs.modal");});},b.prototype.removeBackdrop=function(){this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null;},b.prototype.backdrop=function(b){var c=this.$element.hasClass("fade")?"fade":"";if(this.isShown&&this.options.backdrop){var d=a.support.transition&&c;if(this.$backdrop=a('<div class="modal-backdrop '+c+'" />').appendTo(document.body),this.$element.on("click.dismiss.bs.modal",a.proxy(function(a){a.target===a.currentTarget&&("static"==this.options.backdrop?this.$element[0].focus.call(this.$element[0]):this.hide.call(this));},this)),d&&this.$backdrop[0].offsetWidth,this.$backdrop.addClass("in"),!b){return;}d?this.$backdrop.one(a.support.transition.end,b).emulateTransitionEnd(150):b();}else{!this.isShown&&this.$backdrop?(this.$backdrop.removeClass("in"),a.support.transition&&this.$element.hasClass("fade")?this.$backdrop.one(a.support.transition.end,b).emulateTransitionEnd(150):b()):b&&b();}};var c=a.fn.modal;a.fn.modal=function(c,d){return this.each(function(){var e=a(this),f=e.data("bs.modal"),g=a.extend({},b.DEFAULTS,e.data(),"object"==typeof c&&c);f||e.data("bs.modal",f=new b(this,g)),"string"==typeof c?f[c](d):g.show&&f.show(d);});},a.fn.modal.Constructor=b,a.fn.modal.noConflict=function(){return a.fn.modal=c,this;},a(document).on("click.bs.modal.data-api",'[data-toggle="modal"]',function(b){var c=a(this),d=c.attr("href"),e=a(c.attr("data-target")||d&&d.replace(/.*(?=#[^\s]+$)/,"")),f=e.data("bs.modal")?"toggle":a.extend({remote:!/#/.test(d)&&d},e.data(),c.data());c.is("a")&&b.preventDefault(),e.modal(f,this).one("hide",function(){c.is(":visible")&&c.focus();});}),a(document).on("show.bs.modal",".modal",function(){a(document.body).addClass("modal-open");}).on("hidden.bs.modal",".modal",function(){a(document.body).removeClass("modal-open");});}(jQuery),+function(a){var b=function(a,b){this.type=this.options=this.enabled=this.timeout=this.hoverState=this.$element=null,this.init("tooltip",a,b);};b.DEFAULTS={animation:!0,placement:"top",selector:!1,template:'<div class="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>',trigger:"hover focus",title:"",delay:0,html:!1,container:!1},b.prototype.init=function(b,c,d){this.enabled=!0,this.type=b,this.$element=a(c),this.options=this.getOptions(d);for(var e=this.options.trigger.split(" "),f=e.length;f--;){var g=e[f];if("click"==g){this.$element.on("click."+this.type,this.options.selector,a.proxy(this.toggle,this));}else{if("manual"!=g){var h="hover"==g?"mouseenter":"focusin",i="hover"==g?"mouseleave":"focusout";this.$element.on(h+"."+this.type,this.options.selector,a.proxy(this.enter,this)),this.$element.on(i+"."+this.type,this.options.selector,a.proxy(this.leave,this));}}}this.options.selector?this._options=a.extend({},this.options,{trigger:"manual",selector:""}):this.fixTitle();},b.prototype.getDefaults=function(){return b.DEFAULTS;},b.prototype.getOptions=function(b){return b=a.extend({},this.getDefaults(),this.$element.data(),b),b.delay&&"number"==typeof b.delay&&(b.delay={show:b.delay,hide:b.delay}),b;},b.prototype.getDelegateOptions=function(){var b={},c=this.getDefaults();return this._options&&a.each(this._options,function(a,d){c[a]!=d&&(b[a]=d);}),b;},b.prototype.enter=function(b){var c=b instanceof this.constructor?b:a(b.currentTarget)[this.type](this.getDelegateOptions()).data("bs."+this.type);return clearTimeout(c.timeout),c.hoverState="in",c.options.delay&&c.options.delay.show?void (c.timeout=setTimeout(function(){"in"==c.hoverState&&c.show();},c.options.delay.show)):c.show();},b.prototype.leave=function(b){var c=b instanceof this.constructor?b:a(b.currentTarget)[this.type](this.getDelegateOptions()).data("bs."+this.type);return clearTimeout(c.timeout),c.hoverState="out",c.options.delay&&c.options.delay.hide?void (c.timeout=setTimeout(function(){"out"==c.hoverState&&c.hide();},c.options.delay.hide)):c.hide();},b.prototype.show=function(){var b=a.Event("show.bs."+this.type);if(this.hasContent()&&this.enabled){if(this.$element.trigger(b),b.isDefaultPrevented()){return;}var c=this,d=this.tip();this.setContent(),this.options.animation&&d.addClass("fade");var e="function"==typeof this.options.placement?this.options.placement.call(this,d[0],this.$element[0]):this.options.placement,f=/\s?auto?\s?/i,g=f.test(e);g&&(e=e.replace(f,"")||"top"),d.detach().css({top:0,left:0,display:"block"}).addClass(e),this.options.container?d.appendTo(this.options.container):d.insertAfter(this.$element);var h=this.getPosition(),i=d[0].offsetWidth,j=d[0].offsetHeight;if(g){var k=this.$element.parent(),l=e,m=document.documentElement.scrollTop||document.body.scrollTop,n="body"==this.options.container?window.innerWidth:k.outerWidth(),o="body"==this.options.container?window.innerHeight:k.outerHeight(),p="body"==this.options.container?0:k.offset().left;e="bottom"==e&&h.top+h.height+j-m>o?"top":"top"==e&&h.top-m-j<0?"bottom":"right"==e&&h.right+i>n?"left":"left"==e&&h.left-i<p?"right":e,d.removeClass(l).addClass(e);}var q=this.getCalculatedOffset(e,h,i,j);this.applyPlacement(q,e),this.hoverState=null;var r=function(){c.$element.trigger("shown.bs."+c.type);};a.support.transition&&this.$tip.hasClass("fade")?d.one(a.support.transition.end,r).emulateTransitionEnd(150):r();}},b.prototype.applyPlacement=function(b,c){var d,e=this.tip(),f=e[0].offsetWidth,g=e[0].offsetHeight,h=parseInt(e.css("margin-top"),10),i=parseInt(e.css("margin-left"),10);isNaN(h)&&(h=0),isNaN(i)&&(i=0),b.top=b.top+h,b.left=b.left+i,a.offset.setOffset(e[0],a.extend({using:function(a){e.css({top:Math.round(a.top),left:Math.round(a.left)});}},b),0),e.addClass("in");var j=e[0].offsetWidth,k=e[0].offsetHeight;if("top"==c&&k!=g&&(d=!0,b.top=b.top+g-k),/bottom|top/.test(c)){var l=0;b.left<0&&(l=-2*b.left,b.left=0,e.offset(b),j=e[0].offsetWidth,k=e[0].offsetHeight),this.replaceArrow(l-f+j,j,"left");}else{this.replaceArrow(k-g,k,"top");}d&&e.offset(b);},b.prototype.replaceArrow=function(a,b,c){this.arrow().css(c,a?50*(1-a/b)+"%":"");},b.prototype.setContent=function(){var a=this.tip(),b=this.getTitle();a.find(".tooltip-inner")[this.options.html?"html":"text"](b),a.removeClass("fade in top bottom left right");},b.prototype.hide=function(){function b(){"in"!=c.hoverState&&d.detach(),c.$element.trigger("hidden.bs."+c.type);}var c=this,d=this.tip(),e=a.Event("hide.bs."+this.type);return this.$element.trigger(e),e.isDefaultPrevented()?void 0:(d.removeClass("in"),a.support.transition&&this.$tip.hasClass("fade")?d.one(a.support.transition.end,b).emulateTransitionEnd(150):b(),this.hoverState=null,this);},b.prototype.fixTitle=function(){var a=this.$element;(a.attr("title")||"string"!=typeof a.attr("data-original-title"))&&a.attr("data-original-title",a.attr("title")||"").attr("title","");},b.prototype.hasContent=function(){return this.getTitle();},b.prototype.getPosition=function(){var b=this.$element[0];return a.extend({},"function"==typeof b.getBoundingClientRect?b.getBoundingClientRect():{width:b.offsetWidth,height:b.offsetHeight},this.$element.offset());},b.prototype.getCalculatedOffset=function(a,b,c,d){return"bottom"==a?{top:b.top+b.height,left:b.left+b.width/2-c/2}:"top"==a?{top:b.top-d,left:b.left+b.width/2-c/2}:"left"==a?{top:b.top+b.height/2-d/2,left:b.left-c}:{top:b.top+b.height/2-d/2,left:b.left+b.width};},b.prototype.getTitle=function(){var a,b=this.$element,c=this.options;return a=b.attr("data-original-title")||("function"==typeof c.title?c.title.call(b[0]):c.title);},b.prototype.tip=function(){return this.$tip=this.$tip||a(this.options.template);},b.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".tooltip-arrow");},b.prototype.validate=function(){this.$element[0].parentNode||(this.hide(),this.$element=null,this.options=null);},b.prototype.enable=function(){this.enabled=!0;},b.prototype.disable=function(){this.enabled=!1;},b.prototype.toggleEnabled=function(){this.enabled=!this.enabled;},b.prototype.toggle=function(b){var c=b?a(b.currentTarget)[this.type](this.getDelegateOptions()).data("bs."+this.type):this;c.tip().hasClass("in")?c.leave(c):c.enter(c);},b.prototype.destroy=function(){clearTimeout(this.timeout),this.hide().$element.off("."+this.type).removeData("bs."+this.type);};var c=a.fn.tooltip;a.fn.tooltip=function(c){return this.each(function(){var d=a(this),e=d.data("bs.tooltip"),f="object"==typeof c&&c;(e||"destroy"!=c)&&(e||d.data("bs.tooltip",e=new b(this,f)),"string"==typeof c&&e[c]());});},a.fn.tooltip.Constructor=b,a.fn.tooltip.noConflict=function(){return a.fn.tooltip=c,this;};}(jQuery),+function(a){var b=function(a,b){this.init("popover",a,b);};if(!a.fn.tooltip){throw new Error("Popover requires tooltip.js");}b.DEFAULTS=a.extend({},a.fn.tooltip.Constructor.DEFAULTS,{placement:"right",trigger:"click",content:"",template:'<div class="popover"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>'}),b.prototype=a.extend({},a.fn.tooltip.Constructor.prototype),b.prototype.constructor=b,b.prototype.getDefaults=function(){return b.DEFAULTS;},b.prototype.setContent=function(){var a=this.tip(),b=this.getTitle(),c=this.getContent();a.find(".popover-title")[this.options.html?"html":"text"](b),a.find(".popover-content")[this.options.html?"string"==typeof c?"html":"append":"text"](c),a.removeClass("fade top bottom left right in"),a.find(".popover-title").html()||a.find(".popover-title").hide();},b.prototype.hasContent=function(){return this.getTitle()||this.getContent();},b.prototype.getContent=function(){var a=this.$element,b=this.options;return a.attr("data-content")||("function"==typeof b.content?b.content.call(a[0]):b.content);},b.prototype.arrow=function(){return this.$arrow=this.$arrow||this.tip().find(".arrow");},b.prototype.tip=function(){return this.$tip||(this.$tip=a(this.options.template)),this.$tip;};var c=a.fn.popover;a.fn.popover=function(c){return this.each(function(){var d=a(this),e=d.data("bs.popover"),f="object"==typeof c&&c;(e||"destroy"!=c)&&(e||d.data("bs.popover",e=new b(this,f)),"string"==typeof c&&e[c]());});},a.fn.popover.Constructor=b,a.fn.popover.noConflict=function(){return a.fn.popover=c,this;};}(jQuery),+function(a){function b(c,d){var e,f=a.proxy(this.process,this);this.$element=a(a(c).is("body")?window:c),this.$body=a("body"),this.$scrollElement=this.$element.on("scroll.bs.scroll-spy.data-api",f),this.options=a.extend({},b.DEFAULTS,d),this.selector=(this.options.target||(e=a(c).attr("href"))&&e.replace(/.*(?=#[^\s]+$)/,"")||"")+" .nav li > a",this.offsets=a([]),this.targets=a([]),this.activeTarget=null,this.refresh(),this.process();}b.DEFAULTS={offset:10},b.prototype.refresh=function(){var b=this.$element[0]==window?"offset":"position";this.offsets=a([]),this.targets=a([]);var c=this;this.$body.find(this.selector).map(function(){var d=a(this),e=d.data("target")||d.attr("href"),f=/^#./.test(e)&&a(e);return f&&f.length&&f.is(":visible")&&[[f[b]().top+(!a.isWindow(c.$scrollElement.get(0))&&c.$scrollElement.scrollTop()),e]]||null;}).sort(function(a,b){return a[0]-b[0];}).each(function(){c.offsets.push(this[0]),c.targets.push(this[1]);});},b.prototype.process=function(){var a,b=this.$scrollElement.scrollTop()+this.options.offset,c=this.$scrollElement[0].scrollHeight||this.$body[0].scrollHeight,d=c-this.$scrollElement.height(),e=this.offsets,f=this.targets,g=this.activeTarget;if(b>=d){return g!=(a=f.last()[0])&&this.activate(a);}if(g&&b<=e[0]){return g!=(a=f[0])&&this.activate(a);}for(a=e.length;a--;){g!=f[a]&&b>=e[a]&&(!e[a+1]||b<=e[a+1])&&this.activate(f[a]);}},b.prototype.activate=function(b){this.activeTarget=b,a(this.selector).parentsUntil(this.options.target,".active").removeClass("active");var c=this.selector+'[data-target="'+b+'"],'+this.selector+'[href="'+b+'"]',d=a(c).parents("li").addClass("active");d.parent(".dropdown-menu").length&&(d=d.closest("li.dropdown").addClass("active")),d.trigger("activate.bs.scrollspy");};var c=a.fn.scrollspy;a.fn.scrollspy=function(c){return this.each(function(){var d=a(this),e=d.data("bs.scrollspy"),f="object"==typeof c&&c;e||d.data("bs.scrollspy",e=new b(this,f)),"string"==typeof c&&e[c]();});},a.fn.scrollspy.Constructor=b,a.fn.scrollspy.noConflict=function(){return a.fn.scrollspy=c,this;},a(window).on("load",function(){a('[data-spy="scroll"]').each(function(){var b=a(this);b.scrollspy(b.data());});});}(jQuery),+function(a){var b=function(b){this.element=a(b);};b.prototype.show=function(){var b=this.element,c=b.closest("ul:not(.dropdown-menu)"),d=b.data("target");if(d||(d=b.attr("href"),d=d&&d.replace(/.*(?=#[^\s]*$)/,"")),!b.parent("li").hasClass("active")){var e=c.find(".active:last a")[0],f=a.Event("show.bs.tab",{relatedTarget:e});if(b.trigger(f),!f.isDefaultPrevented()){var g=a(d);this.activate(b.parent("li"),c),this.activate(g,g.parent(),function(){b.trigger({type:"shown.bs.tab",relatedTarget:e});});}}},b.prototype.activate=function(b,c,d){function e(){f.removeClass("active").find("> .dropdown-menu > .active").removeClass("active"),b.addClass("active"),g?(b[0].offsetWidth,b.addClass("in")):b.removeClass("fade"),b.parent(".dropdown-menu")&&b.closest("li.dropdown").addClass("active"),d&&d();}var f=c.find("> .active"),g=d&&a.support.transition&&f.hasClass("fade");g?f.one(a.support.transition.end,e).emulateTransitionEnd(150):e(),f.removeClass("in");};var c=a.fn.tab;a.fn.tab=function(c){return this.each(function(){var d=a(this),e=d.data("bs.tab");e||d.data("bs.tab",e=new b(this)),"string"==typeof c&&e[c]();});},a.fn.tab.Constructor=b,a.fn.tab.noConflict=function(){return a.fn.tab=c,this;},a(document).on("click.bs.tab.data-api",'[data-toggle="tab"], [data-toggle="pill"]',function(b){b.preventDefault(),a(this).tab("show");});}(jQuery),+function(a){var b=function(c,d){this.options=a.extend({},b.DEFAULTS,d),this.$window=a(window).on("scroll.bs.affix.data-api",a.proxy(this.checkPosition,this)).on("click.bs.affix.data-api",a.proxy(this.checkPositionWithEventLoop,this)),this.$element=a(c),this.affixed=this.unpin=this.pinnedOffset=null,this.checkPosition();};b.RESET="affix affix-top affix-bottom",b.DEFAULTS={offset:0},b.prototype.getPinnedOffset=function(){if(this.pinnedOffset){return this.pinnedOffset;}this.$element.removeClass(b.RESET).addClass("affix");var a=this.$window.scrollTop(),c=this.$element.offset();return this.pinnedOffset=c.top-a;},b.prototype.checkPositionWithEventLoop=function(){setTimeout(a.proxy(this.checkPosition,this),1);},b.prototype.checkPosition=function(){if(this.$element.is(":visible")){var c=a(document).height(),d=this.$window.scrollTop(),e=this.$element.offset(),f=this.options.offset,g=f.top,h=f.bottom;"top"==this.affixed&&(e.top+=d),"object"!=typeof f&&(h=g=f),"function"==typeof g&&(g=f.top(this.$element)),"function"==typeof h&&(h=f.bottom(this.$element));var i=null!=this.unpin&&d+this.unpin<=e.top?!1:null!=h&&e.top+this.$element.height()>=c-h?"bottom":null!=g&&g>=d?"top":!1;if(this.affixed!==i){this.unpin&&this.$element.css("top","");var j="affix"+(i?"-"+i:""),k=a.Event(j+".bs.affix");this.$element.trigger(k),k.isDefaultPrevented()||(this.affixed=i,this.unpin="bottom"==i?this.getPinnedOffset():null,this.$element.removeClass(b.RESET).addClass(j).trigger(a.Event(j.replace("affix","affixed"))),"bottom"==i&&this.$element.offset({top:c-h-this.$element.height()}));}}};var c=a.fn.affix;a.fn.affix=function(c){return this.each(function(){var d=a(this),e=d.data("bs.affix"),f="object"==typeof c&&c;e||d.data("bs.affix",e=new b(this,f)),"string"==typeof c&&e[c]();});},a.fn.affix.Constructor=b,a.fn.affix.noConflict=function(){return a.fn.affix=c,this;},a(window).on("load",function(){a('[data-spy="affix"]').each(function(){var b=a(this),c=b.data();c.offset=c.offset||{},c.offsetBottom&&(c.offset.bottom=c.offsetBottom),c.offsetTop&&(c.offset.top=c.offsetTop),b.affix(c);});});}(jQuery);
/*
 *Touchswipe
 */
(function(e){var o="left",n="right",d="up",v="down",c="in",w="out",l="none",r="auto",k="swipe",s="pinch",x="tap",i="doubletap",b="longtap",A="horizontal",t="vertical",h="all",q=10,f="start",j="move",g="end",p="cancel",a="ontouchstart" in window,y="TouchSwipe";var m={fingers:1,threshold:75,cancelThreshold:null,pinchThreshold:20,maxTimeThreshold:null,fingerReleaseThreshold:250,longTapThreshold:500,doubleTapThreshold:200,swipe:null,swipeLeft:null,swipeRight:null,swipeUp:null,swipeDown:null,swipeStatus:null,pinchIn:null,pinchOut:null,pinchStatus:null,click:null,tap:null,doubleTap:null,longTap:null,triggerOnTouchEnd:true,triggerOnTouchLeave:false,allowPageScroll:"auto",fallbackToMouseEvents:true,excludedElements:"button, input, select, textarea, a, .noSwipe"};e.fn.swipe=function(D){var C=e(this),B=C.data(y);if(B&&typeof D==="string"){if(B[D]){return B[D].apply(this,Array.prototype.slice.call(arguments,1));}else{e.error("Method "+D+" does not exist on jQuery.swipe");}}else{if(!B&&(typeof D==="object"||!D)){return u.apply(this,arguments);}}return C;};e.fn.swipe.defaults=m;e.fn.swipe.phases={PHASE_START:f,PHASE_MOVE:j,PHASE_END:g,PHASE_CANCEL:p};e.fn.swipe.directions={LEFT:o,RIGHT:n,UP:d,DOWN:v,IN:c,OUT:w};e.fn.swipe.pageScroll={NONE:l,HORIZONTAL:A,VERTICAL:t,AUTO:r};e.fn.swipe.fingers={ONE:1,TWO:2,THREE:3,ALL:h};function u(B){if(B&&(B.allowPageScroll===undefined&&(B.swipe!==undefined||B.swipeStatus!==undefined))){B.allowPageScroll=l;}if(B.click!==undefined&&B.tap===undefined){B.tap=B.click;}if(!B){B={};}B=e.extend({},e.fn.swipe.defaults,B);return this.each(function(){var D=e(this);var C=D.data(y);if(!C){C=new z(this,B);D.data(y,C);}});}function z(a0,aq){var av=(a||!aq.fallbackToMouseEvents),G=av?"touchstart":"mousedown",au=av?"touchmove":"mousemove",R=av?"touchend":"mouseup",P=av?null:"mouseleave",az="touchcancel";var ac=0,aL=null,Y=0,aX=0,aV=0,D=1,am=0,aF=0,J=null;var aN=e(a0);var W="start";var T=0;var aM=null;var Q=0,aY=0,a1=0,aa=0,K=0;var aS=null;try{aN.bind(G,aJ);aN.bind(az,a5);}catch(ag){e.error("events not supported "+G+","+az+" on jQuery.swipe");}this.enable=function(){aN.bind(G,aJ);aN.bind(az,a5);return aN;};this.disable=function(){aG();return aN;};this.destroy=function(){aG();aN.data(y,null);return aN;};this.option=function(a8,a7){if(aq[a8]!==undefined){if(a7===undefined){return aq[a8];}else{aq[a8]=a7;}}else{e.error("Option "+a8+" does not exist on jQuery.swipe.options");}};function aJ(a9){if(ax()){return;}if(e(a9.target).closest(aq.excludedElements,aN).length>0){return;}var ba=a9.originalEvent?a9.originalEvent:a9;var a8,a7=a?ba.touches[0]:ba;W=f;if(a){T=ba.touches.length;}else{a9.preventDefault();}ac=0;aL=null;aF=null;Y=0;aX=0;aV=0;D=1;am=0;aM=af();J=X();O();if(!a||(T===aq.fingers||aq.fingers===h)||aT()){ae(0,a7);Q=ao();if(T==2){ae(1,ba.touches[1]);aX=aV=ap(aM[0].start,aM[1].start);}if(aq.swipeStatus||aq.pinchStatus){a8=L(ba,W);}}else{a8=false;}if(a8===false){W=p;L(ba,W);return a8;}else{ak(true);}}function aZ(ba){var bd=ba.originalEvent?ba.originalEvent:ba;if(W===g||W===p||ai()){return;}var a9,a8=a?bd.touches[0]:bd;var bb=aD(a8);aY=ao();if(a){T=bd.touches.length;}W=j;if(T==2){if(aX==0){ae(1,bd.touches[1]);aX=aV=ap(aM[0].start,aM[1].start);}else{aD(bd.touches[1]);aV=ap(aM[0].end,aM[1].end);aF=an(aM[0].end,aM[1].end);}D=a3(aX,aV);am=Math.abs(aX-aV);}if((T===aq.fingers||aq.fingers===h)||!a||aT()){aL=aH(bb.start,bb.end);ah(ba,aL);ac=aO(bb.start,bb.end);Y=aI();aE(aL,ac);if(aq.swipeStatus||aq.pinchStatus){a9=L(bd,W);}if(!aq.triggerOnTouchEnd||aq.triggerOnTouchLeave){var a7=true;if(aq.triggerOnTouchLeave){var bc=aU(this);a7=B(bb.end,bc);}if(!aq.triggerOnTouchEnd&&a7){W=ay(j);}else{if(aq.triggerOnTouchLeave&&!a7){W=ay(g);}}if(W==p||W==g){L(bd,W);}}}else{W=p;L(bd,W);}if(a9===false){W=p;L(bd,W);}}function I(a7){var a8=a7.originalEvent;if(a){if(a8.touches.length>0){C();return true;}}if(ai()){T=aa;}a7.preventDefault();aY=ao();Y=aI();if(a6()){W=p;L(a8,W);}else{if(aq.triggerOnTouchEnd||(aq.triggerOnTouchEnd==false&&W===j)){W=g;L(a8,W);}else{if(!aq.triggerOnTouchEnd&&a2()){W=g;aB(a8,W,x);}else{if(W===j){W=p;L(a8,W);}}}}ak(false);}function a5(){T=0;aY=0;Q=0;aX=0;aV=0;D=1;O();ak(false);}function H(a7){var a8=a7.originalEvent;if(aq.triggerOnTouchLeave){W=ay(g);L(a8,W);}}function aG(){aN.unbind(G,aJ);aN.unbind(az,a5);aN.unbind(au,aZ);aN.unbind(R,I);if(P){aN.unbind(P,H);}ak(false);}function ay(bb){var ba=bb;var a9=aw();var a8=aj();var a7=a6();if(!a9||a7){ba=p;}else{if(a8&&bb==j&&(!aq.triggerOnTouchEnd||aq.triggerOnTouchLeave)){ba=g;}else{if(!a8&&bb==g&&aq.triggerOnTouchLeave){ba=p;}}}return ba;}function L(a9,a7){var a8=undefined;if(F()||S()){a8=aB(a9,a7,k);}else{if((M()||aT())&&a8!==false){a8=aB(a9,a7,s);}}if(aC()&&a8!==false){a8=aB(a9,a7,i);}else{if(al()&&a8!==false){a8=aB(a9,a7,b);}else{if(ad()&&a8!==false){a8=aB(a9,a7,x);}}}if(a7===p){a5(a9);}if(a7===g){if(a){if(a9.touches.length==0){a5(a9);}}else{a5(a9);}}return a8;}function aB(ba,a7,a9){var a8=undefined;if(a9==k){aN.trigger("swipeStatus",[a7,aL||null,ac||0,Y||0,T]);if(aq.swipeStatus){a8=aq.swipeStatus.call(aN,ba,a7,aL||null,ac||0,Y||0,T);if(a8===false){return false;}}if(a7==g&&aR()){aN.trigger("swipe",[aL,ac,Y,T]);if(aq.swipe){a8=aq.swipe.call(aN,ba,aL,ac,Y,T);if(a8===false){return false;}}switch(aL){case o:aN.trigger("swipeLeft",[aL,ac,Y,T]);if(aq.swipeLeft){a8=aq.swipeLeft.call(aN,ba,aL,ac,Y,T);}break;case n:aN.trigger("swipeRight",[aL,ac,Y,T]);if(aq.swipeRight){a8=aq.swipeRight.call(aN,ba,aL,ac,Y,T);}break;case d:aN.trigger("swipeUp",[aL,ac,Y,T]);if(aq.swipeUp){a8=aq.swipeUp.call(aN,ba,aL,ac,Y,T);}break;case v:aN.trigger("swipeDown",[aL,ac,Y,T]);if(aq.swipeDown){a8=aq.swipeDown.call(aN,ba,aL,ac,Y,T);}break;}}}if(a9==s){aN.trigger("pinchStatus",[a7,aF||null,am||0,Y||0,T,D]);if(aq.pinchStatus){a8=aq.pinchStatus.call(aN,ba,a7,aF||null,am||0,Y||0,T,D);if(a8===false){return false;}}if(a7==g&&a4()){switch(aF){case c:aN.trigger("pinchIn",[aF||null,am||0,Y||0,T,D]);if(aq.pinchIn){a8=aq.pinchIn.call(aN,ba,aF||null,am||0,Y||0,T,D);}break;case w:aN.trigger("pinchOut",[aF||null,am||0,Y||0,T,D]);if(aq.pinchOut){a8=aq.pinchOut.call(aN,ba,aF||null,am||0,Y||0,T,D);}break;}}}if(a9==x){if(a7===p||a7===g){clearTimeout(aS);if(V()&&!E()){K=ao();aS=setTimeout(e.proxy(function(){K=null;aN.trigger("tap",[ba.target]);if(aq.tap){a8=aq.tap.call(aN,ba,ba.target);}},this),aq.doubleTapThreshold);}else{K=null;aN.trigger("tap",[ba.target]);if(aq.tap){a8=aq.tap.call(aN,ba,ba.target);}}}}else{if(a9==i){if(a7===p||a7===g){clearTimeout(aS);K=null;aN.trigger("doubletap",[ba.target]);if(aq.doubleTap){a8=aq.doubleTap.call(aN,ba,ba.target);}}}else{if(a9==b){if(a7===p||a7===g){clearTimeout(aS);K=null;aN.trigger("longtap",[ba.target]);if(aq.longTap){a8=aq.longTap.call(aN,ba,ba.target);}}}}}return a8;}function aj(){var a7=true;if(aq.threshold!==null){a7=ac>=aq.threshold;}return a7;}function a6(){var a7=false;if(aq.cancelThreshold!==null&&aL!==null){a7=(aP(aL)-ac)>=aq.cancelThreshold;}return a7;}function ab(){if(aq.pinchThreshold!==null){return am>=aq.pinchThreshold;}return true;}function aw(){var a7;if(aq.maxTimeThreshold){if(Y>=aq.maxTimeThreshold){a7=false;}else{a7=true;}}else{a7=true;}return a7;}function ah(a7,a8){if(aq.allowPageScroll===l||aT()){a7.preventDefault();}else{var a9=aq.allowPageScroll===r;switch(a8){case o:if((aq.swipeLeft&&a9)||(!a9&&aq.allowPageScroll!=A)){a7.preventDefault();}break;case n:if((aq.swipeRight&&a9)||(!a9&&aq.allowPageScroll!=A)){a7.preventDefault();}break;case d:if((aq.swipeUp&&a9)||(!a9&&aq.allowPageScroll!=t)){a7.preventDefault();}break;case v:if((aq.swipeDown&&a9)||(!a9&&aq.allowPageScroll!=t)){a7.preventDefault();}break;}}}function a4(){var a8=aK();var a7=U();var a9=ab();return a8&&a7&&a9;}function aT(){return !!(aq.pinchStatus||aq.pinchIn||aq.pinchOut);}function M(){return !!(a4()&&aT());}function aR(){var ba=aw();var bc=aj();var a9=aK();var a7=U();var a8=a6();var bb=!a8&&a7&&a9&&bc&&ba;return bb;}function S(){return !!(aq.swipe||aq.swipeStatus||aq.swipeLeft||aq.swipeRight||aq.swipeUp||aq.swipeDown);}function F(){return !!(aR()&&S());}function aK(){return((T===aq.fingers||aq.fingers===h)||!a);}function U(){return aM[0].end.x!==0;}function a2(){return !!(aq.tap);}function V(){return !!(aq.doubleTap);}function aQ(){return !!(aq.longTap);}function N(){if(K==null){return false;}var a7=ao();return(V()&&((a7-K)<=aq.doubleTapThreshold));}function E(){return N();}function at(){return((T===1||!a)&&(isNaN(ac)||ac===0));}function aW(){return((Y>aq.longTapThreshold)&&(ac<q));}function ad(){return !!(at()&&a2());}function aC(){return !!(N()&&V());}function al(){return !!(aW()&&aQ());}function C(){a1=ao();aa=event.touches.length+1;}function O(){a1=0;aa=0;}function ai(){var a7=false;if(a1){var a8=ao()-a1;if(a8<=aq.fingerReleaseThreshold){a7=true;}}return a7;}function ax(){return !!(aN.data(y+"_intouch")===true);}function ak(a7){if(a7===true){aN.bind(au,aZ);aN.bind(R,I);if(P){aN.bind(P,H);}}else{aN.unbind(au,aZ,false);aN.unbind(R,I,false);if(P){aN.unbind(P,H,false);}}aN.data(y+"_intouch",a7===true);}function ae(a8,a7){var a9=a7.identifier!==undefined?a7.identifier:0;aM[a8].identifier=a9;aM[a8].start.x=aM[a8].end.x=a7.pageX||a7.clientX;aM[a8].start.y=aM[a8].end.y=a7.pageY||a7.clientY;return aM[a8];}function aD(a7){var a9=a7.identifier!==undefined?a7.identifier:0;var a8=Z(a9);a8.end.x=a7.pageX||a7.clientX;a8.end.y=a7.pageY||a7.clientY;return a8;}function Z(a8){for(var a7=0;a7<aM.length;a7++){if(aM[a7].identifier==a8){return aM[a7];}}}function af(){var a7=[];for(var a8=0;a8<=5;a8++){a7.push({start:{x:0,y:0},end:{x:0,y:0},identifier:0});}return a7;}function aE(a7,a8){a8=Math.max(a8,aP(a7));J[a7].distance=a8;}function aP(a7){return J[a7].distance;}function X(){var a7={};a7[o]=ar(o);a7[n]=ar(n);a7[d]=ar(d);a7[v]=ar(v);return a7;}function ar(a7){return{direction:a7,distance:0};}function aI(){return aY-Q;}function ap(ba,a9){var a8=Math.abs(ba.x-a9.x);var a7=Math.abs(ba.y-a9.y);return Math.round(Math.sqrt(a8*a8+a7*a7));}function a3(a7,a8){var a9=(a8/a7)*1;return a9.toFixed(2);}function an(){if(D<1){return w;}else{return c;}}function aO(a8,a7){return Math.round(Math.sqrt(Math.pow(a7.x-a8.x,2)+Math.pow(a7.y-a8.y,2)));}function aA(ba,a8){var a7=ba.x-a8.x;var bc=a8.y-ba.y;var a9=Math.atan2(bc,a7);var bb=Math.round(a9*180/Math.PI);if(bb<0){bb=360-Math.abs(bb);}return bb;}function aH(a8,a7){var a9=aA(a8,a7);if((a9<=45)&&(a9>=0)){return o;}else{if((a9<=360)&&(a9>=315)){return o;}else{if((a9>=135)&&(a9<=225)){return n;}else{if((a9>45)&&(a9<135)){return v;}else{return d;}}}}}function ao(){var a7=new Date();return a7.getTime();}function aU(a7){a7=e(a7);var a9=a7.offset();var a8={left:a9.left,right:a9.left+a7.outerWidth(),top:a9.top,bottom:a9.top+a7.outerHeight()};return a8;}function B(a7,a8){return(a7.x>a8.left&&a7.x<a8.right&&a7.y>a8.top&&a7.y<a8.bottom);}}})(jQuery);
/*
* jquery.customSelect() - v0.4.1
* http://adam.co/lab/jquery/customselect/
* 2013-05-13
*
* Copyright 2013 Adam Coulombe
* @license http://www.opensource.org/licenses/mit-license.html MIT License
* @license http://www.gnu.org/licenses/gpl.html GPL2 License
*/
function tornarAmunt(){$("html, body").animate({scrollTop:0},"slow")}function amunt(){$("html,body").scrollTop(0)}function CompartirTwitter(){var a=575,b=400,c=($(window).width()-a)/2,d=($(window).height()-b)/2,e="http://twitter.com/share?text=",f="status=1,width="+a+",height="+b+",top="+d+",left="+c;e+=document.title,e+=" ",window.open(e,"Twitter",f)}function CompartirFacebook(){var a=575,b=400,c=($(window).width()-a)/2,d=($(window).height()-b)/2,e="https://www.facebook.com/sharer/sharer.php?u=",f="status=1,width="+a+",height="+b+",top="+d+",left="+c;e+=document.URL,window.open(e,"Facebook",f)}!function(a){a.fn.extend({customSelect:function(b){if(void 0===document.body.style.maxHeight)return this;b=a.extend({customClass:"customSelect",mapClass:!0,mapStyle:!0},b);var c=b.customClass,d=function(b,d){var e=b.find(":selected"),f=d.children(":first"),g=e.html()||"&nbsp;";f.html(g),e.attr("disabled")?d.addClass(c+"DisabledOption"):d.removeClass(c+"DisabledOption"),setTimeout(function(){d.removeClass(c+"Open"),a(document).off("mouseup.customSelect")},60)};return this.each(function(){var e=a(this),f=a("<span />").addClass(c+"Inner"),g=a("<span />");e.after(g.append(f)),g.addClass(c),b.mapClass&&g.addClass(e.attr("class")),b.mapStyle&&g.attr("style",e.attr("style")),e.addClass("hasCustomSelect").on("render.customSelect",function(){d(e,g);var a=parseInt(e.outerWidth(),10)-(parseInt(g.outerWidth(),10)-parseInt(g.width(),10));g.css({display:"inline-block"});var b=g.outerHeight();e.attr("disabled")?g.addClass(c+"Disabled"):g.removeClass(c+"Disabled"),f.css({width:a,display:"inline-block"}),e.css({"-webkit-appearance":"menulist-button",width:g.outerWidth(),position:"absolute",opacity:0,height:b,fontSize:g.css("font-size")})}).on("change.customSelect",function(){g.addClass(c+"Changed"),d(e,g)}).on("keyup.customSelect",function(a){g.hasClass(c+"Open")?13!=a.which&&27!=a.which||d(e,g):(e.trigger("blur.customSelect"),e.trigger("focus.customSelect"))}).on("mousedown.customSelect",function(){g.removeClass(c+"Changed")}).on("mouseup.customSelect",function(b){g.hasClass(c+"Open")||(0<a("."+c+"Open").not(g).length&&"undefined"!=typeof InstallTrigger?e.trigger("focus.customSelect"):(g.addClass(c+"Open"),b.stopPropagation(),a(document).one("mouseup.customSelect",function(b){b.target!=e.get(0)&&0>a.inArray(b.target,e.find("*").get())?e.trigger("blur.customSelect"):d(e,g)})))}).on("focus.customSelect",function(){g.removeClass(c+"Changed").addClass(c+"Focus")}).on("blur.customSelect",function(){g.removeClass(c+"Focus "+c+"Open")}).on("mouseenter.customSelect",function(){g.addClass(c+"Hover")}).on("mouseleave.customSelect",function(){g.removeClass(c+"Hover")}).trigger("render.customSelect")})}})}(jQuery);var ajaxableLoaded=!1;$(document).ready(function(){function a(){if(cont=0,c>751)if(c>976)var a=5;else var a=4;else var a=2;var b=$(".llistat_imatges .item").length;$(".llistat_imatges .item").each(function(c){++cont!=a&&c+1!=b||($(this).after("<div class='cercador_imatge_detail'></div>"),cont=0)}),$("#cercador_img .nav-tabs li").each(function(){$(this).mouseup(function(){$("#cercador_mapa").hasClass("active")?$("#cercador_img .txt_result,#cercador_img .cerca_avancada").show():$("#cercador_img .txt_result,#cercador_img .cerca_avancada").hide()})}),$(".treu_filtre").click(function(){$(this).parent().find("input[type=radio]").prop("checked",!1)})}function b(){var a=window.navigator.userAgent;return a.indexOf("MSIE ")>0||(a.indexOf("Trident/")>0?(a.indexOf("rv:"),!0):a.indexOf("Edge/")>0)}$(".panel").each(function(a,b){var c=$(this);c.has(".ajaxable").length>0&&c.addClass("ajaxable")}),$(".panel-heading").each(function(a,b){var c=$(this);c.has(".map_temps_buto").length>0&&c.addClass("map_temps_buto")}),$(".accord").click(function(){var a="#"+$(this).attr("id"),b=window.location.pathname+a;window.location.replace(b),setTimeout(function(){var b=$(a).offset().top;$("body,html").animate({scrollTop:b},"500")},600)}),$(".tab").each(function(a,b){var c=$(this);c.has(".ajaxable").length>0&&c.addClass("ajaxable")}),$(".tab").each(function(a,b){var c=$(this);c.has(".map_temps_buto").length>0&&c.addClass("map_temps_buto")}),$(".ajaxable").click(function(){ajaxableLoaded?setTimeout("initLayer()",1200):(loadScript(),ajaxableLoaded=!0)}),$(".map_temps_buto").click(function(){setTimeout("redraw()",1200)}),$(window).width()<750&&($(".col-sm-4.shadowBox-sm").each(function(){if($(this).has("el_mes_consultat")){var a=$(this);a.append('<div class="neteja"></div>'),$(".col-sm-8 .pestanyes").insertAfter(a),$(this).css("margin",0),$(this).css("padding",0)}}),$(".col-sm-4.shadowBox-sm").each(function(){if($(this).has("el_mes_consultat")){var a=$(this);a.append('<div class="neteja"></div>'),$(".col-sm-8.sense-pestanyes").insertAfter(a),$(this).css("margin",0),$(this).css("padding",0)}}));var c=$(window).width();$(".all-close").click(function(){$(".ulOrganigrama a.darkGray").addClass("collapsed"),$(".ulOrganigrama .panel-collapse").removeClass("in")}),$(".all-open").click(function(){$(".ulOrganigrama a.darkGray").removeClass("collapsed"),$(".ulOrganigrama .panel-collapse").addClass("in")}),c>750?$(".cercador #cerca_avancada_id").removeClass("collapse"):$(".cercador #cerca_avancada_id").addClass("collapse"),$('.dropdown-menu img[alt="Tancar"]').click(function(){$(".dropdown").removeClass("open")}),$("button[title='Neteja']").click(function(){$(this).siblings("input").val("")}),$(".fons_header .navbar-toggle").click(function(){$(".fons_header .dos").hasClass("in")&&$(".fons_header .dos").removeClass("in")}),$(".fons_header .ico_cerca").click(function(){$(".fons_header .navbar-collapse").hasClass("in")&&$(".fons_header .navbar-collapse").removeClass("in")}),function(a){a(document).ready(function(){a("ul.dropdown-menu [data-toggle=dropdown]").on("click",function(b){b.preventDefault(),b.stopPropagation(),a(this).parent().siblings().removeClass("open"),a(this).parent().toggleClass("open")})})}(jQuery),$(".pestanyes .pestanyes_top .tab").each(function(){var a=$(this).parent().parent().parent().siblings(".pestanyes_bot").children(".panel");$(this).click(function(){var b=$(this).index();$(this).removeClass("active").siblings(".tab").removeClass("active"),$(this).addClass("active"),a.children(".panel-collapse").addClass("collapse"),a.children(".panel-collapse").removeClass("in"),a.eq(b).children(".panel-collapse").addClass("in"),a.removeClass("active"),a.eq(b).addClass("active"),a.children(".panel-title").children("a").addClass("collapsed"),a.eq(b).find(".panel-title").children().removeClass("collapsed")})}),$(".pestanyes .panel").each(function(){var a=$(this).index();$(this).find(".panel-title").children().click(function(){$(this).hasClass("collapsed")?($(".pestanyes .panel").removeClass("active"),$(this).parent().parent().parent().addClass("active"),$(".pestanyes_top .tab").removeClass("active"),$(".pestanyes_top .tab").eq(a).addClass("active")):$(".pestanyes .panel").removeClass("active")})}),$(".resultats_cerca .pestanyes_top .tab").each(function(a,b){$(this).click(function(){$(".pestanyes_top .tab").removeClass("active"),$(this).addClass("active"),$(".pestanyes_bot > .panel > .panel-collapse").addClass("collapse"),$(".pestanyes_bot > .panel > .panel-collapse").removeClass("in"),$(".pestanyes_bot > .panel").eq(a).children(".panel-collapse").addClass("in"),$(".pestanyes_bot > .panel").removeClass("active"),$(".pestanyes_bot > .panel").eq(a).addClass("active"),$(".pestanyes .panel-title a").addClass("collapsed"),$(".pestanyes .panel").eq(a).find(".panel-title").children().removeClass("collapsed")})}),$(".pestanyes.cercador_sac .panel").each(function(a,b){$(this).find(".panel-title").children().click(function(){$(this).hasClass("collapsed")?($(".pestanyes .panel").removeClass("active"),$(this).parent().parent().parent().addClass("active"),$(".pestanyes_top .tab").removeClass("active"),$(".pestanyes_top .tab").eq(a).addClass("active")):$(".pestanyes .panel").removeClass("active")})}),$(".avall").hide(),$(".mes_link").click(function(){$(this).hasClass("amunt")?($(".ocult").each(function(){$(this).slideToggle()}),$(".amunt").hide(),$(".avall").show()):($(".ocult").each(function(){$(this).slideToggle()}),$(".amunt").show(),$(".avall").hide())}),$(".el_mes_consultat .panel-heading a").click(function(){$(this).parents(".el_mes_consultat").hasClass("open")?($(this).parents(".el_mes_consultat").removeClass("open"),$(".el_mes_consultat .panel-body").animate({right:"-260px"},400,"swing")):($(this).parents(".el_mes_consultat").addClass("open"),$(".el_mes_consultat .panel-body").animate({right:"-5px"},400,"swing"))}),a(),$(window).resize(function(){c=$(window).width(),$(".cercador_imatge_cont .container").css("height","auto");var b=$(".cercador_imatge_cont .container").outerHeight();$(".cercador_imatge_cont").css("height",b),$(".cercador_imatge_detail.opened").css("height",b),$(".cercador_imatge_detail.opened").html(),$(".cercador_imatge_detail").remove(),$(this).width()>=768&&($(".fons_header .dos").removeClass("in"),$(".pestanyes .panel-collapse").hasClass("in")||$(".pestanyes .panel:first-child .panel-collapse").addClass("in")),$(".cerca_avancada_cont").hasClass("in")&&c>768&&$(".cerca_avancada_cont").addClass("in"),c>768?$("#cerca_avancada_id").removeClass("collapse"):$("#cerca_avancada_id").addClass("collapse"),a()}),jQuery.fn.nextMatching=function(a){return this.nextAll(a).first()},jQuery.fn.prevMatching=function(a){return this.prevAll(a).first()},jQuery.fn.lastMatching=function(a){return this.nextAll(a).last()},$(".cerca_avancada a").click(function(){$(".cerca_avancada a").attr("class").length>0?$(".navbar-form.col-xs-12.cercador_vermell.web_az").fadeOut("slow"):$(".navbar-form.col-xs-12.cercador_vermell.web_az").fadeIn("slow")}),$(".item img").each(function(){$(this).click(function(){$(".item").removeClass("item_opened"),$(this).parent().addClass("item_opened");var a=$(this).attr("src");if($(this).parent().nextMatching(".cercador_imatge_detail").hasClass("opened"))$(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").stop().animate({opacity:"0"},{duration:200,complete:function(){$(".cercador_imatge_detail.opened img").attr("src",a)}}),$(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").animate({opacity:"1"},200);else{$(".cercador_imatge_detail").removeClass("opened").css("height","0").empty(),$(this).parent().nextMatching(".cercador_imatge_detail").addClass("opened").append("<div class='cercador_imatge_cont'><div class='container fullcontainer-xs'><button class='cercar_detall'>cercar</button><div class='col-izq'><a class='prev_img' href='javascript:void(0)' title=''>Imatge anterior</a><a class='next_img' href='javascript:void(0)' title=''>Imatge segÃƒÂ¼ent</a><img class='img-responsive' src='' alt=''></div><div class='col-dch'><h2>Lorem ipsum sit amet amb text de tÃƒtol molt llarg de tres lÃƒnies</h2><p>Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit lorem ipsum dolor sit amet.</p><ul><li>Autor: Generalitat de Catalunya</li><li>Responsable: <a href=''>Departament de Salut</a></li><li>Data de publicaciÃƒÂ³: 07/10/2013</li><li>Mida: 1701 x 1134</li></ul><div><a href=''>UtilizaciÃƒÂ³ de les imatges i vÃƒdeos</a><a class='hidden-xs' href=''>Imprimir</a></div><ul class='list-group llistat_xarxes_socials'><li class='list-group-item social_text hidden-xs'>Comparteix l'arxiu a:</li><li class='list-group-item'><a class='twitter' href='#' title='twitter'></a></li><li class='list-group-item'><a class='facebook' href='#' title='facebook'></a></li></ul></div></div></div>"),$(".cercador_imatge_detail.opened img").attr("src",a);var b=$(".cercador_imatge_cont .container").outerHeight();$(".cercador_imatge_cont").animate({height:b},400,"swing"),$(".cercador_imatge_detail.opened").animate({height:b},400,"swing")}$(".cercar_detall").click(function(){$(".cercador_imatge_detail").stop().animate({height:0},{duration:400,easing:"swing",complete:function(){$(this).removeClass("opened")}}),$(".cercador_imatge_cont").stop().animate({height:0},{duration:400,easing:"swing",complete:function(){$(".cercador_imatge_detail").empty()}})}),$(".prev_img").click(function(){var a=$(".item.item_opened"),b=a.prevMatching(".item").children().attr("src");a.index()<=0||($(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").stop().animate({opacity:"0"},{duration:200,complete:function(){$(".cercador_imatge_detail.opened img").attr("src",b)}}),$(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").animate({opacity:"1"},200),$(a).removeClass("item_opened").prevMatching(".item").addClass("item_opened"))}),$(".next_img").click(function(){var a=$(".item.item_opened"),b=a.nextMatching(".item").children().attr("src"),c=$(".item").nextAll(".item").last(".item").index();a.index()>=c||($(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").stop().animate({opacity:"0"},{duration:200,complete:function(){$(".cercador_imatge_detail.opened img").attr("src",b)}}),$(".cercador_imatge_cont .col-izq, .cercador_imatge_cont .col-dch").animate({opacity:"1"},200),$(a).removeClass("item_opened").nextMatching(".item").addClass("item_opened"))}),$("html,body").animate({scrollTop:$(".item_opened").offset().top},"slow")})});var d = !1;$(".carousel").each(function () {$(this).on("slid.bs.carousel", "", function () {if ($(".carousel").length > 1) {var slideFrom = $(this).find('.active').index();if (slideFrom == 0) {$(this).carousel("pause");}$(this).find(".carousel-inner .item:last").hasClass("active");} else {d && $(this).carousel("pause"),$(this).find(".carousel-inner .item:last").hasClass("active") && (d = !0)}})});!b()&&$(window).width()<=1022&&$(".carousel").swipe({tap:function(){if($('.fpca_diapositives').find($(this)).length == 0){var a=$(this).find(".active a").attr("href");a&&(window.location.href=a)}},swipeLeft:function(a,b,c,d,e){$(this).carousel("next"),$(this).carousel("pause")},swipeRight:function(){$(this).carousel("prev"),$(this).carousel("pause")},threshold:0}),$(".custom_select").customSelect(),$(this).width()>=975&&($(".carousel .slide_navigator").hide(),$(".carousel .slider_imatges_cont").hover(function(){$(".carousel .slide_navigator").stop(!0,!1).fadeToggle(1e3,"linear")}))}),$(".scroll").click(function(a){a.preventDefault();var b=0;b=$(this.hash).offset().top>$(document).height()-$(window).height()?$(document).height()-$(window).height():$(this.hash).offset().top,$("html,body").animate({scrollTop:b},1e3,"swing")}),function(a){var b=function(b,c){this.$element=a(b),this.$indicators=this.$element.find(".carousel-indicators"),this.options=c,this.paused=this.sliding=this.interval=this.$active=this.$items=null,"hover"==this.options.pause&&this.$element.on("mouseenter",a.proxy(this.pause,this)).on("mouseleave",a.proxy(this.cycle,this));var d=this;this.loading=this.$element.find(".loading"),this.lazy_elements=this.$element.find(".item img[lazy-src]"),this.lazy_elements.load(function(){var b=a(this);b.attr("lazy-load","success"),d.$element.trigger("slid.bs.load.success"),d.resume(b)}).error(function(){var b=a(this);b.attr("lazy-load","error"),d.$element.trigger("slid.bs.load.error"),d.resume(b)})};b.DEFAULTS={interval:5e3,pause:"hover",wrap:!0},b.prototype.cycle=function(b){return b||(this.paused=!1),this.interval&&clearInterval(this.interval),this.options.interval&&!this.paused&&(this.interval=setInterval(a.proxy(this.next,this),this.options.interval)),this},b.prototype.getActiveIndex=function(){return this.$active=this.$element.find(".item.active"),this.$items=this.$active.parent().children(),this.$items.index(this.$active)},b.prototype.to=function(b){var c=this,d=this.getActiveIndex();if(!(b>this.$items.length-1||b<0))return this.sliding?this.$element.one("slid.bs.carousel",function(){c.to(b)}):d==b?this.pause().cycle():this.slide(b>d?"next":"prev",a(this.$items[b]))},b.prototype.pause=function(b){return b||(this.paused=!0),this.$element.find(".next, .prev").length&&a.support.transition.end&&(this.$element.trigger(a.support.transition.end),this.cycle(!0)),this.interval=clearInterval(this.interval),this},b.prototype.next=function(){if(!this.sliding)return this.slide("next")},b.prototype.prev=function(){if(!this.sliding)return this.slide("prev")},b.prototype.load=function(a){this.sliding=!1,this.loading.removeClass("hide"),a.attr("src",a.attr("lazy-src")),a.removeAttr("lazy-src"),a.attr("lazy-load","loading"),this.$element.trigger("slid.bs.load")},b.prototype.resume=function(a){var b=a.parents(".item"),c=b.parents(".carousel-inner").children(),d=c.index(b);this.loading.addClass("hide"),this.to(d),this.options.interval&&this.cycle()},b.prototype.slide=function(b,c){var d=this.$element.find(".item.active"),e=c||d[b](),f=this.interval,g="next"==b?"left":"right",h="next"==b?"first":"last",i=this;if(!e.length){if(!this.options.wrap)return;e=this.$element.find(".item")[h]()}this.sliding=!0,f&&this.pause();var j=a.Event("slide.bs.carousel",{relatedTarget:e[0],direction:g});if(!e.hasClass("active")){var k=e.find("img[lazy-src]");if(k.length)return void this.load(k);if(this.$indicators.length&&(this.$indicators.find(".active").removeClass("active"),this.$element.one("slid.bs.carousel",function(){var b=a(i.$indicators.children()[i.getActiveIndex()]);b&&b.addClass("active")})),a.support.transition&&this.$element.hasClass("slide")){if(this.$element.trigger(j),j.isDefaultPrevented())return;e.addClass(b),e[0].offsetWidth,d.addClass(g),e.addClass(g),d.one(a.support.transition.end,function(){e.removeClass([b,g].join(" ")).addClass("active"),d.removeClass(["active",g].join(" ")),i.sliding=!1,setTimeout(function(){i.$element.trigger("slid.bs.carousel")},0)}).emulateTransitionEnd(600)}else{if(this.$element.trigger(j),j.isDefaultPrevented())return;d.removeClass("active"),e.addClass("active"),this.sliding=!1,this.$element.trigger("slid.bs.carousel")}return f&&this.cycle(),this}};var c=a.fn.carousel;a.fn.carousel=function(c){return this.each(function(){var d=a(this),e=d.data("bs.carousel"),f=a.extend({},b.DEFAULTS,d.data(),"object"==typeof c&&c),g="string"==typeof c?c:f.slide;e||d.data("bs.carousel",e=new b(this,f)),"number"==typeof c?e.to(c):g?e[g]():f.interval&&e.pause().cycle()})},a.fn.carousel.Constructor=b,a.fn.carousel.noConflict=function(){return a.fn.carousel=c,this},a(document).on("touchstart",".fpca_diapositives [data-slide], .fpca_diapositives [data-slide-to]",function(b){var c, d=a(this), e=a(d.attr("data-target")||(c=d.attr("href"))&&c.replace(/.*(?=#[^\s]+$)/, "")), f=a.extend({}, e.data(), d.data()), g=d.attr("data-slide-to");	g&&(f.interval=!1),	e.carousel(f), (g=d.attr("data-slide-to"))&&e.data("bs.carousel").to(g), b.preventDefault(), e.carousel("pause")}),a(document).on("click.bs.carousel.data-api","[data-slide], [data-slide-to]",function(b){var c,d=a(this),e=a(d.attr("data-target")||(c=d.attr("href"))&&c.replace(/.*(?=#[^\s]+$)/,"")),f=a.extend({},e.data(),d.data()),g=d.attr("data-slide-to");g&&(f.interval=!1),e.carousel(f),(g=d.attr("data-slide-to"))&&e.data("bs.carousel").to(g),b.preventDefault(),e.carousel("pause")}),a(window).on("load",function(){a('[data-ride="carousel"]').each(function(){var b=a(this);b.carousel(b.data())})})}(jQuery);/*!
 /* gencat javascript Library v0.1
 * http://www.gencat.cat/
 */
function getRelativePath(){var a=document.getElementsByTagName("head")[0].getElementsByTagName("script"),b=null!=a[0].getAttribute("src").match(/\.\.\//g)?a[0].getAttribute("src").replace(/[^..\/]/g,"").replace(/\/.$/,""):"";return b}function getPathGencat(){for(var a="",b=document.getElementsByTagName("head")[0].getElementsByTagName("script"),c=0;c<b.length;c++)b[c].src.indexOf("/gencat.js")!=-1&&(a=b[c].src.slice(0,b[c].src.indexOf("/FW_FrameworkJS")));return a+"/FW_FrameworkJS/Dev/"}!function(a){function b(){var b={dom:!(!document.createElement||!document.getElementsByTagName),XHR:null,lang:"ca",externalurl:"",serviceuri:"",resources:[],getXHR:function(){var c=!1;try{a.XMLHttpRequest?c=new XMLHttpRequest:a.ActiveXObject&&(c=new ActiveXObject("Microsoft.XMLHTTP"))}catch(a){}b.XHR=c},encodeTools:{encode:function(a){a=a.replace(/\r\n/g,"\n");for(var b="",c=0;c<a.length;c++){var d=a.charCodeAt(c);d<128?b+=String.fromCharCode(d):d>127&&d<2048?(b+=String.fromCharCode(d>>6|192),b+=String.fromCharCode(63&d|128)):(b+=String.fromCharCode(d>>12|224),b+=String.fromCharCode(d>>6&63|128),b+=String.fromCharCode(63&d|128))}return b},decode:function(a){for(var b="",c=0,d=c1=c2=0;c<a.length;)d=a.charCodeAt(c),d<128?(b+=String.fromCharCode(d),c++):d>191&&d<224?(c2=a.charCodeAt(c+1),b+=String.fromCharCode((31&d)<<6|63&c2),c+=2):(c2=a.charCodeAt(c+1),c3=a.charCodeAt(c+2),b+=String.fromCharCode((15&d)<<12|(63&c2)<<6|63&c3),c+=3);return b}},utf8Tools:{encode:function(a){for(var b,c=-1,d=(a=a.split("")).length,e=String.fromCharCode;++c<d;a[c]=(b=a[c].charCodeAt(0))>=127?e(192|b>>>6)+e(128|63&b):a[c]);return a.join("")},decode:function(a){for(var b,c,d=-1,e=(a=a.split("")).length,f=String.fromCharCode,g="charCodeAt";++d<e;128&(b=a[d][g](0))&&(a[d]=f(192==(252&b)&&128==(192&(c=a[d+1][g](0)))?((3&b)<<6)+(63&c):128),a[++d]=""));return a.join("")}}};b.setLang=function(a){b.lang=a},b.load=function(a,c,d,e){for(var f=!1,g="",h=0,i=b.resources.length;h<i;h++)b.resources[h]==a&&(f=!0);if(!f){if(!c){if(a&&a.length>0&&a.lastIndexOf(".")==-1||a.length-a.lastIndexOf(".")>5)return;c=a.slice(a.lastIndexOf(".")+1)}switch(c){case"css":g='<link href="'+b.externalurl+a+'" type="text/css"',d?g=g+' rel="'+d+'"':g+=' rel="stylesheet"',e&&(g=g+' media="'+e+'"'),g+=" />";break;case"js":g='<script src="'+b.externalurl+a+'" type="text/javascript"></script>'}document.write(g),b.resources.push(a)}},b.include=function(a,c){switch(a){case"accordion":b.load("/web/resources/fwk/comuns/js/include/acordio/gencatAccordionBO.js");break;case"carousel":b.load("/web/resources/fwk/comuns/js/include/jcarousel/gencatCarouselBO.js");break;case"colorbox":b.load("/web/resources/fwk/comuns/js/include/colorbox/gencatColorboxBO.js");break;case"autocomplete":b.load("/web/resources/fwk/comuns/js/include/autocomplete/gencatAutocompleteBO.js");break;case"datepicker":b.load("/web/resources/fwk/comuns/js/include/datepicker/gencatDatepickerBO.js");break;case"galeriaBig":b.load("/web/resources/fwk/comuns/js/include/gencatBIGPlayer/gencatBIGPlayer.js");break;case"twitter":b.load("/web/resources/fwk/comuns/js/include/twitter/gencatTwitterBO.js")}},b.addEvent=function(a,b,c){return a.addEventListener?(a.addEventListener(b,c,!1),!0):!!a.attachEvent&&a.attachEvent("on"+b,c)},b.ready=function(c){b.addEvent(a,"load",c)},b.getElement=function(a){try{return $(a)}catch(e){if(quickExpr=/^[^<]*(<[\w\W]+>)[^>]*$|^#([\w-]+)$/,rsingleTag=/^<(\w+)\s*\/?>(?:<\/\1>)?$/,rsingleClass=/^\.([\w-]+)$/,elems=[],match=quickExpr.exec(a),match&&match[1]){if(match2=rsingleTag.exec(a),match2&&match2[1]){for(var c=document.getElementsByTagName(match2[1]),d=0;d<c.length;d++)elems.push(c[d]);return elems}}else{if(match&&match[2])return document.getElementById?document.getElementById(match[2]):document.all[match[2]];if(match3=rsingleClass.exec(a),match3&&match3[1])return b.getElementsByClassName(match3[1])}return null}},b.extend=function(a,b){var b=b||this;for(var c in a)b[c]=a[c];return b},b.merge=function(a,b){var c=a.length,d=0;if("number"==typeof b.length)for(var e=b.length;d<e;d++)a[c++]=b[d];else for(;void 0!==b[d];)a[c++]=b[d++];return a.length=c,a},b.getElementsByClassName=function(a,b){b||(b=document.getElementsByTagName("body")[0]);for(var c=[],d=new RegExp("\\b"+a+"\\b"),e=b.getElementsByTagName("*"),f=0,g=e.length;f<g;f++)d.test(e[f].className)&&c.push(e[f]);return c},b.loadFromURL=function(a,c,d,e){if(b.getXHR(),ajax=b.XHR,ajax.open("GET",a,e),ajax.send(null),e)ajax.onreadystatechange=function(){4!=ajax.readyState||200!=ajax.status&&0!=ajax.status||(c?c(d?ajax.responseXML:ajax.responseText):d?ajax.responseXML:ajax.responseText)};else{if(c)return c(d?ajax.responseXML:ajax.responseText);d?ajax.responseXML:ajax.responseText}},b.loadFromText=function(a){var b=null;try{b=new ActiveXObject("Microsoft.XMLDOM"),b.async="false",b.loadXML(a)}catch(c){try{parser=new DOMParser,b=parser.parseFromString(a,"text/xml")}catch(a){}}return b},b.addMethod=function(a,b,c){var d=a[b];d?a[b]=function(){return c.length==arguments.length?c.apply(this,arguments):"function"==typeof d?d.apply(this,arguments):void 0}:a[b]=c},b.encodeHtml=function(a,b){var c="";for(i=0;i<a.length;i++)c+=a.charCodeAt(i)>127?"&#"+a.charCodeAt(i)+";":1==b?"<"==a.charAt(i)?"&lt;":">"==a.charAt(i)?"&gt;":"&"==a.charAt(i)?"&amp;":a.charAt(i):2==b?"<"==a.charAt(i)?"&lt;":">"==a.charAt(i)?"&gt;":a.charAt(i):a.charAt(i);return c},b.encodeDec=function(a){for(var b="",c=0;c<a.length;c++)a.charCodeAt(c)>=192?b=b+"\\"+a.charCodeAt(c):b+=a[c];return b},b.clone=function(a,b){if(null==a||"object"!=typeof a)return a;if(a.constructor!=Object&&a.constructor!=Array)return a;if(a.constructor==Date||a.constructor==RegExp||a.constructor==Function||a.constructor==String||a.constructor==Number||a.constructor==Boolean)return new a.constructor(a);b=b||new a.constructor;for(var c in a)b[c]="undefined"==typeof b[c]?this.clone(a[c],null):b[c];return b},a.PluginGencat=b}b()}(window);