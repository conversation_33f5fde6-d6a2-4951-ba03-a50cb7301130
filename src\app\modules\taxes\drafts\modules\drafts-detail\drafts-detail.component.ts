import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { 
	DraftsRequest, 
	IDrafts, 
	DRAFTS_DETAIL_STORAGE, 
	CanviEstat, 
	States
} from '../../models/drafts.model';
import { AppRoutes, DraftsRoutes, TaxesRoutes } from 'src/app/core/models/config.model';
import { DETAIL_CARD_FORM_DATA, DETAIL_TABS_MODELS, DETAIL_TAB_DATA, DeleteModalData, IDetailData } from './models/drafts-detail.model';
import { takeUntil } from 'rxjs/operators';
import { DraftsDetailEndpointsService } from './services/drafts-detail-endpoint.service';
import { DraftsService } from '../../services/drafts.service';
import { DraftsEndpointsService } from '../../services/drafts-endpoints.service';
import { DetailCardData } from '../../../../shared/detail-card';
import { StateChangesTableRow } from '../../../../shared/detail-state-table';
import { TabData } from '../../../../shared/detail-tabs';
import { CopyDraftResponse, ResponseDeleteAutoliquidacio } from './models/drafts-detail-endpoint.model';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { AlertService } from 'src/app/core/services/alert.service';

@Component({
	selector: 'app-drafts-detail',
	templateUrl: './drafts-detail.component.html'
})
export class DraftsDetailComponent implements OnInit {

	// Procedure Data
	draft: IDrafts;
	idDraft: string;
	idReceipt: string;

	// Detail data
	stateChanges: StateChangesTableRow[] = [];
	detailData: IDetailData;
	stateIcon: string;
	stateLabel: string;
	detailCardFields:DetailCardData[] = [];
	detailTabInput: TabData[] =[];
	
	// Navegation Data
	currentIndex:number;
	totalRows:number;	
	navigateBackUrl: string;
	
	// Validations models
	downloadButtonsModelList:string[] = ["600"];

	// Other
	showDeleteButton: boolean = true;
	deleteCopyAllowRoles: UserRoles[] = [UserRoles.ADMIN];

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		private draftsEndpointsService: DraftsEndpointsService,
		private detailEndpointsService: DraftsDetailEndpointsService,
		private router: Router,
		private aRoute: ActivatedRoute,
		private draftsService: DraftsService,
		private alertService: AlertService
	) {
		this.idDraft = this.aRoute.snapshot.params.id;
	}

	ngOnInit(): void {
		this.setConstantValues();

		this.setDetailData();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();

		this.draftsService.dataStorageService.deleteItem(DRAFTS_DETAIL_STORAGE);
	}
	
	private setConstantValues() {
		this.detailCardFields = DETAIL_CARD_FORM_DATA;
		this.navigateBackUrl = '/'+AppRoutes.TAXES.concat('/',TaxesRoutes.DRAFTS,'/',DraftsRoutes.TABLE);
	}

	private setDetailData(){
		this.detailData = this.draftsService.dataStorageService.getItem(DRAFTS_DETAIL_STORAGE) as IDetailData;

		if (this.detailData && this.idDraft === this.detailData.idSelfAssessment) {
			this.currentIndex = this.detailData.index
			this.totalRows = this.detailData.totalRows
		} else {
			this.draftsService.dataStorageService.deleteItem(DRAFTS_DETAIL_STORAGE);
		}
	}

	getSelfAssessment() {
		this.detailEndpointsService.getDraft(this.idDraft)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				if (response?.content){
					this.draft = response.content;
		
					this.setCoreValues(this.draft);
				}
			});
	}
	
	private setCoreValues(data: IDrafts) {
		this.idReceipt = data.id;
		this.idDraft = data.id;
		
		this.stateChanges = this.setStateChanges(data?.canviEstat);

		this.stateIcon = data.estat ? this.draftsService.getStateIcon(data.estat,'detail') : '';
		this.stateLabel = data.estat ? `MODULE_TRACKING.MODULE_DRAFTS.STATES.${data.estat}` : null;

		this.setTabsToDisplay(data);
	}

	private setStateChanges(stateChanges: CanviEstat[]): StateChangesTableRow[] {
		return stateChanges.map(state => {
			return {
				before: state.estatAnterior,
				current: state.estat,
				dateChange: state.dataCanvi,
				currentStateIcon: this.draftsService.getStateIcon(state.estat,'detail'),
				previousStateIcon: this.draftsService.getStateIcon(state.estatAnterior,'detail'),
			}
		}) || [];
	}

	private setTabsToDisplay(data: IDrafts) {
		this.detailTabInput = DETAIL_TAB_DATA.filter(
			tabData => {
				if(DETAIL_TABS_MODELS[tabData.tabType]) {
					return DETAIL_TABS_MODELS[tabData.tabType]?.includes(data.model);
				}
				
				return true;
			}  
		);
	}

	getSelfAssessmentList() {
		this.detailData.options.rows = 1;
		this.detailData.options.first = this.currentIndex;
		this.detailData.index = this.currentIndex;

		const request: DraftsRequest = new DraftsRequest(this.detailData.filterValue,this.detailData.options);

		this.draftsEndpointsService.getDraftsList(request)
			.pipe(takeUntil(this._unsubscribe)).subscribe(response => {
				if (response?.content?.results?.length > 0) {
					this.draft =  response?.content?.results[0];
					this.detailData.idSelfAssessment = this.draft?.id;
		
					this.setCoreValues(this.draft);
		
					this.draftsService.dataStorageService.setItem(DRAFTS_DETAIL_STORAGE,this.detailData);
		
					this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.DRAFTS,'/',DraftsRoutes.DETAIL), this.idDraft]);
				}
		});
	}
	
	onShowErrors() {
		this.draftsService.showErrors(this.draft?.errors)
	}

	deleteModal() {
		if (this.draftsService.authService.getSessionStorageUser()?.rol !== UserRoles.ADMIN) return;

		const deleteData:DeleteModalData = {
			idDraft: this.idDraft,
			isCopy: this.draft.indCopia,
			confirmFn: this.deleteSelfAssessment.bind(this)
		}

		this.draftsService.deleteModal(deleteData);
	}

	// Función que borra las autoliquidaciones
	private deleteSelfAssessment() {
		this.detailEndpointsService.deleteDraft(this.idDraft)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response: ResponseDeleteAutoliquidacio) => {
				if(response?.content) {
					this.alertService.setAlert({
						severity: 'success',
						title: 'MODULE_TRACKING.MODULE_DRAFTS.ALERT.DELETE_TITLE',
					});
					this.navigateBackwards()
				}
			})
	}

	navigateBackwards = () => this.router.navigate([this.navigateBackUrl]);

	copyDraft() {
		if(this.draft.indCopia) {
			this.draftsService.openCopyModal()
		} else {
			this.detailEndpointsService.copyDraft(this.draft.id)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response: CopyDraftResponse) => {
				if(response?.content) {
					this.alertService.setAlert({
						severity: 'success',
						title: 'MODULE_TRACKING.MODULE_DRAFTS.ALERT.COPY_TITLE',
					});
					this.navigateBackwards()
				} 
			})
		}
	}
}
