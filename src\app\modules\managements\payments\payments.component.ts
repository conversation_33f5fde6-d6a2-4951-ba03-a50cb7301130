import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { WcComponentInput } from 'pt-ui-components-mf-lib';
import { ManagementRoutes } from 'src/app/core/models/config.model';

@Component({
  selector: 'app-payments',
  templateUrl: './payments.component.html'
})
export class PaymentsComponent implements OnInit {

  pagamentsWcInput: WcComponentInput;

  constructor(private router: Router) {
    //
  }

  ngOnInit(): void {
    this.pagamentsWcInput = {
			module: null,
			component: 'payment-tracking',
			service: null,
		}
  }


  goToDetail(ev: any) {
    this.router.navigate([ManagementRoutes.DETAIL, ev.detail.componentEvent.id])
  }

}
