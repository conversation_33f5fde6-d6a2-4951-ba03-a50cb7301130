import { Injectable } from '@angular/core';
import { ConfFilters, ConfSateEnum } from '../models/maintenance-conf.model';
import { StatesIcons } from 'src/app/core/models/config.model';

@Injectable({
	providedIn: 'root'
})
export class MaintenanceConfService {

	resetMaintenanceFilterData(): ConfFilters {
		const inicioDate = new Date();
		inicioDate.setFullYear(inicioDate.getFullYear()-2);
		const finDate = new Date();
		finDate.setFullYear(finDate.getFullYear()+2);

		return {
			aplicacio: null,
			mantenimentInici: null,
			mantenimentFinal: null,
		};
	}

	getStateIcon (state: ConfSateEnum) :string {
		switch (state) {
			case ConfSateEnum.ACTIVE:
				return StatesIcons.SUCCESS;
			case ConfSateEnum.IN_MAINTENANCE:
				return StatesIcons.EXCLAMATION_WARNING;
			case ConfSateEnum.SCHEDULED_MAINTENANCE:
				return StatesIcons.WAITING_WARNING;
			default:
				return '';
		}
	}
}
