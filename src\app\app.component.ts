import { Component } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrimeNGConfig } from 'primeng/api';
import {
	PtAuthService,
	PtLoginResponse,
	PtLoginService,
	PtMessageService,
	PtPageLayoutService,
} from 'pt-ui-components-mf-lib';
import { ValidateSessionTokenService } from './core/services/validate-session-token.service';

@Component({
	selector: 'app-root',
	templateUrl: './app.component.html',
	styleUrls: ['./app.component.sass'],
})
export class AppComponent {
	dataLoaded: Promise<boolean>;

	constructor(
		private authService: PtAuthService,
		private validateSessionService: ValidateSessionTokenService,
		private translateService: TranslateService,
		private configPrimeNG: PrimeNGConfig,
		public pageLayoutService: PtPageLayoutService,
		public msgService: PtMessageService,
		private loginService: PtLoginService
	) //private idleService: ModalInactivityService
	{
		console.log('Webcomponent: Tracking > constructor');

		// Set primeNG specific tranlsations
		this.translateService
			.get('UI_COMPONENTS.PRIME_NG')
			.subscribe((res) => this.configPrimeNG.setTranslation(res));

		// Listen to the route changes (updates page layout)
		this.pageLayoutService.listenNavigation();

		// Secured
		if (
			window.location.href.includes('/secured') 
			&& !this.validateSessionService.isValidToken()
		) {
			this.authService.deleteSession();
			this.secured();
		} else this.dataLoaded = Promise.resolve(true);
	}

	async secured( ) {
		const response = await this.loginService.login(true)
		if (response?.content?.tokenJwt) {
			this.dataLoaded = Promise.resolve(true);
		}
	}
}
