body {
	overflow-x: visible;
	font-size: 14px;
}
#hTContainer {
	font-size: 14px;
}
#hTContainer .z-index-menu {
	z-index: 2000;
}
/* Search icon */
header #inputSearch,
header .buttonSearch,
header .headerCenter .blockRight,
footer {
	line-height: normal;
}
/* Search icon */
header .searchMobil form .buttonSearch {
	font-size: 16px
}
/* Navbar menu */
#navigation .dropdown-toggle:after {
	display: none;
}
#navigation .dropdown-toggle {
	white-space: normal;
}
header .navigationMenu .container ul li .closeSubMenu {
	cursor: pointer;
}
@media (min-width: 1200px) {
	#hTContainer .container,
	footer .container {
		width: 1170px;
		max-width: 1170px;
	}
}
/* Mobile */
@media (max-width: 47.938em) {
	.collapse:not(.show).navbar-collapse.navbar-ex1-collapse,
	header .headerTop .navigationMenuMobile .container,
	footer .mapsSite .logoBottom .row {
		display: block;
	}
	footer .blockLast .socialMedia {
		width: auto;
	}
}