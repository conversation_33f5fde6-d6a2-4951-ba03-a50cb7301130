import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { DataStorageService, WebComponentsService } from 'pt-ui-components-mf-lib';
import { AppRoutes, PT_USER, UserRoutes } from 'src/app/core/models/config.model';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-dispatcher',
	templateUrl: './dispatcher.component.html',
	styleUrls: ['./dispatcher.component.sass']
})
export class TrackingDispatcherComponent implements OnInit {

	// Webcomponent > URLs
	wcDispatcherUrlCss = environment.wcUrlDispatcherCss;

	constructor(
		private webComponentService: WebComponentsService,
		private dataStorageService: DataStorageService,
		private router: Router
	) {
		console.log('Tracking dispatcher component');
		
		this.webComponentService.setWebComponentStyle(this.wcDispatcherUrlCss, 'dispatcher');
	}

	ngOnInit(): void {
		if(!this.dataStorageService.getItem(PT_USER)) {
			this.router.navigate(['/' + AppRoutes.USERS.concat('/',UserRoutes.SIMULATED_LOGIN)]);
		} 
	}
}
