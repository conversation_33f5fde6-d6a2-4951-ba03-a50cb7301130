apiVersion: v1
kind: ConfigMap
metadata:
  name: pt-portal-tracking-gw-config
  labels:
    name: pt-portal-tracking-gw
    app: pt-app
    type: gateway 
data:
  idp-metadata: |
    <md:EntityDescriptor xmlns="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:md="urn:oasis:names:tc:SAML:2.0:metadata" xmlns:saml="urn:oasis:names:tc:SAML:2.0:assertion" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" entityID="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad">
    <md:IDPSSODescriptor WantAuthnRequestsSigned="true" protocolSupportEnumeration="urn:oasis:names:tc:SAML:2.0:protocol">
    <md:KeyDescriptor use="signing">
    <ds:KeyInfo>
    <ds:KeyName>JRdfyM32baXDSBOmq6yD_FlYl1U3FZj0YYEbjeZsuuo</ds:KeyName>
    <ds:X509Data>
    <ds:X509Certificate>MIIDSTCCAjGgAwIBAgIIQPnTIE699KcwDQYJKoZIhvcNAQELBQAwFzEVMBMGA1UEAxMMR0lDQVItQ0EyMDIwMCAXDTI0MTIxMjEwMjQwMFoYDzIwNTQxMjEyMTAyNDAwWjATMREwDwYDVQQDEwhnaWNhci1hZDCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAOUuhdXER17tXNj7vHfAZDKBpaXveQrXlMw+ERSjIUrJE3psE2XFjAqv9kmGBnHTNbnArDqaodSSLVDkDraNAFfCWSWUebCBYAn2tFZARb+LsiURAoX8IY9gm8TM0qVyyg+qyy1R2m6Y+0nx5ZAhHDfPa8QbK+2xqAgEi2OOaEpgIR8yQYsSDVBD37YP1IlFM0rBsa3T53pyKVBKvfzF7IE53xhfvcVKW7YTdLProfAeQ7ivixhiRlDEM2zhNm6wEsxjPXZawCeO/9ofZ0NCR4lfYei3KfiG+riNqp2/1aZn8Lw6dTfREzXGXZANwcZ3L7fzVAWHli3sC3B8Eg5j2KMCAwEAAaOBmjCBlzAMBgNVHRMBAf8EAjAAMB0GA1UdDgQWBBT0AZ7dBeVm0A+/ku8x1kse1IWT1jALBgNVHQ8EBAMCA+gwEwYDVR0lBAwwCgYIKwYBBQUHAwEwEwYDVR0RBAwwCoIIZ2ljYXItYWQwEQYJYIZIAYb4QgEBBAQDAgZAMB4GCWCGSAGG+EIBDQQRFg94Y2EgY2VydGlmaWNhdGUwDQYJKoZIhvcNAQELBQADggEBAHrylTtN7deMU7ZO240Sh+j5SMGp99h6LelcBILvIqlEaqXjcZwU6M0QL/NitYme0Vx+DCVGSUMcs921gDGAVBI64JDKgv2jVim0yCRXw9Y0DKhr3GQtPLqLvkip4C4tKfM5xIcOhusBxZ82i2M+74Os4j32z8LbJO4P8gnW783mKGt2cLRwgeqImqu8FGfExXNiyVEHGbKkjGD6jXc0v/8QCd3LiHEDMyzhLoScsDe96BiwPhIPHKCLcDMLX0Cx4CUz+JKAmxgdpdkWYbdJxFb3T2xBpDcOo4bfBaDpUAR46UXUExaE8fGCXx5iL4zmSbyY8LlR9+HfeGYtqxSpIIU=</ds:X509Certificate>
    </ds:X509Data>
    </ds:KeyInfo>
    </md:KeyDescriptor>
    <md:ArtifactResolutionService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml/resolve" index="0"/>
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleLogoutService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:persistent</md:NameIDFormat>
    <md:NameIDFormat>urn:oasis:names:tc:SAML:2.0:nameid-format:transient</md:NameIDFormat>
    <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:unspecified</md:NameIDFormat>
    <md:NameIDFormat>urn:oasis:names:tc:SAML:1.1:nameid-format:emailAddress</md:NameIDFormat>
    <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-POST" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Redirect" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:SOAP" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    <md:SingleSignOnService Binding="urn:oasis:names:tc:SAML:2.0:bindings:HTTP-Artifact" Location="https://preproduccio.autenticaciogicar5.extranet.gencat.cat/realms/gicar-ad/protocol/saml"/>
    </md:IDPSSODescriptor>
    </md:EntityDescriptor>
