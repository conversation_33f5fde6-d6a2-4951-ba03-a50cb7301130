import { PtHttpResponse } from "pt-ui-components-mf-lib";
import { AvisoEnum, NotificationTypeEnum } from "./notifications.model";
import { LazyLoadEvent } from "primeng/api";

export interface NotificationBase {
	tituloCa?: string;
	tituloEs?: string;
	tipo?: NotificationTypeEnum;
	contexto?: AvisoEnum;
	activo?: boolean;
}

export interface NotificationFilters extends NotificationBase {
	inicio?: number;
	fin?: number;
}

export class NotificationRequest {
	filter: Partial<NotificationFilters>;
	options: LazyLoadEvent;

	constructor(params: Partial<NotificationFilters>, options: LazyLoadEvent) {
		this.filter = {
			tituloCa: params?.tituloCa?.replace(/<[^>]*>/g, '') || undefined,
			tituloEs: params?.tituloEs?.replace(/<[^>]*>/g, '') || undefined,
			tipo: params?.tipo || undefined,
			contexto: params?.contexto || undefined,
			inicio: params?.inicio || undefined,
			fin: params?.fin || undefined,
			activo: params?.activo || undefined,
		}
		this.options = options
	}
}

export class NewNotificationRequest {
	tituloCa?: string;
	tituloEs?: string;
	tipo?: NotificationTypeEnum;
	contexto?: AvisoEnum;
	activo?: boolean;
	mensajeCa?: string;
	mensajeEs?: string;
	inicioNum?: number;
	finNum?: number;

	constructor(params: Partial<NotificationResponseContent>) {
		this.tituloCa = params?.tituloCa || undefined;
		this.tituloEs = params?.tituloEs || undefined;
		this.mensajeCa = params?.mensajeCa || undefined;
		this.mensajeEs = params?.mensajeEs || undefined;
		this.tipo = params?.tipo || undefined;
		this.contexto = params?.contexto || undefined;
		this.inicioNum = params?.inicio || undefined;
		this.finNum = params?.fin || undefined;
		this.activo = params?.activo;
	}
}

export interface NotificationResponseContent extends NotificationFilters {
	id?: string
	mensajeCa?: string;
	mensajeEs?: string;
}

export interface NotificationListResponse extends PtHttpResponse {
	content: {
		results: NotificationResponseContent[],
		total: number,
		size: number
	}
}

export interface NotificationResponse extends PtHttpResponse {
	content: NotificationResponseContent
}

export interface DeleteNotificationResponse extends PtHttpResponse {
	content: boolean
}