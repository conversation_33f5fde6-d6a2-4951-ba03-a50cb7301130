import { WcComponentInput } from "pt-ui-components-mf-lib";

export enum TabType {
  documents = 'llistat-documents',
  payments = 'payment-detail',
  validations = 'validations',
  grouping = 'grouping',
  notifications = 'notifications',
  presentations = 'presentations',
  procedures = 'tramitacions',
  custom = 'custom'
}

export interface DetailTabsData {
	idSelfAssessment:string;
	idTramitacio: string;
  tabs: TabData[];
}

export interface TabData {
  tabType: TabType;
  tabTitle: string; 
  tabRetryButtonClass?: string;
  tabCustomTemplate?: string;
}

export interface TabInput extends TabData {
  tabInput?: WcComponentInput;
}
