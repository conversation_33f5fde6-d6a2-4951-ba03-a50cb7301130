import {
	RequestDeleteSelfAssessment,
	ResponseDeleteAutoliquidacio,
	ResponseGetAutoliquidacio
} from './models/detail-endpoint.model';
import {Component, OnD<PERSON>roy, OnInit} from '@angular/core';
import {
	Card2Mode,
	iCommonError,
	PtTableColumn,
	PtTableSelection,
	UserRoles,
	WcComponentInput
} from 'pt-ui-components-mf-lib';
import {FormBuilder, FormGroup} from '@angular/forms';
import {takeUntil} from 'rxjs/operators';
import {Subject} from 'rxjs';
import {
	AutoliquidacioRequest,
	DeleteModalData,
	IAutoliquidacio,
	IDetailData,
	StateChangesTableRow,
	States
} from '../../models/autoliquidacions-v1.model';
import {ActivatedRoute, Router} from '@angular/router';
import { DetailEndpointsService } from './services/detail-endpoint.service';
import { AutoliquidacionsV1EndpointsService } from '../../services/autoliquidacions-v1-endpoints.service';
import { AutoliquidacionsV1Service } from '../../services/autoliquidacions-v1.service';
import { AppRoutes, SelfAssessmentV1Routes, TaxesRoutes } from 'src/app/core/models/config.model';
import { DetailCardData } from 'src/app/modules/shared/detail-card';
import { TabData } from 'src/app/modules/shared/detail-tabs';
import { DETAIL_CARD_FORM_DATA, DETAIL_TABS_MODELS, DETAIL_TAB_DATA, DetailCardFields } from './models/detail.model';
import { DownloadButtonsData } from 'src/app/modules/shared/detail-page';

@Component({
	selector: 'app-detail',
	templateUrl: './detail.component.html'
})

export class DetailComponent implements OnInit, OnDestroy {

	// Procedure Data
	selfAssessment: IAutoliquidacio;
	idSelfAssessment: string;
	idReceipt: string;
	idTramitacio: string;

	// Detail data
	stateChangesRows: StateChangesTableRow[] = [];
	detailData: IDetailData;
	stateIcon: string;
	stateLabel: string;
	detailCardFields:DetailCardData[] = [];
	detailTabInput: TabData[] =[];

	// Navigation Data 
	currentIndex:number;
	totalRows:number;	
	navigateBackUrl: string; 

	// Validations models
	showButtonsInState:string[] = ["600"];

	// Other
	showDeleteButton: boolean = true;
	deleteAllowRoles: UserRoles[] = [UserRoles.ADMIN];
	states: string[] = [States.PAID, States.PRESENTED, States.PROCESSED, States.PROCESSING_ERROR];
	modelType:number;

	downloadButtonsData: DownloadButtonsData;
	showPresentationButtons:boolean;

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
		private autoliquidacionsEndpointsService: AutoliquidacionsV1EndpointsService,
		private detailEndpointsService: DetailEndpointsService,
		private router: Router,
		private aRoute: ActivatedRoute,
		private autoliquidacioService: AutoliquidacionsV1Service
	) {
		this.idSelfAssessment = this.aRoute.snapshot.params.id;
	}

	ngOnInit(): void {
		this.setConstantValues();

		this.setDetailData();
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();

		this.autoliquidacioService.dataStorageService.deleteItem('detailData');
	}

	private setConstantValues() {
		this.navigateBackUrl = '/'+AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V1,'/',SelfAssessmentV1Routes.TABLE);
	}
	
	private setDetailData(){
		this.detailData = this.autoliquidacioService.dataStorageService.getItem('detailData') as IDetailData;

		if (this.detailData && this.idSelfAssessment === this.detailData.idSelfAssessment) {
			this.currentIndex = this.detailData.index
			this.totalRows = this.detailData.totalRows
		} else {
			this.autoliquidacioService.dataStorageService.deleteItem('detailData');
		}
	}

	getSelfAssessment() {
		this.detailEndpointsService.getSelfAssessment(this.idSelfAssessment).pipe(takeUntil(this._unsubscribe)).subscribe(response => {
			if (response?.content){
				this.selfAssessment = response.content;

				this.setUpPropertyValues();
			}
		});
	}

	private setUpPropertyValues(){
		this.setCoreValues(this.selfAssessment);

		this.setTabsToDisplay(this.selfAssessment);
	}

	private setCoreValues(data: IAutoliquidacio) {
		this.idReceipt = data?.idReceipt;
		this.idTramitacio = data?.idTramitacio;
		this.idSelfAssessment = data?.idSelfAssessment;

		this.modelType = Number(data.model);
		
		this.detailCardFields = this.setDetailCardFieldsValue(data, this.modelType);
		
		this.stateChangesRows = this.setStateChanges(this.selfAssessment?.stateChanges);

		this.downloadButtonsData = {
			isDisabled: this.states.includes(data?.state),
			idEntity: data?.idDocument
		}
		
		this.showPresentationButtons = this.showButtonsInState.includes(data?.model);

		this.stateIcon = this.autoliquidacioService.getStateIcon(data?.state,'detail');
		this.stateLabel = data?.state ? this.autoliquidacioService.translateService.instant(`MODULE_TRACKING.STATES.${data?.state}`) : '';
	}

	private setDetailCardFieldsValue(data: IAutoliquidacio, modelType: number): DetailCardData[] {
		return DETAIL_CARD_FORM_DATA.filter(fieldData => {
			return (
				(fieldData.id !== DetailCardFields.idTramitacio &&
					fieldData.id !== DetailCardFields.notarialFileReference) ||
				(fieldData.id === DetailCardFields.idTramitacio &&
					data?.idTramitacio) ||
				(fieldData.id === DetailCardFields.notarialFileReference &&
					modelType === 600)
			);
		});
	}

	private setStateChanges(stateChanges: StateChangesTableRow[]): StateChangesTableRow[] {
		return stateChanges?.map(state => {
			return {
				...state,
				currentStateIcon: this.autoliquidacioService.getStateIcon(state.current,'detail'),
				previousStateIcon: this.autoliquidacioService.getStateIcon(state.before,'detail'),
			}
		}) || [];
	}

	private setTabsToDisplay(data: IAutoliquidacio) {
		this.detailTabInput = DETAIL_TAB_DATA.filter(
			tabData => DETAIL_TABS_MODELS[tabData.tabType].includes(data.model)
		);
	}

	getSelfAssessmentList() {
		this.detailData.options.rows = 1;
		this.detailData.options.first = this.currentIndex;
		this.detailData.index = this.currentIndex;

		const request: AutoliquidacioRequest = new AutoliquidacioRequest(this.detailData.filterValue,this.detailData.options);

		this.autoliquidacionsEndpointsService.getSelfAssessmentList(request).pipe(takeUntil(this._unsubscribe)).subscribe(response => {
			if (response?.content?.results?.length > 0) {
				this.selfAssessment =  response?.content?.results[0];
				this.detailData.idSelfAssessment = this.selfAssessment?.idSelfAssessment;

				this.setUpPropertyValues();
	
				this.autoliquidacioService.dataStorageService.setItem("detailData",this.detailData);
	
				this.router.navigate(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V1,'/',SelfAssessmentV1Routes.DETAIL), this.idSelfAssessment]);
			}
		});
	}

	onShowErrors = () => this.autoliquidacioService.showErrors(this.selfAssessment.errors || []);

	deleteModal() {
		if (this.autoliquidacioService.authService.getSessionStorageUser()?.rol !== UserRoles.ADMIN) return;

		const deleteData:DeleteModalData = {
			idReceipt:this.idReceipt,
			idSelfAssessment: this.idSelfAssessment,
			state: this.selfAssessment.state,
			confirmFn: this.deleteSelfAssessment.bind(this)
		}

		this.autoliquidacioService.deleteModal(deleteData);
	}

	// Función que borra las autoliquidaciones
	private deleteSelfAssessment() {
		const request:RequestDeleteSelfAssessment = {
			idReceipt: this.idReceipt,
			idSelfAssessment: this.idSelfAssessment
		};

		this.detailEndpointsService.deleteAssessment(request)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response: ResponseDeleteAutoliquidacio) => {
				if(response?.content) this.navigateBackwards()
			})
	}

	navigateBackwards = () => this.router.navigate([this.navigateBackUrl]);
}

