<!--Pestañas del detalle-->
<ng-container *ngIf="tabsInput?.length">
	<p-tabView>
		<p-tabPanel
			*ngFor="let tab of tabsInput; first as isFirst"
			[header]="tab.tabTitle | translate"
			[selected]="isFirst"
		>
			<ng-template pTemplate="content">
				<ng-container
					*ngTemplateOutlet="
						tabsTemplate,
						context: { 
							tabType: tab.tabType, 
							tabInput: tab?.tabInput, 
							tabCustomTemplate: customTabsContent[tab?.tabCustomTemplate]
						}
					"
				>
				</ng-container>
			</ng-template>
		</p-tabPanel>
	</p-tabView>
</ng-container>

<ng-template
	#tabsTemplate
	let-tabType="tabType"
	let-tabInput="tabInput"
	let-tabCustomTemplate="tabCustomTemplate"
>
	<ng-container *ngIf="tabType === TabType.documents">
		<mf-documents *axLazyElement [input]="tabInput"></mf-documents>
	</ng-container>

	<ng-container *ngIf="tabType === TabType.payments">
		<mf-pagaments *axLazyElement [input]="tabInput"></mf-pagaments>
	</ng-container>

	<ng-container
		*ngIf="
			tabType === TabType.validations ||
			tabType === TabType.grouping ||
			tabType === TabType.notifications ||
			tabType === TabType.presentations ||
			tabType === TabType.procedures
		"
	>
		<mf-presentacions *axLazyElement [input]="tabInput"></mf-presentacions>
	</ng-container>

	<ng-container *ngIf="tabType === TabType.custom">
		<ng-container *ngTemplateOutlet="tabCustomTemplate"></ng-container>
	</ng-container>
</ng-template>
