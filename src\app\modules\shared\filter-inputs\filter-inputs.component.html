<ng-container [ngSwitch]="fieldData.fieldType">
  <pt-select
    *ngSwitchCase="FilterFieldType.select"
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
    [_options]="fieldData.options"
  >
  </pt-select>

  <pt-select-multiple
    *ngSwitchCase="FilterFieldType.multiSelect"
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
    [_options]="fieldData.options"
  >
  </pt-select-multiple>

  <pt-datepicker
    *ngSwitchCase="FilterFieldType.datePicker"
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
    [_yearNavigator]="true"
    [_yearRange]="yearRange"
    (_changeEvent)="changeRangeYear($event)"
  >
  </pt-datepicker>

  <pt-checkbox
    *ngSwitchCase="FilterFieldType.checkbox"
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
    [_options]="fieldData.options"
    [_binary]="true">
  </pt-checkbox>

  <pt-tri-checkbox
    *ngSwitchCase="FilterFieldType.triCheckbox"
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
    [_options]="fieldData.options">
  </pt-tri-checkbox>

  <pt-input-text
    *ngSwitchDefault
    [_id]="fieldData.id"
    [_label]="fieldData.label"
    [_formGroup]="formGroup"
    [_formControlName]="fieldData.id"
  >
  </pt-input-text>

	<ng-container *ngSwitchCase="FilterFieldType.keyValue">
		<div class="row" [formGroup]="fieldData.id">
			<label class="col-12">{{ fieldData.label | translate }}</label>
			<ng-container *ngFor="let fieldControl of getFieldKeysControl(fieldData); let i = index">
				<div class="col-12 col-md-6">
					<pt-input-text
						[_id]="fieldControl"
						[_formGroup]="formGroup.get(fieldData.id)"
						[_formControlName]="fieldControl"
					>
					</pt-input-text>
				</div>
			</ng-container>
		</div>
	</ng-container>
</ng-container>
