import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { UserRoutes } from 'src/app/core/models/config.model';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: UserRoutes.SIMULATED_LOGIN,
		loadChildren: () => import(`./login-simulat/login-simulat.module`).then(module => module.LoginSimulatModule),
		canActivate:[AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN,UserRoles.TECHNICIAN, UserRoles.INSPECTOR],
			isSimulatedLogin: true
		}
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class UsersRoutingModule { }
