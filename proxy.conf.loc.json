{"/api/pagaments/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/pagaments": ""}}, "/api/sarcat-middleware/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/sarcat-middleware", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/sarcat-middleware": ""}}, "/api/secured/pagaments/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/secured/pagaments", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/secured/pagaments": ""}}, "/api/tributs/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/tributs", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/tributs": ""}}, "/api/area-privada-config/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/area-privada-config", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/area-privada-config": ""}}, "/api/area-privada/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/area-privada", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/area-privada": ""}}, "/api/documents/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/documents", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/documents": ""}}, "/api/seguretat/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/seguretat", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/seguretat": ""}}, "/api/gestio-configuracio-tr/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/gestio-configuracio-tr", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/gestio-configuracio-tr": ""}}, "/api/dades-referencia/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/dades-referencia", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/dades-referencia": ""}}, "/mf/pt-seguretat-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-seguretat-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-seguretat-mf": ""}}, "/mf/pt-gestions-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-gestions-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-gestions-mf": ""}}, "/mf/pt-documents-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-documents-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-documents-mf": ""}}, "/mf/pt-pagaments-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-pagaments-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-pagaments-mf": ""}}, "/mf/pt-commons-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-commons-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-commons-mf": ""}}, "/mf/pt-el-meu-espai-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/pt-el-meu-espai-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/pt-el-meu-espai-mf": ""}}, "/mf/sarcat-middleware-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/sarcat-middleware-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/sarcat-middleware-mf": ""}}, "/mf/aeat-conector-mf/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/mf/aeat-conector-mf", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/mf/aeat-conector-mf": ""}}, "/api/aeat-conector/v1/*": {"target": "https://dev.admin-seu.atc.intranet.gencat.cat/api/aeat-conector/v1", "secure": false, "logLevel": "debug", "changeOrigin": true, "pathRewrite": {"^/api/aeat-conector/v1": ""}}}