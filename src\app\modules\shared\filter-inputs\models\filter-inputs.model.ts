import { ValidatorFn } from "@angular/forms";
import { PtSelectCardModel } from "pt-ui-components-mf-lib";

export interface FilterFieldsData {
  id: string;
  fieldType: FilterFieldType;
  containerClass?: string;
  label?: string;
  options?: PtSelectCardModel[];
  optionsValue?: [string, string][];
  optionsLabelTranslations?: string;
	numOptions?: number;
  validations?: ValidatorFn | ValidatorFn[];
}

export enum FilterFieldType {
  text = 'text',
  select = 'select',
  multiSelect = 'multiSelect',
  datePicker = 'datePicker',
  number = 'number',
  checkbox = 'checkbox',
  triCheckbox = 'triCheckbox',
	keyValue = 'keyValue',
}
