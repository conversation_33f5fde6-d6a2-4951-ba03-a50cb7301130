import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { ConfListResponse, ConfRequest, CreateConfRequest, UpdateConfRequest } from '../models/maintenance-conf-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class MaintenanceConfEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search notifications by filter
	getConfList(request: ConfRequest): Observable<ConfListResponse> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlGestioConfiguracio,
			url: `/llistat`,
			method: 'post',
			body: request
		};
		return this.httpService.post(httpRequest);
	}

	createConf(request: CreateConfRequest): Observable<CreateConfRequest> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlGestioConfiguracio,
			url: ``,
			method: 'post',
			body: request
		};
		return this.httpService.post(httpRequest);
	}

	updateConf(request: UpdateConfRequest[]): Observable<CreateConfRequest> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlGestioConfiguracio,
			url: ``,
			method: 'put',
			body: request
		};
		return this.httpService.put(httpRequest);
	}

	deleteConf(id: string): Observable<any> {
		const httpRequest: PtHttpRequest = {
			baseUrl: environment.baseUrlGestioConfiguracio,
			url: `${id}`,
			method: 'delete'
		};
		return this.httpService.delete(httpRequest);
	}
}
