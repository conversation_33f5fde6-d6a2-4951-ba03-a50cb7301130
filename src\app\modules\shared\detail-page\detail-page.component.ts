import {
	Component,
	EventEmitter,
	Input,
	OnInit,
	Output,
	TemplateRef,
} from '@angular/core';
import { StateChangesTableRow } from '../detail-state-table';
import { DetailCardData } from '../detail-card';
import { TabData } from '../detail-tabs';
import { NavigationExtras, Router } from '@angular/router';
import {
	UserRoles,
	WcComponentInput,
	WebComponentsService,
} from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { DownloadButtonsData } from './models/detail-page.model';

@Component({
	selector: 'app-detail-page',
	templateUrl: './detail-page.component.html',
})
export class DetailPageComponent implements OnInit {
	// Translations data
	@Input() pageTitle: string = '';
	@Input() statesTableTranslation: string = '';
	@Input() deleteButtonLabel: string = '';
	@Input() detailCardTitle: string = '';

	// Procedure Data
	@Input() procedureData: any;
	@Input() idEntity: string;
	@Input() idTramit: string;
	@Input() idReceipt: string;

	// Detail data
	@Input() stateChangeFirstDate: Date;
	@Input() stateChanges: StateChangesTableRow[] = [];
	@Input() stateIcon: string;
	@Input() stateLabel: string;
	@Input() detailCardFields: DetailCardData[] = [];
	@Input() detailTabInput: TabData[] = [];
	@Input() customTabsContent: { [key: string]: TemplateRef<any> } = {};

	// Navegation Data
	@Input() currentIndex: number;
	@Input() totalRows: number;
	@Input() backUrl: string;
	@Input() navigationExtras: NavigationExtras;

	// Buttons data
	@Input() customButtonsTemplate: TemplateRef<any>;
	@Input() showPresentationButtons: boolean;
	@Input() set downloadButtonsData(value: DownloadButtonsData) {
		this.donwloadDocumentsWcInput = {
			...this.donwloadDocumentsWcInput,
			data: value,
		};
	}

	@Input() showDeleteButton: boolean;
	@Input() deleteAllowRoles: UserRoles[] = [];

	@Output() getProcedureList: EventEmitter<void> = new EventEmitter();
	@Output() getProcedure: EventEmitter<void> = new EventEmitter();
	@Output() incrementIndex: EventEmitter<void> = new EventEmitter();
	@Output() decrementIndex: EventEmitter<void> = new EventEmitter();
	@Output() showErrors: EventEmitter<any> = new EventEmitter();
	@Output() showDeleteModal: EventEmitter<any> = new EventEmitter();
	@Output() linkClicked: EventEmitter<string> = new EventEmitter();

	wcPresentacionsUrlCss = environment.wcUrlPresentacionsCss;

	donwloadDocumentsWcInput: WcComponentInput = {
		module: null,
		component: 'download-documents',
		data: {
			idEntity: '',
			isDisabled: false,
		},
	};

	constructor(
		private router: Router,
		private webComponentService: WebComponentsService
	) {
		this.webComponentService.setWebComponentStyle(
			this.wcPresentacionsUrlCss,
			'presentacions'
		);
	}

	ngOnInit(): void {
		this.emitProcedureEvent();
	}

	private emitProcedureEvent() {
		/* Condición (if) comentada para que haga la primera carga del detalle
			mediante el id del detalle, no mediante la posición en la lista. */

		// if (this.currentIndex >=0 && this.totalRows >=0) {
		// 	this.getProcedureList.emit();
		// } else {
		this.getProcedure.emit();
		// }
	}

	onShowErrors($event: any) {
		this.showErrors.emit($event);
	}

	onLinkClicked($event: string) {
		this.linkClicked.emit($event);
	}

	//Función de cambio de autoliquidación (anterior)
	goPreviousSelfAssessment() {
		this.decrementIndex.emit();

		this.getProcedureList.emit();
	}

	//Función de cambio de autoliquidación (siguiente)
	goNextSelfAssessment() {
		this.incrementIndex.emit();

		this.getProcedureList.emit();
	}

	deleteModal() {
		this.showDeleteModal.emit();
	}

	navigateBackwards = () => this.router.navigate([this.backUrl],this.navigationExtras);
}
