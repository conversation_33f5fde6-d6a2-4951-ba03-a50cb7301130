import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
	PtComponentEventOutput,
	PtMessageI,
	PtSelectCardModel,
	PtValidations,
} from 'pt-ui-components-mf-lib';
import {
	ConfContextEnum,
	ConfModalInput,
	ConfModalModeEnum,
	ConfRow,
} from '../../models/maintenance-conf.model';

@Component({
	selector: 'app-modal-maintenance-conf',
	templateUrl: './modal-maintenance-conf.component.html',
	styleUrls: ['./modal-maintenance-conf.component.sass'],
})
export class ModalMaintenanceConfComponent implements OnInit {
	@Input() modalInput: ConfModalInput;

	@Output() modalOutput: EventEmitter<ConfRow> = new EventEmitter<ConfRow>();

	contextOptions: PtSelectCardModel[] = [];

	// Filter
	maintenanceForm: FormGroup;

	// Other
	confirmationData: PtMessageI;
	yearRange: string;
	maintenanceEnum = ConfModalModeEnum;
	minDate: Date;

	constructor(
		private fb: FormBuilder,
		public activeModal: NgbActiveModal,
		private translateService: TranslateService
	) {}

	ngOnInit(): void {
		this.setUpModalData();

		this.setContextOptions();

		this._setForm();

		this.setMinDate();
		this.setYearRange();
	}

	private setUpModalData(): void {
		this.confirmationData = {
			severity: null,
			title: null,
			closableFn: this.closeModal.bind(this),
		};
	}

	private setContextOptions = (): void => {
		const statesList = Object.keys(ConfContextEnum);
		statesList.forEach((state) =>
			this.contextOptions.push({
				id: state,
				label: this.translateService.instant(
					`MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.CONTEXT_OPTIONS.${state}`
				),
			})
		);
	};

	private _setForm = (): void => {
		const maintenanceInput = this.modalInput?.maintenance as ConfRow;
		const currentDateValidation =
			this.modalInput.mode !== ConfModalModeEnum.FILTER
				? PtValidations.equalsOrGreaterThanCurrentDate('')
				: null;
		const dateValitadion =
			maintenanceInput?.mantenimentInici ||
			maintenanceInput?.mantenimentFinal
				? [Validators.required, currentDateValidation].filter(Boolean)
				: currentDateValidation;

		this.maintenanceForm = this.fb.group(
			{
				aplicacio: [maintenanceInput?.aplicacio],
				mantenimentInici: [
					maintenanceInput?.mantenimentInici &&
						new Date(maintenanceInput?.mantenimentInici),
					dateValitadion,
				],
				mantenimentFinal: [
					maintenanceInput?.mantenimentFinal &&
						new Date(maintenanceInput?.mantenimentFinal),
					dateValitadion,
				],
			},
			{
				validators: [
					PtValidations.lessThan(
						'mantenimentFinal',
						'mantenimentInici',
						'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_MODAL.VALIDATIONS.LESS_EQUAL'
					),
					PtValidations.greaterThan(
						'mantenimentInici',
						'mantenimentFinal',
						'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_NOTIFICATIONS.NOTIFICATION_MODAL.VALIDATIONS.GREATER_EQUAL'
					),
				],
			}
		);
	};

	private setMinDate() {
		this.minDate =
			this.modalInput.mode !== ConfModalModeEnum.FILTER
				? new Date()
				: null;
	}

	setYearRange(event?: PtComponentEventOutput) {
		const rangeDate = new Date();
		this.yearRange = `${rangeDate.getFullYear() - 11}:${
			rangeDate.getFullYear() + 9
		}`;
	}

	onChange(event?: PtComponentEventOutput) {
		const currentDateValidation =
			this.modalInput.mode !== ConfModalModeEnum.FILTER
				? [
						Validators.required,
						PtValidations.equalsOrGreaterThanCurrentDate(''),
				  ]
				: Validators.required;

		if (event?.componentValue) {
			this.maintenanceForm
				.get('mantenimentInici')
				.setValidators(currentDateValidation);
			this.maintenanceForm
				.get('mantenimentFinal')
				.setValidators(currentDateValidation);
		}
	}

	onInputEvent() {
		if (
			!this.maintenanceForm.get('mantenimentInici').value &&
			!this.maintenanceForm.get('mantenimentFinal').value
		) {
			this.clearFilter();
		}
	}

	clearFilter = () => {
		this.maintenanceForm.reset();
		this.maintenanceForm.get('mantenimentInici').setErrors(null);
		this.maintenanceForm.get('mantenimentInici').setValidators(null);
		this.maintenanceForm.get('mantenimentFinal').setErrors(null);
		this.maintenanceForm.get('mantenimentFinal').setValidators(null);
		this.maintenanceForm.updateValueAndValidity();
	};

	submit() {
		const startDate = new Date(
			this.maintenanceForm.value.mantenimentInici
		).setSeconds(0);
		const endDate = new Date(
			this.maintenanceForm.value.mantenimentFinal
		).setSeconds(0);
		const output = {
			...this.maintenanceForm.value,
			mantenimentInici: startDate,
			mantenimentFinal: endDate,
		};

		this.modalOutput.emit(output);
		this.activeModal.close();
	}

	closeModal = () => this.activeModal.close();
}
