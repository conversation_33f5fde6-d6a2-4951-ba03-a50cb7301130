import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { DetailTabsComponent } from './detail-tabs.component';
import { TabViewModule } from 'primeng/tabview';
import { LazyElementsModule } from '@angular-extensions/elements';

@NgModule({
	declarations: [
		DetailTabsComponent
	],
	imports: [
		CommonModule,
    TabViewModule,
    LazyElementsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule
	],
  exports: [DetailTabsComponent]
})
export class DetailTabsModule { }
