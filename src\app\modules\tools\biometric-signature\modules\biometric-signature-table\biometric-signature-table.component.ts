import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { Router } from '@angular/router';
import { takeUntil } from 'rxjs/operators';
import { LazyLoadEvent } from 'primeng/api';
import {
	DataStorageService,
	PtHeaderTagList,
	PtTableColumn} from 'pt-ui-components-mf-lib';

import { BiometricSignatureService } from '../../biometric-signature.service';
import { DatePipe } from '@angular/common';
import {
	IBiometricSignature,
	BIOMETRIC_SIGNATURE_FILTER_STORAGE,
	BIOMETRIC_SIGNATURE_DETAIL_STORAGE,
	ErrorDTO,
	Metadades,
} from '../../models/biometric-signature.model';
import { AppRoutes, BiometricSignatureRoutes, StatesIcons, ToolsRoutes } from 'src/app/core/models/config.model';
import { IDetailData } from '../biometric-signature-detail/models/biometric-signature-detail.model';
import { FilterModalInput } from '../../../../shared/modal-filter/models/modal-filter.model';
import {
	BiometricSignatureFilterModal,
	BiometricSignatureRequest,
	BiometricSignatureRow,
	EXPORT_COLUMNS_DATA,
	FilterFormValue,
	TableColumns,
	TableMode,
	TableSortFields
} from './models/biometric-signature-table.model';
import { ModalFilterComponent } from '../../../../shared/modal-filter';
import { TableExportService } from 'src/app/core/services/table-export.service';
import { PtTableComponent } from 'pt-ui-components-mf-lib/lib/components/table/table.component';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { BiometricSignatureTableEndpointsService } from './services/biometric-signature-table-endpoints.service';
import { BiometricSignatureResponse, BiometricSignatureResponseContent } from './models';
import { BiometricSignatureTableService } from './services/biometric-signature-table.service';

const BIOMETRIC_FILTER = "BIOMETRIC_FILTER";
@Component({
	selector: 'app-biometric-signature-table',
	templateUrl: './biometric-signature-table.component.html'
})
export class BiometricSignatureTableComponent implements OnInit {

	@ViewChild('dt', { static: true }) private readonly table: PtTableComponent;

	filterValue: BiometricSignatureFilterModal;

	// Table
	searchColumns: PtTableColumn[] = [];
	searchRows: BiometricSignatureRow[] = [];

	// Other
	options: LazyLoadEvent;
	paginationTotal: number = 0;
	filterTagList: PtHeaderTagList[] = [];

	@Input() mode: TableMode = TableMode.ACTIVES;

	private _unsubscribe: Subject<void> = new Subject();

	//Constructor function
	constructor(
		private endpointsService: BiometricSignatureTableEndpointsService,
		private router: Router,
		private biometricSignatureService: BiometricSignatureService,
		private exportService: TableExportService,
		private tableService: BiometricSignatureTableService,
		private dataStorageService: DataStorageService,
		private modalService: NgbModal,
		private translateService: TranslateService,
	) { }

	ngOnInit(): void {
		this.searchColumns = this.tableService.getTableColumns();

		this.translateService.stream('MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE').
		pipe(
			takeUntil(this._unsubscribe)
		).subscribe(() => {
			this.filterValue = this.getFilterValue();
			this.filterTagList = this.tableService.getFilterTagList(this.filterValue);
		});
	}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	openFilterModal() {
		// Open delete modal
		const modalRef = this.modalService.open(
			ModalFilterComponent,
			{ size: 'xl', windowClass: '' }
		);

		const modalInput: FilterModalInput = this.tableService.getModalInput(this.filterValue);
		modalRef.componentInstance.modalInput = modalInput;

		modalRef.componentInstance.modalOutput.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(filterFormValue: BiometricSignatureFilterModal) => {
				this.dataStorageService.setItem(BIOMETRIC_FILTER+this.mode, filterFormValue);
				this.filterValue = filterFormValue;
				this.filterTagList = this.tableService.getFilterTagList(this.filterValue);
				this.table.table.reset();
			}
		);
	}

	/**
	 * Search
	 * @description Search resolucions by criteria
	 */
	search = (event: LazyLoadEvent): void => {
		// Reset previous data
		this.searchRows = [];
		this.paginationTotal = 0;

		this.setOptionsEvent(event);

		this.dataStorageService.setItem(BIOMETRIC_SIGNATURE_FILTER_STORAGE, this.filterValue);

		const request: BiometricSignatureRequest = this.getRequestCopy(event);

		const observable: Observable<BiometricSignatureResponse> = this.mode === TableMode.ACTIVES ?
			this.endpointsService.getActiveList(request) : this.endpointsService.getHistoricList(request);

		observable.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: BiometricSignatureResponse) => {
				if (response?.content) {
					this.setTableRows(response.content);
				}
			}
		);
	}

	private setOptionsEvent(event: LazyLoadEvent) {
		// Request
		if (!event.sortField){
			event.sortField = TableColumns.dataActualitzacio;
			event.sortOrder = -1;
		}

		if (TableSortFields[event.sortField]){
			event.sortField = TableSortFields[event.sortField];
		}

		this.options = event;
	}
	
	private getRequestCopy(event: LazyLoadEvent):  BiometricSignatureRequest {
		const request: BiometricSignatureRequest = {
			filter: {...new BiometricSignatureRequest(this.filterValue, event).filter},
			options: {...new BiometricSignatureRequest(this.filterValue, event).options}
		};

		delete request.filter.data_creacio_error;
		delete request.filter.error_missatge;

		return request;
	}

	/**
	 * Table rows
	 * @description Map the table rows
	 */
	private setTableRows(response: BiometricSignatureResponseContent) {
		this.paginationTotal = response.total;

		this.searchRows = response.results?.map((signature: IBiometricSignature, id: number) => {
			const stateIcon = this.biometricSignatureService.getStateIcon(signature?.estat_peticio);
			const metadadesLabel = this.translateService.instant(`MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.CARD.FIELDS.${TableColumns.metadades}_value`);
			const errorsLabel = this.translateService.instant(`MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.DETAIL.CARD.FIELDS.${TableColumns.errors}_value`);
			// Map autoliquidacio entity into the autoliquidacio table row
			return Object.assign(
				{},
				// Default signature entity
				signature,
				// Table row model
				{
					metadades: {
						id: `metadades${signature.id}`,
						label: signature.metadades.length > 0 ? metadadesLabel : "",
						clickFn: this.showMetadades.bind(this, signature.metadades)
					}
				},
				{
					errors: {
						id: `errors${signature.id}`,
						label: signature.errors.length > 0 ? errorsLabel : "",
						clickFn: this.showErrors.bind(this, signature.errors)
					}
				},
				{
					stateRow: {
						id: `button${signature.id}`,
						icon: stateIcon,
						iconPos: 'left',
						label: signature.estat_peticio ? this.translateService.instant(`MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.estat_peticio.${signature.estat_peticio}`) : "",
						clickFn: this.showDetailError.bind(this, signature?.errors),
						disabled: !this.isStateError(stateIcon)
					},
					tableActions: [{
						id: `detail${signature.id}`,
						icon: 'sli2-eye',
						class: 'p-button-text',
						componentType: 'button',
						clickFn: this.navigateToDetail.bind(this, signature, id)
					}],
				}
			);
		});
	}

	private showDetailError = (errors?: ErrorDTO[]) => this.biometricSignatureService.showErrors(errors);

	private isStateError = (icon: string): boolean => icon === StatesIcons.ERROR;

	private navigateToDetail (row: BiometricSignatureRow, id: number) {
		const detailData: IDetailData = {
			filterValue : this.filterValue,
			totalRows: this.paginationTotal,
			options: this.options,
			index: id + this.options?.first,
			idEntity: row?.id,
			row,
			mode: this.mode
		}

		this.dataStorageService.setItem(BIOMETRIC_SIGNATURE_DETAIL_STORAGE, detailData);

		this.router.navigate(['/' + AppRoutes.TOOLS.concat('/',ToolsRoutes.BIOMETRIC_SIGNATURE,'/',BiometricSignatureRoutes.DETAIL), row.id]);
	}

	private showMetadades = (metadades: Metadades[]) => {
		this.biometricSignatureService.showMetadades(metadades);
	}

	private showErrors(errors: ErrorDTO[]) {
		this.biometricSignatureService.showErrors(errors)
	}


	export = () : void =>  {
		const options = {...this.options,first:0, rows: 500}
		const request: BiometricSignatureRequest = new BiometricSignatureRequest(this.filterValue, options)
		const observable: Observable<BiometricSignatureResponse> = this.mode === TableMode.ACTIVES ?
			this.endpointsService.getActiveList(request) : this.endpointsService.getHistoricList(request);

		observable.pipe(
			takeUntil(this._unsubscribe)
		).subscribe(
			(response: BiometricSignatureResponse) => {
				if (response?.content) {
					var datePipe = new DatePipe('en-US');
					let date = datePipe.transform(new Date(), 'yyyy-MM-dd_HH:mm');
					this.exportService.export2Excel(response?.content.results, EXPORT_COLUMNS_DATA, "Export_"+ date, 'Gestions');
				}
			}
		);
	}

	onTagEvent = (tag: PtHeaderTagList): void => {
		const tagId = (tag.id as String).split('-')[0];

		if (this.filterValue[tagId] instanceof Array) {
			const tagValue = (tag.id as String).split('-')[1]
			this.filterValue[tagId] = this.filterValue[tagId].filter((el: any, index: number) => {
				if(el instanceof Object) {
					return index.toString() !== tagValue;
				}
				return el !== tagValue;
			});



			if (this.filterValue[tagId].length === 0) {
				this.filterValue[tagId] = null;
			}
		} else {
			this.filterValue[tagId] = null;
		}

		this.dataStorageService.setItem(BIOMETRIC_FILTER+this.mode, this.filterValue);
		this.filterTagList = this.tableService.getFilterTagList(this.filterValue);
		this.table.table.reset();
	}

	private getFilterValue = (): BiometricSignatureFilterModal => {
		const filterValue = this.dataStorageService.getItem(BIOMETRIC_FILTER+this.mode) as BiometricSignatureFilterModal;
		return new FilterFormValue(filterValue) ?? new FilterFormValue();
	}
}
