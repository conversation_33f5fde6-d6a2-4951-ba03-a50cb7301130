import {
	AplicacioENUM,
	ErrorDTO,
	EstatPeticioENUM,
	IBiometricSignature,
	Metadades,
	RepositoriDocumentalENUM,
	States,
	TipusSignaturaENUM,
} from '../../../models/biometric-signature.model';
import {
	FilterFieldType,
	FilterFieldsData,
} from 'src/app/modules/shared/filter-inputs';
import { ExportColumnsData } from 'src/app/core/models/export-table.model';
import { LazyLoadEvent } from 'primeng/api';
import { PtTableLinkTemplate } from 'pt-ui-components-mf-lib';
/**
 * Biometric Signature: search filters
 */
export interface BiometricSignatureFilter {
	nom_dispositiu: string;
	nom_document: string;
	metadades: Metadades[];
	repositori_documental: RepositoriDocumentalENUM;
	nom_signant: string;
	tipus_identificador_signant: string;
	identificador_signant: string;
	tipus_signatura: TipusSignaturaENUM;
	anchor: string;
	aplicacio: AplicacioENUM;
	estat_peticio: EstatPeticioENUM[];
	doc_gui: string;
	id_repositori_documental: string;
	data_creacio_from: Date;
	data_creacio_to: Date;
	data_actualitzacio_from: Date;
	data_actualitzacio_to: Date;
	motiu_rebuig: string;
	errors: ErrorDTO[];
	error_missatge: string;
	data_creacio_error: Date;
}

export interface KeyValue {
	key: string;
	value: string;
}

export interface BiometricSignatureFilterModal {
	nom_dispositiu: string;
	nom_document: string;
	metadades: KeyValue[];
	repositori_documental: RepositoriDocumentalENUM;
	nom_signant: string;
	tipus_identificador_signant: string;
	identificador_signant: string;
	tipus_signatura: TipusSignaturaENUM;
	anchor: string;
	aplicacio: AplicacioENUM;
	estat_peticio: EstatPeticioENUM[];
	doc_gui: string;
	id_repositori_documental: string;
	data_creacio_from: Date;
	data_creacio_to: Date;
	data_actualitzacio_from: Date;
	data_actualitzacio_to: Date;
	motiu_rebuig: string;
	error_missatge: string;
	data_creacio_error: Date;
}
export interface BiometricSignatureOptions {
	first?: number;
    rows?: number;
    sort_field?: string;
    sort_order?: number;
}

export class BiometricSignatureRequest {
	filter: Partial<BiometricSignatureFilter>;
	options: BiometricSignatureOptions;

	constructor(
		params: Partial<BiometricSignatureFilterModal>,
		options: LazyLoadEvent
	) {
		if (params.data_actualitzacio_from) {
			params.data_actualitzacio_from.setHours(0);
			params.data_actualitzacio_from.setMinutes(0);
			params.data_actualitzacio_from.setSeconds(0);
		}

		if (params.data_actualitzacio_to) {
			params.data_actualitzacio_to.setHours(23);
			params.data_actualitzacio_to.setMinutes(59);
			params.data_actualitzacio_to.setSeconds(59);
		}

		if (params.data_creacio_from) {
			params.data_creacio_from.setHours(0);
			params.data_creacio_from.setMinutes(0);
			params.data_creacio_from.setSeconds(0);
		}

		if (params.data_creacio_to) {
			params.data_creacio_to.setHours(23);
			params.data_creacio_to.setMinutes(59);
			params.data_creacio_to.setSeconds(59);
		}

		this.filter = {
			...params,
			metadades: params?.metadades
				? params.metadades.map((meta) => {
						return { clau: meta.key, valor: meta.value };
				  })
				: [],
			errors:
				params?.data_creacio_error || params?.error_missatge
					? [
							{
								data_creacio: params?.data_creacio_error
									? getUTCDate(params?.data_creacio_error)
									: null,
								error_missatge: params?.error_missatge,
							},
					  ]
					: [],
			data_creacio_error: params.data_creacio_error
				? getUTCDate(params.data_creacio_error)
				: undefined,
			data_creacio_from: params.data_creacio_from
				? getUTCDate(params.data_creacio_from)
				: undefined,
			data_creacio_to: params.data_creacio_to
				? getUTCDate(params.data_creacio_to)
				: undefined,
			data_actualitzacio_from: params.data_actualitzacio_from
				? getUTCDate(params.data_actualitzacio_from)
				: undefined,
			data_actualitzacio_to: params.data_actualitzacio_to
				? getUTCDate(params.data_actualitzacio_to)
				: undefined,
		};

		this.options = {
			first: options.first,
			rows: options.rows,
			sort_field: options.sortField,
			sort_order: options.sortOrder,
		}
	}
}

export function getUTCDate(date: Date): Date {
	const utcDate = new Date(
		Date.UTC(
			date.getFullYear(),
			date.getMonth(),
			date.getDate(),
			date.getHours(),
			date.getMinutes(),
			date.getSeconds()
		)
	);
	return utcDate;
}

export interface BiometricSignatureRow extends IBiometricSignature {
	stateRow: PtTableLinkTemplate;
}

export enum TableMode {
	ACTIVES = 'actives',
	HISTORIC = 'historic',
}

export type TableModeT = keyof typeof TableMode;

export enum TableColumns {
	idPeticio = 'id',
	aplicacio = 'aplicacio',
	dataActualitzacio = 'data_actualitzacio',
	dataCreacio = 'data_creacio',
	estatPeticio = 'stateRow',
	identificadorSignant = 'identificador_signant',
	idVidSigner = 'id_vid_signer',
	idRepositoriDocumental = 'id_repositori_documental',
	nomDispositiu = 'nom_dispositiu',
	nomDocument = 'nom_document',
	nomSignant = 'nom_signant',
	metadades = 'metadades',
	errors = 'errors',
	reintents = 'reintents',
	repositoriDocumental = 'repositori_documental',
	tipusIdentificadorSignant = 'tipus_identificador_signant',
	tipusSignatura = 'tipus_signatura',
	tableActions = 'tableActions',
}

export enum TableSortFields {
	stateRow = 'estat_peticio',
}

export const EXPORT_COLUMNS_DATA: ExportColumnsData[] = [
	{
		id: TableColumns.idPeticio,
		columnTitle: 'ID_Peticio',
		columnType: 'text',
	},
	{
		id: TableColumns.nomDocument,
		columnTitle: 'Nom_Document',
		columnType: 'text',
	},
	{
		id: TableColumns.nomDispositiu,
		columnTitle: 'Nom_Dispositiu',
		columnType: 'text',
	},
	{
		id: TableColumns.repositoriDocumental,
		columnTitle: 'Repositori_Documental',
		columnType: 'text',
	},
	{
		id: TableColumns.nomSignant,
		columnTitle: 'Nom_Signant',
		columnType: 'text',
	},
	{
		id: TableColumns.tipusIdentificadorSignant,
		columnTitle: 'Tipus_Identificador_Signant',
		columnType: 'text',
	},
	{
		id: TableColumns.identificadorSignant,
		columnTitle: 'Identificador_Signant',
		columnType: 'text',
	},
	{
		id: TableColumns.identificadorSignant,
		columnTitle: 'Identificador_Signant',
		columnType: 'text',
	},
	{
		id: TableColumns.estatPeticio,
		columnTitle: 'Estat',
		columnType: 'translation',
		translation:
			'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.estat_peticio',
	},
	{
		id: TableColumns.idVidSigner,
		columnTitle: 'id_Vid_Signer',
		columnType: 'text',
	},
	{
		id: TableColumns.idRepositoriDocumental,
		columnTitle: 'id_Repositori_Documental',
		columnType: 'text',
	},
	{
		id: TableColumns.reintents,
		columnTitle: 'reintents',
		columnType: 'text',
	},
	{
		id: TableColumns.dataCreacio,
		columnTitle: 'Data_Creacio',
		columnType: 'date',
	},
	{
		id: TableColumns.dataActualitzacio,
		columnTitle: 'Data_Actualitzacio',
		columnType: 'date',
	},
];

export enum FilterFormFields {
	idPeticio = 'id',
	aplicacio = 'aplicacio',
	dataActualitzacioFrom = 'data_actualitzacio_from',
	dataActualitzacioTo = 'data_actualitzacio_to',
	dataCreacioFrom = 'data_creacio_from',
	dataCreacioTo = 'data_creacio_to',
	estatPeticio = 'estat_peticio',
	identificadorSignant = 'identificador_signant',
	tipusIdentificadorSignant = 'tipus_identificador_signant',
	tipusSignatura = 'tipus_signatura',
	// opcions = 'opcions',
	metadades = 'metadades',
	errorMissatge = 'error_missatge',
	dataCreacioError = 'data_creacio_error',
}
export type FilterFormFieldsT = keyof typeof FilterFormFields;

const FILTER_FIELDS_TRANSLATIONS =
	'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.FILTER.FIELDS';
export const FILTER_FORM_DATA: FilterFieldsData[][] = [
	[
		{
			id: FilterFormFields.idPeticio,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idPeticio}`,
			fieldType: FilterFieldType.text,
		},
		{
			id: FilterFormFields.aplicacio,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.aplicacio}`,
			fieldType: FilterFieldType.select,
			optionsValue: Object.entries(AplicacioENUM),
			optionsLabelTranslations:
				'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.aplicacio',
		},
		{
			id: FilterFormFields.dataActualitzacioFrom,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dataActualitzacioFrom}`,
			// validations: Validators.required,
			fieldType: FilterFieldType.datePicker,
		},
		{
			id: FilterFormFields.dataActualitzacioTo,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dataActualitzacioTo}`,
			// validations: Validators.required,
			fieldType: FilterFieldType.datePicker,
		},
		{
			id: FilterFormFields.dataCreacioFrom,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dataCreacioFrom}`,
			// validations: Validators.required,
			fieldType: FilterFieldType.datePicker,
		},
		{
			id: FilterFormFields.dataCreacioTo,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dataCreacioTo}`,
			// validations: Validators.required,
			fieldType: FilterFieldType.datePicker,
		},
		{
			id: FilterFormFields.estatPeticio,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.estatPeticio}`,
			fieldType: FilterFieldType.select,
			optionsValue: Object.entries(States),
			optionsLabelTranslations:
				'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.estat_peticio',
		},
		{
			id: FilterFormFields.identificadorSignant,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.identificadorSignant}`,
			fieldType: FilterFieldType.text,
		},
		{
			id: FilterFormFields.tipusIdentificadorSignant,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.tipusIdentificadorSignant}`,
			fieldType: FilterFieldType.text,
		},
		{
			id: FilterFormFields.tipusSignatura,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.tipusSignatura}`,
			fieldType: FilterFieldType.select,
			optionsValue: Object.entries(TipusSignaturaENUM),
			optionsLabelTranslations:
				'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.tipusSignatura',
		},
		{
			id: FilterFormFields.dataCreacioError,
			label: `${FILTER_FIELDS_TRANSLATIONS}.data_creacio_error`,
			fieldType: FilterFieldType.datePicker,
		},
		{
			id: FilterFormFields.errorMissatge,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.errorMissatge}`,
			fieldType: FilterFieldType.text,
		},
		// {
		//   id: FilterFormFields.opcions,
		//   label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.opcions}`,
		//   fieldType: FilterFieldType.checkbox,
		// 	options: [
		//     { id: 'errors', label: `${FILTER_FIELDS_TRANSLATIONS}.nomesErronis` },
		//   ]
		// },
	],
	[
		{
			id: FilterFormFields.metadades,
			label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.metadades}`,
			fieldType: FilterFieldType.keyValue,
			numOptions: 5,
			containerClass: 'col-12 col-md-6',
		},
	],
];

export class FilterFormValue implements BiometricSignatureFilterModal {
	nom_dispositiu: string;
	nom_document: string;
	metadades: KeyValue[];
	repositori_documental: RepositoriDocumentalENUM;
	nom_signant: string;
	tipus_identificador_signant: string;
	identificador_signant: string;
	tipus_signatura: TipusSignaturaENUM;
	anchor: string;
	aplicacio: AplicacioENUM;
	estat_peticio: EstatPeticioENUM[];
	doc_gui: string;
	id_repositori_documental: string;
	data_creacio_from: Date;
	data_creacio_to: Date;
	data_actualitzacio_from: Date;
	data_actualitzacio_to: Date;
	motiu_rebuig: string;
	error_missatge: string;
	data_creacio_error: Date;

	constructor(data?: BiometricSignatureFilterModal) {
		Object.assign(this, {
			...data,
			data_creacio_error: data?.data_creacio_error
				? new Date(data.data_creacio_error)
				: null,
			data_actualitzacio_from: data?.data_actualitzacio_from
				? new Date(data.data_actualitzacio_from)
				: null,
			data_actualitzacio_to: data?.data_actualitzacio_to
				? new Date(data.data_actualitzacio_to)
				: null,
			data_creacio_from: data?.data_creacio_from
				? new Date(data.data_creacio_from)
				: null,
			data_creacio_to: data?.data_creacio_to
				? new Date(data.data_creacio_to)
				: null,
			estat_peticio:
				data?.estat_peticio?.length > 0 ? data.estat_peticio : null,
		});
	}
}

export enum TranslateTags {
	estatPeticio = 'estat_peticio',
}
