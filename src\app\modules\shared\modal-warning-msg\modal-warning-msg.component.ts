import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { PtMessageI } from 'pt-ui-components-mf-lib';

@Component({
  selector: 'app-modal-warning-msg',
  templateUrl: './modal-warning-msg.component.html',
  styleUrls: ['./modal-warning-msg.component.sass']
})
export class ModalWarningMsgComponent implements OnInit {
	
	@Input() state:string;
	@Input() stateTraslation:string;
	@Input() title:string = `MODULE_TRACKING.WARNING_MSG.TITLE`;
	@Input() subtitle:string = 'MODULE_TRACKING.WARNING_MSG.SUBTITLE';

  	// Confirmation message
	confirmationData: PtMessageI;
	
	constructor(
		public activeModal: NgbActiveModal,
		private translateService: TranslateService
	) { }

	ngOnInit(): void {
		this.setupData();
	}

	setupData(): void {
		const translationSource = this.stateTraslation || 'MODULE_TRACKING.STATES'
		const translateState = this.translateService.instant(`${translationSource}.${this.state}`);
		
		this.confirmationData = {
			severity: 'warning',
			closableFn: this.closeModal.bind(this),
			title: this.title,
			subtitle: this.translateService.instant(this.subtitle,{state: translateState})
		};
	}

	// Close modal > Content button
	closeModal = (): void => {
		this.activeModal.close();
	}

}
