import { Component, OnInit } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-gestions',
  templateUrl: './gestions.component.html'
})
export class GestionsComponent {
	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlElMeuEspaiCss;

	constructor(
		private webComponentService: WebComponentsService
  ) {
		this.webComponentService.setWebComponentStyle(this.wcUrlCss, 'area-privada');
	}

}
