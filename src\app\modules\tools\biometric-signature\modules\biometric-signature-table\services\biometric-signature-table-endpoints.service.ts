import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { BiometricSignatureRequest, BiometricSignatureResponse } from '../models';

@Injectable({
	providedIn: 'root'
})
export class BiometricSignatureTableEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	getActiveList(criteria?: BiometricSignatureRequest | object): Observable<BiometricSignatureResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlBiometricSignature,
			url: "/tracking/signatures/actives",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}

	getHistoricList(criteria?: BiometricSignatureRequest | object): Observable<BiometricSignatureResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlBiometricSignature,
			url: "/tracking/signatures/historic",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}
}
