import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable, of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { CopyDraftResponse, GetSelfAssessmentsResponse, ResponseDeleteAutoliquidacio as ResponseDeleteDraft, ResponseGetDrafts } from '../models/drafts-detail-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class DraftsDetailEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: GET > Get self assessment by id
	getDraft(idSelfAssessment: string): Observable<ResponseGetDrafts> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/esborrany/v1/${idSelfAssessment}`,
			method: 'get'
		}
		return this.httpService.get(requestOptions);
	}

	deleteDraft(id: string): Observable<ResponseDeleteDraft> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/esborrany/v1/${id}`,
			method: 'delete'
		}
		return this.httpService.delete(requestOptions);
	}

	copyDraft(id: string): Observable<CopyDraftResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/esborrany/v1/${id}`,
			method: 'put',
		}
		return this.httpService.put(requestOptions);
	}

	getSelfAssessments(idDraft: string): Observable<GetSelfAssessmentsResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: `/tracking/esborrany/v1/${idDraft}/autoliquidacions`,
			method: 'get',
		}
		return this.httpService.get(requestOptions);
	}
}