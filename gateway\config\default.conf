server {

    listen              1080;

    absolute_redirect   off;

    # server_name is important because it is used by shibboleth for generating SAML URLs
    # Using the catch-all '_' will NOT work.
    server_name ${CLIENT_APP_HOSTNAME:-your-app.localdomain.com};

    # FastCGI authorizer for Auth Request module
    location = /shibauthorizer {
        internal;
        fastcgi_param  SERVER_PORT 443;
        include fastcgi_params;
        fastcgi_pass unix:/tmp/shibauthorizer.sock;
    }

    # FastCGI responder
    location ${SHIBBOLETH_RESPONDER_PATH:-/saml} {
        fastcgi_param  HTTPS on;
        fastcgi_param  SERVER_PORT 443;
        fastcgi_param  SERVER_PROTOCOL https;
        fastcgi_param  X_FORWARDED_PROTO https;
        fastcgi_param  X_FORWARDED_PORT 443;
        include fastcgi_params;
        fastcgi_pass unix:/tmp/shibresponder.sock;
    }

    # Resources for the Shibboleth error pages. This can be customised.
    location /shibboleth-sp {
        alias /etc/shibboleth/;
    }

    # Quitamos CA de la url
    #location ^~ /ca/ {
    #    rewrite ^/ca(.*)$ $1 permanent;
    #}

    # Quitamos ES de la url
    #location ^~ /es/ {
    #    rewrite ^/es(.*)$ $1 permanent;
    #}

    #location = / {
    #    return 301 /tracking/;
    #}

    #location = /tracking {
    #    return 301 /tracking/;
    #}

    location / {
        resolver ${RESOLVERS} valid=10s;

		# Redirect to the secured spanish index page, if the REQUEST_URI starts with /es/secured
    	rewrite ^/es/secured.*$ /secured/index_es.html last;

        # Redirect to the spanish index page, if the REQUEST_URI starts with /es
        rewrite ^/es(?!/secured).*$ /index_es.html last;

        # Redirect to the secured catalan index page, if the REQUEST_URI starts with /ca/secured
        rewrite ^/ca/secured.*$ /secured/index.html last;

        # Redirect to the catalan index page, if the REQUEST_URI starts with /ca
        rewrite ^/ca(?!/secured).*$ /index.html last;

        # setting timeouts
        proxy_connect_timeout 60s;
        proxy_read_timeout 60s;
        proxy_send_timeout 60s;

        ###################################
        ###     Protect front layer     ###
        ###################################

        location /secured/ {

            #try_files $uri $uri/ /secured/index.html;

            more_clear_input_headers 'Variable-*' 'Shib-*' 'Remote-User' 'REMOTE_USER' 'Auth-Type' 'AUTH_TYPE';
            # Add your attributes here. They get introduced as headers
            # by the FastCGI authorizer so we must prevent spoofing.
            more_clear_input_headers 'displayName' 'mail' 'persistent-id' 'GICAR' 'GICARUMJ' 'GICARUMN' 'GICARMAIL' 'GICARCI' 'GICAR_ID' 'GICAR1' 'GICAR2' 'GICAR3' 'GICAR4' 'GICAR5';
            shib_request /shibauthorizer;
            shib_request_use_headers on;

            ### Requisit per a la Construccio de la capcalera GICAR ####
            more_set_input_headers 'GICAR1: CODIINTERN=';
            more_set_input_headers 'GICAR2: ;NIF=';
            more_set_input_headers 'GICAR3: ;EMAIL=';
            more_set_input_headers 'GICAR4: ;UNITAT_MAJOR=';
            more_set_input_headers 'GICAR5: ;UNITAT_MENOR=';

            ### Set headers ####
            proxy_set_header        Accept-Encoding   "";
            proxy_set_header        Host            $host;
            proxy_set_header        X-Real-IP       $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            ### Construccio de la capcalera GICAR ####
            proxy_set_header        GICAR $http_GICAR1$http_GICARCI$http_GICAR2$http_GICAR_ID$http_GICAR3$http_GICARMAIL$http_GICAR4$http_GICARUMJ$http_GICAR5$http_GICARUMN;

            # kill cache
            add_header Last-Modified $date_gmt;
            add_header Cache-Control 'no-store, no-cache';
            if_modified_since off;
            expires off;
            etag off;
        }

        location /tracking/ {

            try_files $uri $uri/ /tracking/index.html;

            more_clear_input_headers 'Variable-*' 'Shib-*' 'Remote-User' 'REMOTE_USER' 'Auth-Type' 'AUTH_TYPE';
            # Add your attributes here. They get introduced as headers
            # by the FastCGI authorizer so we must prevent spoofing.
            more_clear_input_headers 'displayName' 'mail' 'persistent-id' 'GICAR' 'GICARUMJ' 'GICARUMN' 'GICARMAIL' 'GICARCI' 'GICAR_ID' 'GICAR1' 'GICAR2' 'GICAR3' 'GICAR4' 'GICAR5';
            shib_request /shibauthorizer;
            shib_request_use_headers on;

            ### Requisit per a la Construccio de la capcalera GICAR ####
            more_set_input_headers 'GICAR1: CODIINTERN=';
            more_set_input_headers 'GICAR2: ;NIF=';
            more_set_input_headers 'GICAR3: ;EMAIL=';
            more_set_input_headers 'GICAR4: ;UNITAT_MAJOR=';
            more_set_input_headers 'GICAR5: ;UNITAT_MENOR=';

            ### Set headers ####
            proxy_set_header        Accept-Encoding   "";
            proxy_set_header        Host            $host;
            proxy_set_header        X-Real-IP       $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            ### Construccio de la capcalera GICAR ####
            proxy_set_header        GICAR $http_GICAR1$http_GICARCI$http_GICAR2$http_GICAR_ID$http_GICAR3$http_GICARMAIL$http_GICAR4$http_GICARUMJ$http_GICAR5$http_GICARUMN;

            # kill cache
            add_header Last-Modified $date_gmt;
            add_header Cache-Control 'no-store, no-cache';
            if_modified_since off;
            expires off;
            etag off;
        }

       ############################################
        ###      Protect microservice login      ###
        ############################################

        location /api/seguretat/login {
            more_clear_input_headers 'Variable-*' 'Shib-*' 'Remote-User' 'REMOTE_USER' 'Auth-Type' 'AUTH_TYPE';
            # Add your attributes here. They get introduced as headers
            # by the FastCGI authorizer so we must prevent spoofing.
            more_clear_input_headers 'displayName' 'mail' 'persistent-id' 'GICAR' 'GICARUMJ' 'GICARUMN' 'GICARMAIL' 'GICARCI' 'GICAR_ID' 'GICAR1' 'GICAR2' 'GICAR3' 'GICAR4' 'GICAR5';
            shib_request /shibauthorizer;
            shib_request_use_headers on;

            ### Requisit per a la Construccio de la capcalera GICAR ####
            more_set_input_headers 'GICAR1: CODIINTERN=';
            more_set_input_headers 'GICAR2: ;NIF=';
            more_set_input_headers 'GICAR3: ;EMAIL=';
            more_set_input_headers 'GICAR4: ;UNITAT_MAJOR=';
            more_set_input_headers 'GICAR5: ;UNITAT_MENOR=';

            ### ProxyPass Rule to tf-seguretat-ms ###
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/api/seguretat/login;
            proxy_http_version 1.1;

            ### Set headers ####
            proxy_set_header        Accept-Encoding   "";
            proxy_set_header        Host            $host;
            proxy_set_header        X-Real-IP       $remote_addr;
            proxy_set_header        X-Forwarded-For $proxy_add_x_forwarded_for;
            ### Construccio de la capcalera GICAR ####
            proxy_set_header        GICAR $http_GICAR1$http_GICARCI$http_GICAR2$http_GICAR_ID$http_GICAR3$http_GICARMAIL$http_GICAR4$http_GICARUMJ$http_GICAR5$http_GICARUMN;
            proxy_set_header        origin_id       AOC;
        }

        ##################################
        ### Proxy rules to back layer ###
        ##################################

        location /api/pagaments/ {
            proxy_pass http://pt-pagaments-ms${BASE_SERVICE_DOMAIN}:8080/api/pagaments/;
            proxy_http_version 1.1;
        }

        location /api/secured/pagaments/ {
            proxy_pass http://pt-pagaments-ms${BASE_SERVICE_DOMAIN}:8080/api/secured/pagaments/;
            proxy_http_version 1.1;
        }

        location /api/documents-admin/ {
            proxy_pass http://pt-documents-ms${BASE_SERVICE_DOMAIN}:8080/api/documents-admin/;
            proxy_http_version 1.1;
        }
        
        location /api/documents/ {
            client_max_body_size 50M;

            proxy_pass http://pt-documents-ms${BASE_SERVICE_DOMAIN}:8080/api/documents/;
            proxy_http_version 1.1;
        }

        location /api/tributs/ {
            proxy_pass http://pt-tributs-ms${BASE_SERVICE_DOMAIN}:8080/api/tributs/;
            proxy_http_version 1.1;
        }
        
        location /api/seguretat-privat/ {
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/api/seguretat-privat/;
			proxy_http_version 1.1;
        }

        location /api/secured/tributs/ {
            proxy_pass http://pt-tributs-ms${BASE_SERVICE_DOMAIN}:8080/api/secured/tributs/;
            proxy_http_version 1.1;
        }

        location /api/presentacions/ {
            proxy_pass http://pt-presentacions-ms${BASE_SERVICE_DOMAIN}:8080/api/presentacions/;
            proxy_http_version 1.1;
        }

        location /api/seguretat-admin/ {
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/api/seguretat-admin/;
			proxy_http_version 1.1;
        }

        location /api/seguretat/ {
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/api/seguretat/;
            proxy_http_version 1.1;
        }

        location /api/dades-referencia/ {
            proxy_pass http://pt-dades-referencia-ms${BASE_SERVICE_DOMAIN}:8080/api/dades-referencia/;
            proxy_http_version 1.1;
        }

        location /api/gestions/ {
            proxy_pass http://pt-gestions-ms${BASE_SERVICE_DOMAIN}:8080/api/gestions/;
            proxy_http_version 1.1;
        }
        
        location /api/gestions-admin/ {
            proxy_pass http://pt-gestions-ms${BASE_SERVICE_DOMAIN}:8080/api/gestions-admin/;
            proxy_http_version 1.1;
        }

        location /api/turistica/ {
            proxy_pass http://pt-estada-turistica-ms${BASE_SERVICE_DOMAIN}:8080/api/turistica/;
            proxy_http_version 1.1;
        }

        location /api/turistica-admin/ {			
            # Fran - By default, Nginx has a limit of 1MB on file uploads - Solve 413_request Entity too large
			client_max_body_size 10M;

            proxy_pass http://pt-estada-turistica-ms${BASE_SERVICE_DOMAIN}:8080/api/turistica-admin/;
            proxy_http_version 1.1;
        }

        location /api/recurs/v2 {
            proxy_pass http://se-recurs-ms${BASE_SERVICE_DOMAIN}:8080/api/recurs/v2/;
            proxy_http_version 1.1;
        }

        location /api/recurs/ {
            proxy_pass http://pt-recurs-reposicio-ms${BASE_SERVICE_DOMAIN}:8080/api/recurs/;
            proxy_http_version 1.1;
        }

        location /api/area-privada/ {
            proxy_pass http://pt-el-meu-espai-ms${BASE_SERVICE_DOMAIN}:8080/api/area-privada/;
            proxy_http_version 1.1;
        }

        location /api/area-privada-config/ {
            proxy_pass http://pt-el-meu-espai-ms${BASE_SERVICE_DOMAIN}:8080/api/area-privada-config/;
            proxy_http_version 1.1;
        }
        
        location /api/contribuent/ {
            proxy_pass http://pt-contribuent-ms${BASE_SERVICE_DOMAIN}:8080/api/contribuent/;
            proxy_http_version 1.1;
        }  

        location /api/transmissions-patrimonials/v2/ {
            proxy_pass http://se-transmissions-patrimonials-ms${BASE_SERVICE_DOMAIN}:8080/api/transmissions-patrimonials/v2/;
            proxy_http_version 1.1;
        }

        location /api/transmissions-patrimonials/ {
            proxy_pass http://pt-transmissions-patrimonials-ms${BASE_SERVICE_DOMAIN}:8080/api/transmissions-patrimonials/;
            proxy_http_version 1.1;
        }

        location /api/simuladors/ {
            proxy_pass http://pt-ciutada-ms${BASE_SERVICE_DOMAIN}:8080/api/simuladors/;
            proxy_http_version 1.1;
        }

        location /api/cita-previa/ {
            proxy_pass http://pt-ciutada-ms${BASE_SERVICE_DOMAIN}:8080/api/cita-previa/;
            proxy_http_version 1.1;
        }

        location /api/secured/cita-previa/ {
            proxy_pass http://pt-ciutada-ms${BASE_SERVICE_DOMAIN}:8080/api/secured/cita-previa/;
            proxy_http_version 1.1;
        }
        
        location /api/gestio-continguts/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/api/gestio-continguts/;
            proxy_http_version 1.1;
        }
        
        location /api/gestio-continguts-tr/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/api/gestio-continguts-tr/;
            proxy_http_version 1.1;
        }
                
        location /api/gestio-configuracio/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/api/gestio-configuracio/;
            proxy_http_version 1.1;
        }
                
        location /api/gestio-configuracio-tr/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/api/gestio-configuracio-tr/;
            proxy_http_version 1.1;
        }
                        
        location /api/gestio-configuracio-personal/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/api/gestio-configuracio-personal/;
            proxy_http_version 1.1;
        }
        
        location /api/pagament-liquidacions/ {
            proxy_pass http://pt-pagament-liquidacions-ms${BASE_SERVICE_DOMAIN}:8080/api/pagament-liquidacions/;
            proxy_http_version 1.1;
        }
        
        location /api/pagament-liquidacions-config/ {
            proxy_pass http://pt-pagament-liquidacions-ms${BASE_SERVICE_DOMAIN}:8080/api/pagament-liquidacions-config/;
            proxy_http_version 1.1;
        }
		
        location /api/recurs/v2/ {
            proxy_pass http://se-recurs-ms${BASE_SERVICE_DOMAIN}:8080/api/recurs/v2/;
            proxy_http_version 1.1;
        }
        
        location /api/antenes/ {
            proxy_pass http://pt-antenes-ms${BASE_SERVICE_DOMAIN}:8080/api/antenes/;
            proxy_http_version 1.1;
        }

        location /api/tearc/v1 {
           proxy_pass http://tearc-conector-ms${BASE_SERVICE_DOMAIN}:8080/api/tearc/v1;
           proxy_http_version 1.1;
           client_max_body_size 30M;
        }
        
        location /api/sarcat-middleware/ {
           proxy_pass http://sarcat-middleware-ms${BASE_SERVICE_DOMAIN}:8080/api/sarcat-middleware/;
           proxy_http_version 1.1;
        }
        
        location /api/simulador-vehicles/ {
           proxy_pass http://pt-dades-referencia-ms${BASE_SERVICE_DOMAIN}:8080/api/simulador-vehicles/;
           proxy_http_version 1.1;
        }
        
        location /api/igec/ {
           proxy_pass http://se-grans-establiments-ms${BASE_SERVICE_DOMAIN}:8080/api/igec/;
           proxy_http_version 1.1;
        }

		location /api/ianp/ {
           proxy_pass http://se-actius-np-ms${BASE_SERVICE_DOMAIN}:8080/api/ianp/;
           proxy_http_version 1.1;
        }

        location /api/isbee/ {
            proxy_pass http://se-begudes-ensucrades-ms${BASE_SERVICE_DOMAIN}:8080/api/isbee/;
            proxy_http_version 1.1;
        }
        
        location  /api/emissions-co2/ {
           proxy_pass http://se-emissions-co2-ms${BASE_SERVICE_DOMAIN}:8080/api/emissions-co2/;
           proxy_http_version 1.1;
        }

		location  /api/assegurances/ {
            proxy_pass http://se-assegurances-ms${BASE_SERVICE_DOMAIN}:8080/api/assegurances/;
            proxy_http_version 1.1;
        }
        
        location  /api/donacions/ {
            proxy_pass http://se-donacions-ms${BASE_SERVICE_DOMAIN}:8080/api/donacions/;
            proxy_http_version 1.1;
        }
        
        location  /api/begudes/ {
            proxy_pass http://se-begudes-ensucrades-ms${BASE_SERVICE_DOMAIN}:8080/api/begudes/;
            proxy_http_version 1.1;
        }
        
        location /api/reas/ {
            proxy_pass http://se-reas-ms${BASE_SERVICE_DOMAIN}:8080/api/reas/;
            proxy_http_version 1.1;
        }
        
        location  /api/aeat-conector/v1/ {
	 		proxy_pass http://aeat-conector-ms${BASE_SERVICE_DOMAIN}:8080/api/aeat-conector/v1/;
   			proxy_http_version 1.1;
		}
        
        location  /api/signatura-inspeccio/ {
	 		proxy_pass http://se-signatura-inspeccio-ms${BASE_SERVICE_DOMAIN}:8080/api/signatura-inspeccio/;
   			proxy_http_version 1.1;
		}
        
        location  /api/aportar-document/ {
	 		proxy_pass http://se-signatura-inspeccio-ms${BASE_SERVICE_DOMAIN}:8080/api/aportar-document/;
   			proxy_http_version 1.1;
		}
        
        location  /api/tfja/ {
	 		proxy_pass http://se-apostes-ms${BASE_SERVICE_DOMAIN}:8080/api/tfja/;
   			proxy_http_version 1.1;
		}

                
        location /api/signatura-biometrica/ {
            proxy_pass http://gt-signatura-biometrica-ms${BASE_SERVICE_DOMAIN}:8080/api/v1/signatura-biometrica/;
            proxy_http_version 1.1;
            client_max_body_size 60M;
        }
        
        location  /api/aviacio/ {
	 		proxy_pass http://se-aviacio-ms${BASE_SERVICE_DOMAIN}:8080/api/aviacio/;
   			proxy_http_version 1.1;
		}
        
        location  /api/gasos/ {
	 		proxy_pass http://se-gasos-ms${BASE_SERVICE_DOMAIN}:8080/api/gasos/;
   			proxy_http_version 1.1;
		}
        
       #######
       # Fran - Reglas actuator, añadir una por cada micro nuevo que se añada siguiendo el formato
       ######
        
        
        location /actuator/pt-pagaments-ms/ {
            proxy_pass http://pt-pagaments-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-documents-ms/ {
            proxy_pass http://pt-documents-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-tributs-ms/ {
            proxy_pass http://pt-tributs-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-presentacions-ms/ {
            proxy_pass http://pt-presentacions-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-seguretat-ms/ {
            proxy_pass http://pt-seguretat-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-dades-referencia-ms/ {
            proxy_pass http://pt-dades-referencia-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-gestions-ms/ {
            proxy_pass http://pt-gestions-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-estada-turistica-ms/ {
            proxy_pass http://pt-estada-turistica-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-recurs-ms/ {
            proxy_pass http://se-recurs-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-el-meu-espai-ms/ {
            proxy_pass http://pt-el-meu-espai-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-contribuent-ms/ {
            proxy_pass http://pt-contribuent-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-transmissions-patrimonials-ms/ {
            proxy_pass http://pt-transmissions-patrimonials-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-transmissions-patrimonials-ms/ {
            proxy_pass http://se-transmissions-patrimonials-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-ciutada-ms/ {
            proxy_pass http://pt-ciutada-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-gestio-continguts-ms/ {
            proxy_pass http://pt-gestio-continguts-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-pagament-liquidacions-ms/ {
            proxy_pass http://pt-pagament-liquidacions-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/pt-antenes-ms/ {
            proxy_pass http://pt-antenes-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-grans-establiments-ms/ {
            proxy_pass http://se-grans-establiments-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-actius-np-ms/ {
            proxy_pass http://se-actius-np-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-emissions-co2-ms/ {
            proxy_pass http://se-emissions-co2-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-assegurances-ms/ {
            proxy_pass http://se-assegurances-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-donacions-ms/ {
            proxy_pass http://se-donacions-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-begudes-ensucrades-ms/ {
            proxy_pass http://se-begudes-ensucrades-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/se-reas-ms/ {
            proxy_pass http://se-reas-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/aeat-conector-ms/ {
            proxy_pass http://aeat-conector-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/dali-integracio-kafka-ms/ {
            proxy_pass http://dali-integracio-kafka-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/tearc-conector-ms/ {
            proxy_pass http://tearc-conector-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }

        location /actuator/sarcat-middleware-ms/ {
            proxy_pass http://sarcat-middleware-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }
        
        location /actuator/se-signatura-inspeccio-ms/ {
            proxy_pass http://se-signatura-inspeccio-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
            proxy_http_version 1.1;
        }
        
        location  /actuator/se-apostes-ms/ {
	 		proxy_pass http://se-apostes-ms${BASE_SERVICE_DOMAIN}:8080/actuator/;
   			proxy_http_version 1.1;
		}    

       location  /actuator/se-aviacio-ms/ {
	 		proxy_pass http://se-aviacio-ms${BASE_SERVICE_DOMAIN}:8080/api/actuator/;
   			proxy_http_version 1.1;
		}
        
        location  /actuator/se-gasos-ms/ {
	 		proxy_pass http://se-gasos-ms${BASE_SERVICE_DOMAIN}:8080/api/actuator/;
   			proxy_http_version 1.1;
		}


    }
}
