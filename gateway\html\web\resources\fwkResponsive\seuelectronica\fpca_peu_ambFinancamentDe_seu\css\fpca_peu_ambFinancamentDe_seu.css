.footer-logos {
    background-color: #F5F5F5;
}

.footer-logos__wrapper {
    width: 100%;
    padding: 10px 15px 20px 15px;
}

@media (max-width: 1025px) {
    .footer-logos__wrapper {
        padding: 10px 0 20px 0;
        width: calc(100% - 28px);
        margin: auto;
    }
}

.footer-logos__description { 
    display: flex;
    margin-right: 22px;
}

.footer-logos__description span {
    display: block;
    margin: auto;
    font-family: "OpenSans_Bold", Arial, Helvetica, sans-serif;
    font-size: 14px;
}

@media (max-width: 767px) {
    .footer-logos__description {
        width: 100%;
        text-align: center;
        justify-content: center;
        margin: auto;
        margin-bottom: 15px;
    }
}

.footer-logos__list {
    display: flex;
    padding-left: 0;
    margin-bottom: 0;
}

.footer-logos__list-item {
    margin: 0 14px;
}

@media (max-width: 767px) {
    .footer-logos__list {
        width: 100%;
        text-align: center;
        justify-content: center;
    }
}

.footer-logos .NG-border {

    padding-top: 20px;
}
@media (max-width: 1025px) {
    .footer-logos .NG-border {        
        margin: auto;
    }
}
