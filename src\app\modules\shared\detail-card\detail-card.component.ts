import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { Card2Mode, PtDateFormatService } from 'pt-ui-components-mf-lib';
import { DetailCardData, DetailFieldType } from './models/detail-card.model';
import { StatesIcons } from 'src/app/core/models/config.model';
import { TranslateService } from '@ngx-translate/core';

@Component({
	selector: 'app-detail-card',
	templateUrl: './detail-card.component.html'
})
export class DetailCardComponent {

	@Input() title: string;
	@Input() stateIcon: string;
	@Input() stateLabel: string;
	@Input() detailCardFields: DetailCardData[] = [];
	@Input() set formValue(value: any) {
		this.setDetailForm(value);
	};

	@Output() showErrors:EventEmitter<any> = new EventEmitter();
	@Output() linkClicked:EventEmitter<string> = new EventEmitter();

	detailForm: FormGroup;

	// Enums
	Card2Mode = Card2Mode;
	DetailFieldType = DetailFieldType;
	StatesIcons = StatesIcons;

	constructor(
		private fb: FormBuilder,
		private dateFormatService: PtDateFormatService,
		private translationService: TranslateService
	) {
		//...
	}

	private setDetailForm = (data: any): void => {
		this.detailForm = this.fb.group(
			this.setupFormGroup(data)
		);
	}

	private setupFormGroup(data: any): {[key: string]: any} {
		let fields = {};

		this.detailCardFields.forEach(field => {
			const fieldValue = this.getFieldValue(data, field)

			// Add control to fields
			fields[field.id] = [{value: fieldValue,disabled: true}];
		})

		return fields;
	}

	getFieldValue(data: any, field: DetailCardData): any {
		if(data?.[field.id] === undefined || data?.[field.id] === null) return '-';

		switch (field.fieldType) {
			case DetailFieldType.date:
				return this.getDate(data[field.id]);

			case DetailFieldType.number:
				return Number(data[field.id]);

			case DetailFieldType.translate:
				if(field?.fieldValueTranslation) return data[field.id];

				return this.translationService.instant(`${field.fieldValueTranslation}.${data[field.id]}`)
			case DetailFieldType.map:
				if(field?.fieldMap) return data[field.id];

				return field.fieldMap.get(data[field.id]);

			default:
				return data[field.id];
		}
	}

	private getDate (date: Date | string): string | null {
		return date ? this.dateFormatService.fromDateToFormat(new Date(date),'dd/MM/yyyy') : null;
	}

	onStateClick ($event: any){
		this.showErrors.emit($event)
	}

	onLinkClick (id: string){
		this.linkClicked.emit(id)
	}
}
