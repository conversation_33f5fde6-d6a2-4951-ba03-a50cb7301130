import { LazyLoadEvent } from "primeng/api";
import { ManagementsMonitorFilter } from "../../../models/managements-monitor.model";
import { DetailCardData, DetailFieldType } from "src/app/modules/shared/detail-card";
import { TabData, TabType } from "src/app/modules/shared/detail-tabs";

export enum Managements {
  RECURS = "RECURS"
}

export const MANAGEMENTS_TABS_TO_EXCLUDE: {[key in Managements]: TabType[]} = {
  RECURS: [TabType.payments,TabType.presentations]
}

export interface IDetailData {
	filterValue: Partial<ManagementsMonitorFilter>,
	totalRows?: number,
	options?: LazyLoadEvent,
	index?: number,
	idEntity?: string
}

export enum DetailCardFields {
  id = "id",
  idTramitacio = "idTramitacio",
  estat = "estat",
  presentadorNom = "presentadorNom",
  presentadorNif = "presentadorNif",
  subjectePassiuNom = "subjectePassiuNom",
  subjectePassiuNif = "subjectePassiuNif",
  origen = "origen",
  dataModificacio = "dataModificacio",
  tramit = "tramit",
  dadesAddicionals = "dadesAddicionals",
  idGestio = "idGestio"
}
export type DetailCardFieldsT = keyof typeof DetailCardFields;

const DETAIL_CARD_TRANSLATIONS = "MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.DETAIL.CARD.FIELDS";
export const DETAIL_CARD_FORM_DATA:DetailCardData[] = [
  {
    id: DetailCardFields.id,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.id}`
  },
  {
    id: DetailCardFields.idGestio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idGestio}`
  },
  {
    id: DetailCardFields.idTramitacio,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.idTramitacio}`
  },
  {
    id: DetailCardFields.estat,
    fieldType: DetailFieldType.state,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.estat}`
  },
  {
    id: DetailCardFields.presentadorNom,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.presentadorNom}`
  },
  {
    id: DetailCardFields.presentadorNif,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.presentadorNif}`
  },
  {
    id: DetailCardFields.subjectePassiuNom,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.subjectePassiuNom}`
  },
  {
    id: DetailCardFields.subjectePassiuNif,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.subjectePassiuNif}`
  },
  {
    id: DetailCardFields.origen,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.origen}`
  },
  {
    id: DetailCardFields.dataModificacio,
    fieldType: DetailFieldType.date,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dataModificacio}`
  },
  {
    id: DetailCardFields.tramit,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.tramit}`
  },
  {
    id: DetailCardFields.dadesAddicionals,
    fieldType: DetailFieldType.text,
		fieldLabel: `${DETAIL_CARD_TRANSLATIONS}.${DetailCardFields.dadesAddicionals}`
  }
]

const DETAIL_TABS_TRANSLATIONS = "MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.DETAIL.TABS"
export const DETAIL_TAB_DATA: TabData[] = [
  {
    tabType: TabType.documents,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.DOCUMENTS`,
  },
  // {
  //   tabType: TabType.payments,
  //   tabTitle: `${DETAIL_TABS_TRANSLATIONS}.PAYMENTS`,
  //   tabRetryButtonClass: "p-button-primary"
  // },
  // {
  //   tabType: TabType.presentations,
  //   tabTitle: `${DETAIL_TABS_TRANSLATIONS}.PRESENTATIONS`,
  //   tabRetryButtonClass: "p-button-primary"
  // },
  {
    tabType: TabType.procedures,
    tabTitle: `${DETAIL_TABS_TRANSLATIONS}.TRAMITACIONS`,
    tabRetryButtonClass: "p-button-primary"
  },
]
