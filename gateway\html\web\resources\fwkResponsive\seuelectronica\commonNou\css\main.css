/* -- DEVICES SIZES -- */

@font-face {
  font-family: 'openSans_Regular';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-regular_0.eot");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-regular_0.eot") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-regular_0.ttf") format("truetype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-regular_0.woff") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-regular_0.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'openSans_SemiBold';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-semibold_0.eot");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-semibold_0.eot?74g9z8#iefix") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-semibold_0.ttf?74g9z8") format("truetype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-semibold_0.woff?74g9z8") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-semibold_0.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'openSans_Bold';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-bold.eot");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-bold.eot?74g9z8#iefix") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-bold.ttf?74g9z8") format("truetype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-bold.woff?74g9z8") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-bold.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'openSans_light';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-light_0.eot");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-light_0.eot?74g9z8#iefix") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-light_0.ttf?74g9z8") format("truetype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-light_0.woff?74g9z8") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/opensans-light_0.svg") format("svg");
  font-weight: normal;
  font-style: normal;
}


/* FONT AWESOME */

@font-face {
  font-family: 'FontAwesome';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/fontawesome-webfont.eot");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/fontawesome-webfont.eot") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/fontawesome-webfont.woff2") format("woff2"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/fontawesome-webfont.woff") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/fontawesome-webfont.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}

.fa {
  display: inline-block;
  font: normal normal normal 14px/1 FontAwesome;
  font-size: inherit;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}


/* makes the font 33% larger relative to the icon container */

.fa-lg {
  font-size: 1.33333333em;
  line-height: 0.75em;
  vertical-align: -15%;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

.fa-4x {
  font-size: 4em;
}

.fa-5x {
  font-size: 5em;
}

.fa-fw {
  width: 1.28571429em;
  text-align: center;
}

.fa-ul {
  padding-left: 0;
  margin-left: 2.14285714em;
  list-style-type: none;
}

.fa-ul>li {
  position: relative;
}

.fa-li {
  position: absolute;
  left: -2.14285714em;
  width: 2.14285714em;
  top: 0.14285714em;
  text-align: center;
}

.fa-li.fa-lg {
  left: -1.85714286em;
}

.fa-border {
  padding: .2em .25em .15em;
  border: solid 0.08em #eeeeee;
  border-radius: .1em;
}

.fa-pull-left {
  float: left;
}

.fa-pull-right {
  float: right;
}

.fa.fa-pull-left {
  margin-right: .3em;
}

.fa.fa-pull-right {
  margin-left: .3em;
}


/* Deprecated as of 4.4.0 */

.pull-right {
  float: right;
}

.pull-left {
  float: left;
}

.fa.pull-left {
  margin-right: .3em;
}

.fa.pull-right {
  margin-left: .3em;
}

.fa-spin {
  -webkit-animation: fa-spin 2s infinite linear;
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  -webkit-animation: fa-spin 1s infinite steps(8);
  animation: fa-spin 1s infinite steps(8);
}

@-webkit-keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes fa-spin {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

.fa-rotate-90 {
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=1);
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}

.fa-rotate-180 {
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.fa-rotate-270 {
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=3);
  -webkit-transform: rotate(270deg);
  -ms-transform: rotate(270deg);
  transform: rotate(270deg);
}

.fa-flip-horizontal {
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);
  -webkit-transform: scale(-1, 1);
  -ms-transform: scale(-1, 1);
  transform: scale(-1, 1);
}

.fa-flip-vertical {
  filter: progid: DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);
  -webkit-transform: scale(1, -1);
  -ms-transform: scale(1, -1);
  transform: scale(1, -1);
}

:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical {
  filter: none;
}
.fa-stack {
  position: relative;
  display: inline-block;
  width: 2em;
  height: 2em;
  line-height: 2em;
  vertical-align: middle;
}
.fa-stack-1x, .fa-stack-2x {
  position: absolute;
  left: 0;
  width: 100%;
  text-align: center;
}

.fa-stack-1x {
  line-height: inherit;
}

.fa-stack-2x {
  font-size: 2em;
}

.fa-inverse {
  color: #ffffff;
}

/* Font Awesome uses the Unicode Private Use Area (PUA) to ensure screen
   readers do not read off random characters that represent icons */
.fa-glass:before {
  content: "\f000";
}

.fa-music:before {
  content: "\f001";
}

.fa-search:before {
  content: "\f002";
}

.fa-envelope-o:before {
  content: "\f003";
}

.fa-heart:before {
  content: "\f004";
}

.fa-star:before {
  content: "\f005";
}

.fa-star-o:before {
  content: "\f006";
}

.fa-user:before {
  content: "\f007";
}

.fa-film:before {
  content: "\f008";
}

.fa-th-large:before {
  content: "\f009";
}

.fa-th:before {
  content: "\f00a";
}

.fa-th-list:before {
  content: "\f00b";
}

.fa-check:before {
  content: "\f00c";
}

.fa-remove:before, .fa-close:before, .fa-times:before {
  content: "\f00d";
}
.fa-search-plus:before {
  content: "\f00e";
}

.fa-search-minus:before {
  content: "\f010";
}

.fa-power-off:before {
  content: "\f011";
}

.fa-signal:before {
  content: "\f012";
}

.fa-gear:before, .fa-cog:before {
  content: "\f013";
}

.fa-trash-o:before {
  content: "\f014";
}

.fa-home:before {
  content: "\f015";
}

.fa-file-o:before {
  content: "\f016";
}

.fa-clock-o:before {
  content: "\f017";
}

.fa-road:before {
  content: "\f018";
}

.fa-download:before {
  content: "\f019";
}

.fa-arrow-circle-o-down:before {
  content: "\f01a";
}

.fa-arrow-circle-o-up:before {
  content: "\f01b";
}

.fa-inbox:before {
  content: "\f01c";
}

.fa-play-circle-o:before {
  content: "\f01d";
}

.fa-rotate-right:before, .fa-repeat:before {
  content: "\f01e";
}

.fa-refresh:before {
  content: "\f021";
}

.fa-list-alt:before {
  content: "\f022";
}

.fa-lock:before {
  content: "\f023";
}

.fa-flag:before {
  content: "\f024";
}

.fa-headphones:before {
  content: "\f025";
}

.fa-volume-off:before {
  content: "\f026";
}

.fa-volume-down:before {
  content: "\f027";
}

.fa-volume-up:before {
  content: "\f028";
}

.fa-qrcode:before {
  content: "\f029";
}

.fa-barcode:before {
  content: "\f02a";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-tags:before {
  content: "\f02c";
}

.fa-book:before {
  content: "\f02d";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-print:before {
  content: "\f02f";
}

.fa-camera:before {
  content: "\f030";
}

.fa-font:before {
  content: "\f031";
}

.fa-bold:before {
  content: "\f032";
}

.fa-italic:before {
  content: "\f033";
}

.fa-text-height:before {
  content: "\f034";
}

.fa-text-width:before {
  content: "\f035";
}

.fa-align-left:before {
  content: "\f036";
}

.fa-align-center:before {
  content: "\f037";
}

.fa-align-right:before {
  content: "\f038";
}

.fa-align-justify:before {
  content: "\f039";
}

.fa-list:before {
  content: "\f03a";
}

.fa-dedent:before, .fa-outdent:before {
  content: "\f03b";
}

.fa-indent:before {
  content: "\f03c";
}

.fa-video-camera:before {
  content: "\f03d";
}

.fa-photo:before, .fa-image:before, .fa-picture-o:before {
  content: "\f03e";
}

.fa-pencil:before {
  content: "\f040";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-adjust:before {
  content: "\f042";
}

.fa-tint:before {
  content: "\f043";
}

.fa-edit:before, .fa-pencil-square-o:before {
  content: "\f044";
}

.fa-share-square-o:before {
  content: "\f045";
}

.fa-check-square-o:before {
  content: "\f046";
}

.fa-arrows:before {
  content: "\f047";
}

.fa-step-backward:before {
  content: "\f048";
}

.fa-fast-backward:before {
  content: "\f049";
}

.fa-backward:before {
  content: "\f04a";
}

.fa-play:before {
  content: "\f04b";
}

.fa-pause:before {
  content: "\f04c";
}

.fa-stop:before {
  content: "\f04d";
}

.fa-forward:before {
  content: "\f04e";
}

.fa-fast-forward:before {
  content: "\f050";
}

.fa-step-forward:before {
  content: "\f051";
}

.fa-eject:before {
  content: "\f052";
}

.fa-chevron-left:before {
  content: "\f053";
}

.fa-chevron-right:before {
  content: "\f054";
}

.fa-plus-circle:before {
  content: "\f055";
}

.fa-minus-circle:before {
  content: "\f056";
}

.fa-times-circle:before {
  content: "\f057";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-question-circle:before {
  content: "\f059";
}

.fa-info-circle:before {
  content: "\f05a";
}

.fa-crosshairs:before {
  content: "\f05b";
}

.fa-times-circle-o:before {
  content: "\f05c";
}

.fa-check-circle-o:before {
  content: "\f05d";
}

.fa-ban:before {
  content: "\f05e";
}

.fa-arrow-left:before {
  content: "\f060";
}

.fa-arrow-right:before {
  content: "\f061";
}

.fa-arrow-up:before {
  content: "\f062";
}

.fa-arrow-down:before {
  content: "\f063";
}

.fa-mail-forward:before, .fa-share:before {
  content: "\f064";
}

.fa-expand:before {
  content: "\f065";
}

.fa-compress:before {
  content: "\f066";
}

.fa-plus:before {
  content: "\f067";
}

.fa-minus:before {
  content: "\f068";
}

.fa-asterisk:before {
  content: "\f069";
}

.fa-exclamation-circle:before {
  content: "\f06a";
}

.fa-gift:before {
  content: "\f06b";
}

.fa-leaf:before {
  content: "\f06c";
}

.fa-fire:before {
  content: "\f06d";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-eye-slash:before {
  content: "\f070";
}

.fa-warning:before, .fa-exclamation-triangle:before {
  content: "\f071";
}

.fa-plane:before {
  content: "\f072";
}

.fa-calendar:before {
  content: "\f073";
}

.fa-random:before {
  content: "\f074";
}

.fa-comment:before {
  content: "\f075";
}

.fa-magnet:before {
  content: "\f076";
}

.fa-chevron-up:before {
  content: "\f077";
}

.fa-chevron-down:before {
  content: "\f078";
}

.fa-retweet:before {
  content: "\f079";
}

.fa-shopping-cart:before {
  content: "\f07a";
}

.fa-folder:before {
  content: "\f07b";
}

.fa-folder-open:before {
  content: "\f07c";
}

.fa-arrows-v:before {
  content: "\f07d";
}

.fa-arrows-h:before {
  content: "\f07e";
}

.fa-bar-chart-o:before, .fa-bar-chart:before {
  content: "\f080";
}

.fa-twitter-square:before {
  content: "\f081";
}

.fa-facebook-square:before {
  content: "\f082";
}

.fa-camera-retro:before {
  content: "\f083";
}

.fa-key:before {
  content: "\f084";
}

.fa-gears:before, .fa-cogs:before {
  content: "\f085";
}

.fa-comments:before {
  content: "\f086";
}

.fa-thumbs-o-up:before {
  content: "\f087";
}

.fa-thumbs-o-down:before {
  content: "\f088";
}

.fa-star-half:before {
  content: "\f089";
}

.fa-heart-o:before {
  content: "\f08a";
}

.fa-sign-out:before {
  content: "\f08b";
}

.fa-linkedin-square:before {
  content: "\f08c";
}

.fa-thumb-tack:before {
  content: "\f08d";
}

.fa-external-link:before {
  content: "\f08e";
}

.fa-sign-in:before {
  content: "\f090";
}

.fa-trophy:before {
  content: "\f091";
}

.fa-github-square:before {
  content: "\f092";
}

.fa-upload:before {
  content: "\f093";
}

.fa-lemon-o:before {
  content: "\f094";
}

.fa-phone:before {
  content: "\f095";
}

.fa-square-o:before {
  content: "\f096";
}

.fa-bookmark-o:before {
  content: "\f097";
}

.fa-phone-square:before {
  content: "\f098";
}

.fa-twitter:before {
  content: "\f099";
}

.fa-facebook-f:before, .fa-facebook:before {
  content: "\f09a";
}

.fa-github:before {
  content: "\f09b";
}

.fa-unlock:before {
  content: "\f09c";
}

.fa-credit-card:before {
  content: "\f09d";
}

.fa-feed:before, .fa-rss:before {
  content: "\f09e";
}

.fa-hdd-o:before {
  content: "\f0a0";
}

.fa-bullhorn:before {
  content: "\f0a1";
}

.fa-bell:before {
  content: "\f0f3";
}

.fa-certificate:before {
  content: "\f0a3";
}

.fa-hand-o-right:before {
  content: "\f0a4";
}

.fa-hand-o-left:before {
  content: "\f0a5";
}

.fa-hand-o-up:before {
  content: "\f0a6";
}

.fa-hand-o-down:before {
  content: "\f0a7";
}

.fa-arrow-circle-left:before {
  content: "\f0a8";
}

.fa-arrow-circle-right:before {
  content: "\f0a9";
}

.fa-arrow-circle-up:before {
  content: "\f0aa";
}

.fa-arrow-circle-down:before {
  content: "\f0ab";
}

.fa-globe:before {
  content: "\f0ac";
}

.fa-wrench:before {
  content: "\f0ad";
}

.fa-tasks:before {
  content: "\f0ae";
}

.fa-filter:before {
  content: "\f0b0";
}

.fa-briefcase:before {
  content: "\f0b1";
}

.fa-arrows-alt:before {
  content: "\f0b2";
}

.fa-group:before, .fa-users:before {
  content: "\f0c0";
}

.fa-chain:before, .fa-link:before {
  content: "\f0c1";
}

.fa-cloud:before {
  content: "\f0c2";
}

.fa-flask:before {
  content: "\f0c3";
}

.fa-cut:before, .fa-scissors:before {
  content: "\f0c4";
}

.fa-copy:before, .fa-files-o:before {
  content: "\f0c5";
}

.fa-paperclip:before {
  content: "\f0c6";
}

.fa-save:before, .fa-floppy-o:before {
  content: "\f0c7";
}

.fa-square:before {
  content: "\f0c8";
}

.fa-navicon:before, .fa-reorder:before, .fa-bars:before {
  content: "\f0c9";
}

.fa-list-ul:before {
  content: "\f0ca";
}

.fa-list-ol:before {
  content: "\f0cb";
}

.fa-strikethrough:before {
  content: "\f0cc";
}

.fa-underline:before {
  content: "\f0cd";
}

.fa-table:before {
  content: "\f0ce";
}

.fa-magic:before {
  content: "\f0d0";
}

.fa-truck:before {
  content: "\f0d1";
}

.fa-pinterest:before {
  content: "\f0d2";
}

.fa-pinterest-square:before {
  content: "\f0d3";
}

.fa-google-plus-square:before {
  content: "\f0d4";
}

.fa-google-plus:before {
  content: "\f0d5";
}

.fa-money:before {
  content: "\f0d6";
}

.fa-caret-down:before {
  content: "\f0d7";
}

.fa-caret-up:before {
  content: "\f0d8";
}

.fa-caret-left:before {
  content: "\f0d9";
}

.fa-caret-right:before {
  content: "\f0da";
}

.fa-columns:before {
  content: "\f0db";
}

.fa-unsorted:before, .fa-sort:before {
  content: "\f0dc";
}

.fa-sort-down:before, .fa-sort-desc:before {
  content: "\f0dd";
}

.fa-sort-up:before, .fa-sort-asc:before {
  content: "\f0de";
}

.fa-envelope:before {
  content: "\f0e0";
}

.fa-linkedin:before {
  content: "\f0e1";
}

.fa-rotate-left:before, .fa-undo:before {
  content: "\f0e2";
}

.fa-legal:before, .fa-gavel:before {
  content: "\f0e3";
}

.fa-dashboard:before, .fa-tachometer:before {
  content: "\f0e4";
}

.fa-comment-o:before {
  content: "\f0e5";
}

.fa-comments-o:before {
  content: "\f0e6";
}

.fa-flash:before, .fa-bolt:before {
  content: "\f0e7";
}

.fa-sitemap:before {
  content: "\f0e8";
}

.fa-umbrella:before {
  content: "\f0e9";
}

.fa-paste:before, .fa-clipboard:before {
  content: "\f0ea";
}

.fa-lightbulb-o:before {
  content: "\f0eb";
}

.fa-exchange:before {
  content: "\f0ec";
}

.fa-cloud-download:before {
  content: "\f0ed";
}

.fa-cloud-upload:before {
  content: "\f0ee";
}

.fa-user-md:before {
  content: "\f0f0";
}

.fa-stethoscope:before {
  content: "\f0f1";
}

.fa-suitcase:before {
  content: "\f0f2";
}

.fa-bell-o:before {
  content: "\f0a2";
}

.fa-coffee:before {
  content: "\f0f4";
}

.fa-cutlery:before {
  content: "\f0f5";
}

.fa-file-text-o:before {
  content: "\f0f6";
}

.fa-building-o:before {
  content: "\f0f7";
}

.fa-hospital-o:before {
  content: "\f0f8";
}

.fa-ambulance:before {
  content: "\f0f9";
}

.fa-medkit:before {
  content: "\f0fa";
}

.fa-fighter-jet:before {
  content: "\f0fb";
}

.fa-beer:before {
  content: "\f0fc";
}

.fa-h-square:before {
  content: "\f0fd";
}

.fa-plus-square:before {
  content: "\f0fe";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-angle-double-up:before {
  content: "\f102";
}

.fa-angle-double-down:before {
  content: "\f103";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-arrow-right-long:before {
  content: "\f061";
}

.fa-arrow-right-long{
    margin-left: 10px;
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-up:before {
  content: "\f106";
}

.fa-angle-down:before {
  content: "\f107";
}

.fa-desktop:before {
  content: "\f108";
}

.fa-laptop:before {
  content: "\f109";
}

.fa-tablet:before {
  content: "\f10a";
}

.fa-mobile-phone:before, .fa-mobile:before {
  content: "\f10b";
}

.fa-circle-o:before {
  content: "\f10c";
}

.fa-quote-left:before {
  content: "\f10d";
}

.fa-quote-right:before {
  content: "\f10e";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-circle:before {
  content: "\f111";
}

.fa-mail-reply:before, .fa-reply:before {
  content: "\f112";
}

.fa-github-alt:before {
  content: "\f113";
}

.fa-folder-o:before {
  content: "\f114";
}

.fa-folder-open-o:before {
  content: "\f115";
}

.fa-smile-o:before {
  content: "\f118";
}

.fa-frown-o:before {
  content: "\f119";
}

.fa-meh-o:before {
  content: "\f11a";
}

.fa-gamepad:before {
  content: "\f11b";
}

.fa-keyboard-o:before {
  content: "\f11c";
}

.fa-flag-o:before {
  content: "\f11d";
}

.fa-flag-checkered:before {
  content: "\f11e";
}

.fa-terminal:before {
  content: "\f120";
}

.fa-code:before {
  content: "\f121";
}

.fa-mail-reply-all:before, .fa-reply-all:before {
  content: "\f122";
}

.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before {
  content: "\f123";
}

.fa-location-arrow:before {
  content: "\f124";
}

.fa-crop:before {
  content: "\f125";
}

.fa-code-fork:before {
  content: "\f126";
}

.fa-unlink:before, .fa-chain-broken:before {
  content: "\f127";
}

.fa-question:before {
  content: "\f128";
}

.fa-info:before {
  content: "\f129";
}

.fa-exclamation:before {
  content: "\f12a";
}

.fa-superscript:before {
  content: "\f12b";
}

.fa-subscript:before {
  content: "\f12c";
}

.fa-eraser:before {
  content: "\f12d";
}

.fa-puzzle-piece:before {
  content: "\f12e";
}

.fa-microphone:before {
  content: "\f130";
}

.fa-microphone-slash:before {
  content: "\f131";
}

.fa-shield:before {
  content: "\f132";
}

.fa-calendar-o:before {
  content: "\f133";
}

.fa-fire-extinguisher:before {
  content: "\f134";
}

.fa-rocket:before {
  content: "\f135";
}

.fa-maxcdn:before {
  content: "\f136";
}

.fa-chevron-circle-left:before {
  content: "\f137";
}

.fa-chevron-circle-right:before {
  content: "\f138";
}

.fa-chevron-circle-up:before {
  content: "\f139";
}

.fa-chevron-circle-down:before {
  content: "\f13a";
}

.fa-html5:before {
  content: "\f13b";
}

.fa-css3:before {
  content: "\f13c";
}

.fa-anchor:before {
  content: "\f13d";
}

.fa-unlock-alt:before {
  content: "\f13e";
}

.fa-bullseye:before {
  content: "\f140";
}

.fa-ellipsis-h:before {
  content: "\f141";
}

.fa-ellipsis-v:before {
  content: "\f142";
}

.fa-rss-square:before {
  content: "\f143";
}

.fa-play-circle:before {
  content: "\f144";
}

.fa-ticket:before {
  content: "\f145";
}

.fa-minus-square:before {
  content: "\f146";
}

.fa-minus-square-o:before {
  content: "\f147";
}

.fa-level-up:before {
  content: "\f148";
}

.fa-level-down:before {
  content: "\f149";
}

.fa-check-square:before {
  content: "\f14a";
}

.fa-pencil-square:before {
  content: "\f14b";
}

.fa-external-link-square:before {
  content: "\f14c";
}

.fa-share-square:before {
  content: "\f14d";
}

.fa-compass:before {
  content: "\f14e";
}

.fa-toggle-down:before, .fa-caret-square-o-down:before {
  content: "\f150";
}

.fa-toggle-up:before, .fa-caret-square-o-up:before {
  content: "\f151";
}

.fa-toggle-right:before, .fa-caret-square-o-right:before {
  content: "\f152";
}

.fa-euro:before, .fa-eur:before {
  content: "\f153";
}

.fa-gbp:before {
  content: "\f154";
}

.fa-dollar:before, .fa-usd:before {
  content: "\f155";
}

.fa-rupee:before, .fa-inr:before {
  content: "\f156";
}

.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before {
  content: "\f157";
}

.fa-ruble:before, .fa-rouble:before, .fa-rub:before {
  content: "\f158";
}

.fa-won:before, .fa-krw:before {
  content: "\f159";
}

.fa-bitcoin:before, .fa-btc:before {
  content: "\f15a";
}

.fa-file:before {
  content: "\f15b";
}

.fa-file-text:before {
  content: "\f15c";
}

.fa-sort-alpha-asc:before {
  content: "\f15d";
}

.fa-sort-alpha-desc:before {
  content: "\f15e";
}

.fa-sort-amount-asc:before {
  content: "\f160";
}

.fa-sort-amount-desc:before {
  content: "\f161";
}

.fa-sort-numeric-asc:before {
  content: "\f162";
}

.fa-sort-numeric-desc:before {
  content: "\f163";
}

.fa-thumbs-up:before {
  content: "\f164";
}

.fa-thumbs-down:before {
  content: "\f165";
}

.fa-youtube-square:before {
  content: "\f166";
}

.fa-youtube:before {
  content: "\f167";
}

.fa-xing:before {
  content: "\f168";
}

.fa-xing-square:before {
  content: "\f169";
}

.fa-youtube-play:before {
  content: "\f16a";
}

.fa-dropbox:before {
  content: "\f16b";
}

.fa-stack-overflow:before {
  content: "\f16c";
}

.fa-instagram:before {
  content: "\f16d";
}

.fa-flickr:before {
  content: "\f16e";
}

.fa-adn:before {
  content: "\f170";
}

.fa-bitbucket:before {
  content: "\f171";
}

.fa-bitbucket-square:before {
  content: "\f172";
}

.fa-tumblr:before {
  content: "\f173";
}

.fa-tumblr-square:before {
  content: "\f174";
}

.fa-long-arrow-down:before {
  content: "\f175";
}

.fa-long-arrow-up:before {
  content: "\f176";
}

.fa-long-arrow-left:before {
  content: "\f177";
}

.fa-long-arrow-right:before {
  content: "\f178";
}

.fa-apple:before {
  content: "\f179";
}

.fa-windows:before {
  content: "\f17a";
}

.fa-android:before {
  content: "\f17b";
}

.fa-linux:before {
  content: "\f17c";
}

.fa-dribbble:before {
  content: "\f17d";
}

.fa-skype:before {
  content: "\f17e";
}

.fa-foursquare:before {
  content: "\f180";
}

.fa-trello:before {
  content: "\f181";
}

.fa-female:before {
  content: "\f182";
}

.fa-male:before {
  content: "\f183";
}

.fa-gittip:before, .fa-gratipay:before {
  content: "\f184";
}

.fa-sun-o:before {
  content: "\f185";
}

.fa-moon-o:before {
  content: "\f186";
}

.fa-archive:before {
  content: "\f187";
}

.fa-bug:before {
  content: "\f188";
}

.fa-vk:before {
  content: "\f189";
}

.fa-weibo:before {
  content: "\f18a";
}

.fa-renren:before {
  content: "\f18b";
}

.fa-pagelines:before {
  content: "\f18c";
}

.fa-stack-exchange:before {
  content: "\f18d";
}

.fa-arrow-circle-o-right:before {
  content: "\f18e";
}

.fa-arrow-circle-o-left:before {
  content: "\f190";
}

.fa-toggle-left:before, .fa-caret-square-o-left:before {
  content: "\f191";
}

.fa-dot-circle-o:before {
  content: "\f192";
}

.fa-wheelchair:before {
  content: "\f193";
}

.fa-vimeo-square:before {
  content: "\f194";
}

.fa-turkish-lira:before, .fa-try:before {
  content: "\f195";
}

.fa-plus-square-o:before {
  content: "\f196";
}

.fa-space-shuttle:before {
  content: "\f197";
}

.fa-slack:before {
  content: "\f198";
}

.fa-envelope-square:before {
  content: "\f199";
}

.fa-wordpress:before {
  content: "\f19a";
}

.fa-openid:before {
  content: "\f19b";
}

.fa-institution:before, .fa-bank:before, .fa-university:before {
  content: "\f19c";
}

.fa-mortar-board:before, .fa-graduation-cap:before {
  content: "\f19d";
}

.fa-yahoo:before {
  content: "\f19e";
}

.fa-google:before {
  content: "\f1a0";
}

.fa-reddit:before {
  content: "\f1a1";
}

.fa-reddit-square:before {
  content: "\f1a2";
}

.fa-stumbleupon-circle:before {
  content: "\f1a3";
}

.fa-stumbleupon:before {
  content: "\f1a4";
}

.fa-delicious:before {
  content: "\f1a5";
}

.fa-digg:before {
  content: "\f1a6";
}

.fa-pied-piper:before {
  content: "\f1a7";
}

.fa-pied-piper-alt:before {
  content: "\f1a8";
}

.fa-drupal:before {
  content: "\f1a9";
}

.fa-joomla:before {
  content: "\f1aa";
}

.fa-language:before {
  content: "\f1ab";
}

.fa-fax:before {
  content: "\f1ac";
}

.fa-building:before {
  content: "\f1ad";
}

.fa-child:before {
  content: "\f1ae";
}

.fa-paw:before {
  content: "\f1b0";
}

.fa-spoon:before {
  content: "\f1b1";
}

.fa-cube:before {
  content: "\f1b2";
}

.fa-cubes:before {
  content: "\f1b3";
}

.fa-behance:before {
  content: "\f1b4";
}

.fa-behance-square:before {
  content: "\f1b5";
}

.fa-steam:before {
  content: "\f1b6";
}

.fa-steam-square:before {
  content: "\f1b7";
}

.fa-recycle:before {
  content: "\f1b8";
}

.fa-automobile:before, .fa-car:before {
  content: "\f1b9";
}

.fa-cab:before, .fa-taxi:before {
  content: "\f1ba";
}

.fa-tree:before {
  content: "\f1bb";
}

.fa-spotify:before {
  content: "\f1bc";
}

.fa-deviantart:before {
  content: "\f1bd";
}

.fa-soundcloud:before {
  content: "\f1be";
}

.fa-database:before {
  content: "\f1c0";
}

.fa-file-pdf-o:before {
  content: "\f1c1";
}

.fa-file-word-o:before {
  content: "\f1c2";
}

.fa-file-excel-o:before {
  content: "\f1c3";
}

.fa-file-powerpoint-o:before {
  content: "\f1c4";
}

.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before {
  content: "\f1c5";
}

.fa-file-zip-o:before, .fa-file-archive-o:before {
  content: "\f1c6";
}

.fa-file-sound-o:before, .fa-file-audio-o:before {
  content: "\f1c7";
}

.fa-file-movie-o:before, .fa-file-video-o:before {
  content: "\f1c8";
}

.fa-file-code-o:before {
  content: "\f1c9";
}

.fa-vine:before {
  content: "\f1ca";
}

.fa-codepen:before {
  content: "\f1cb";
}

.fa-jsfiddle:before {
  content: "\f1cc";
}

.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before {
  content: "\f1cd";
}

.fa-circle-o-notch:before {
  content: "\f1ce";
}

.fa-ra:before, .fa-rebel:before {
  content: "\f1d0";
}

.fa-ge:before, .fa-empire:before {
  content: "\f1d1";
}

.fa-git-square:before {
  content: "\f1d2";
}

.fa-git:before {
  content: "\f1d3";
}

.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before {
  content: "\f1d4";
}

.fa-tencent-weibo:before {
  content: "\f1d5";
}

.fa-qq:before {
  content: "\f1d6";
}

.fa-wechat:before, .fa-weixin:before {
  content: "\f1d7";
}

.fa-send:before, .fa-paper-plane:before {
  content: "\f1d8";
}

.fa-send-o:before, .fa-paper-plane-o:before {
  content: "\f1d9";
}

.fa-history:before {
  content: "\f1da";
}

.fa-circle-thin:before {
  content: "\f1db";
}

.fa-header:before {
  content: "\f1dc";
}

.fa-paragraph:before {
  content: "\f1dd";
}

.fa-sliders:before {
  content: "\f1de";
}

.fa-share-alt:before {
  content: "\f1e0";
}

.fa-share-alt-square:before {
  content: "\f1e1";
}

.fa-bomb:before {
  content: "\f1e2";
}

.fa-soccer-ball-o:before, .fa-futbol-o:before {
  content: "\f1e3";
}

.fa-tty:before {
  content: "\f1e4";
}

.fa-binoculars:before {
  content: "\f1e5";
}

.fa-plug:before {
  content: "\f1e6";
}

.fa-slideshare:before {
  content: "\f1e7";
}

.fa-twitch:before {
  content: "\f1e8";
}

.fa-yelp:before {
  content: "\f1e9";
}

.fa-newspaper-o:before {
  content: "\f1ea";
}

.fa-wifi:before {
  content: "\f1eb";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-paypal:before {
  content: "\f1ed";
}

.fa-google-wallet:before {
  content: "\f1ee";
}

.fa-cc-visa:before {
  content: "\f1f0";
}

.fa-cc-mastercard:before {
  content: "\f1f1";
}

.fa-cc-discover:before {
  content: "\f1f2";
}

.fa-cc-amex:before {
  content: "\f1f3";
}

.fa-cc-paypal:before {
  content: "\f1f4";
}

.fa-cc-stripe:before {
  content: "\f1f5";
}

.fa-bell-slash:before {
  content: "\f1f6";
}

.fa-bell-slash-o:before {
  content: "\f1f7";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-copyright:before {
  content: "\f1f9";
}

.fa-at:before {
  content: "\f1fa";
}

.fa-eyedropper:before {
  content: "\f1fb";
}

.fa-paint-brush:before {
  content: "\f1fc";
}

.fa-birthday-cake:before {
  content: "\f1fd";
}

.fa-area-chart:before {
  content: "\f1fe";
}

.fa-pie-chart:before {
  content: "\f200";
}

.fa-line-chart:before {
  content: "\f201";
}

.fa-lastfm:before {
  content: "\f202";
}

.fa-lastfm-square:before {
  content: "\f203";
}

.fa-toggle-off:before {
  content: "\f204";
}

.fa-toggle-on:before {
  content: "\f205";
}

.fa-bicycle:before {
  content: "\f206";
}

.fa-bus:before {
  content: "\f207";
}

.fa-ioxhost:before {
  content: "\f208";
}

.fa-angellist:before {
  content: "\f209";
}

.fa-cc:before {
  content: "\f20a";
}

.fa-shekel:before, .fa-sheqel:before, .fa-ils:before {
  content: "\f20b";
}

.fa-meanpath:before {
  content: "\f20c";
}

.fa-buysellads:before {
  content: "\f20d";
}

.fa-connectdevelop:before {
  content: "\f20e";
}

.fa-dashcube:before {
  content: "\f210";
}

.fa-forumbee:before {
  content: "\f211";
}

.fa-leanpub:before {
  content: "\f212";
}

.fa-sellsy:before {
  content: "\f213";
}

.fa-shirtsinbulk:before {
  content: "\f214";
}

.fa-simplybuilt:before {
  content: "\f215";
}

.fa-skyatlas:before {
  content: "\f216";
}

.fa-cart-plus:before {
  content: "\f217";
}

.fa-cart-arrow-down:before {
  content: "\f218";
}

.fa-diamond:before {
  content: "\f219";
}

.fa-ship:before {
  content: "\f21a";
}

.fa-user-secret:before {
  content: "\f21b";
}

.fa-motorcycle:before {
  content: "\f21c";
}

.fa-street-view:before {
  content: "\f21d";
}

.fa-heartbeat:before {
  content: "\f21e";
}

.fa-venus:before {
  content: "\f221";
}

.fa-mars:before {
  content: "\f222";
}

.fa-mercury:before {
  content: "\f223";
}

.fa-intersex:before, .fa-transgender:before {
  content: "\f224";
}

.fa-transgender-alt:before {
  content: "\f225";
}

.fa-venus-double:before {
  content: "\f226";
}

.fa-mars-double:before {
  content: "\f227";
}

.fa-venus-mars:before {
  content: "\f228";
}

.fa-mars-stroke:before {
  content: "\f229";
}

.fa-mars-stroke-v:before {
  content: "\f22a";
}

.fa-mars-stroke-h:before {
  content: "\f22b";
}

.fa-neuter:before {
  content: "\f22c";
}

.fa-genderless:before {
  content: "\f22d";
}

.fa-facebook-official:before {
  content: "\f230";
}

.fa-pinterest-p:before {
  content: "\f231";
}

.fa-whatsapp:before {
  content: "\f232";
}

.fa-server:before {
  content: "\f233";
}

.fa-user-plus:before {
  content: "\f234";
}

.fa-user-times:before {
  content: "\f235";
}

.fa-hotel:before, .fa-bed:before {
  content: "\f236";
}

.fa-viacoin:before {
  content: "\f237";
}

.fa-train:before {
  content: "\f238";
}

.fa-subway:before {
  content: "\f239";
}

.fa-medium:before {
  content: "\f23a";
}

.fa-yc:before, .fa-y-combinator:before {
  content: "\f23b";
}

.fa-optin-monster:before {
  content: "\f23c";
}

.fa-opencart:before {
  content: "\f23d";
}

.fa-expeditedssl:before {
  content: "\f23e";
}

.fa-battery-4:before, .fa-battery-full:before {
  content: "\f240";
}

.fa-battery-3:before, .fa-battery-three-quarters:before {
  content: "\f241";
}

.fa-battery-2:before, .fa-battery-half:before {
  content: "\f242";
}

.fa-battery-1:before, .fa-battery-quarter:before {
  content: "\f243";
}

.fa-battery-0:before, .fa-battery-empty:before {
  content: "\f244";
}

.fa-mouse-pointer:before {
  content: "\f245";
}

.fa-i-cursor:before {
  content: "\f246";
}

.fa-object-group:before {
  content: "\f247";
}

.fa-object-ungroup:before {
  content: "\f248";
}

.fa-sticky-note:before {
  content: "\f249";
}

.fa-sticky-note-o:before {
  content: "\f24a";
}

.fa-cc-jcb:before {
  content: "\f24b";
}

.fa-cc-diners-club:before {
  content: "\f24c";
}

.fa-clone:before {
  content: "\f24d";
}

.fa-balance-scale:before {
  content: "\f24e";
}

.fa-hourglass-o:before {
  content: "\f250";
}

.fa-hourglass-1:before, .fa-hourglass-start:before {
  content: "\f251";
}

.fa-hourglass-2:before, .fa-hourglass-half:before {
  content: "\f252";
}

.fa-hourglass-3:before, .fa-hourglass-end:before {
  content: "\f253";
}

.fa-hourglass:before {
  content: "\f254";
}

.fa-hand-grab-o:before, .fa-hand-rock-o:before {
  content: "\f255";
}

.fa-hand-stop-o:before, .fa-hand-paper-o:before {
  content: "\f256";
}

.fa-hand-scissors-o:before {
  content: "\f257";
}

.fa-hand-lizard-o:before {
  content: "\f258";
}

.fa-hand-spock-o:before {
  content: "\f259";
}

.fa-hand-pointer-o:before {
  content: "\f25a";
}

.fa-hand-peace-o:before {
  content: "\f25b";
}

.fa-trademark:before {
  content: "\f25c";
}

.fa-registered:before {
  content: "\f25d";
}

.fa-creative-commons:before {
  content: "\f25e";
}

.fa-gg:before {
  content: "\f260";
}

.fa-gg-circle:before {
  content: "\f261";
}

.fa-tripadvisor:before {
  content: "\f262";
}

.fa-odnoklassniki:before {
  content: "\f263";
}

.fa-odnoklassniki-square:before {
  content: "\f264";
}

.fa-get-pocket:before {
  content: "\f265";
}

.fa-wikipedia-w:before {
  content: "\f266";
}

.fa-safari:before {
  content: "\f267";
}

.fa-chrome:before {
  content: "\f268";
}

.fa-firefox:before {
  content: "\f269";
}

.fa-opera:before {
  content: "\f26a";
}

.fa-internet-explorer:before {
  content: "\f26b";
}

.fa-tv:before, .fa-television:before {
  content: "\f26c";
}

.fa-contao:before {
  content: "\f26d";
}

.fa-500px:before {
  content: "\f26e";
}

.fa-amazon:before {
  content: "\f270";
}

.fa-calendar-plus-o:before {
  content: "\f271";
}

.fa-calendar-minus-o:before {
  content: "\f272";
}

.fa-calendar-times-o:before {
  content: "\f273";
}

.fa-calendar-check-o:before {
  content: "\f274";
}

.fa-industry:before {
  content: "\f275";
}

.fa-map-pin:before {
  content: "\f276";
}

.fa-map-signs:before {
  content: "\f277";
}

.fa-map-o:before {
  content: "\f278";
}

.fa-map:before {
  content: "\f279";
}

.fa-commenting:before {
  content: "\f27a";
}

.fa-commenting-o:before {
  content: "\f27b";
}

.fa-houzz:before {
  content: "\f27c";
}

.fa-vimeo:before {
  content: "\f27d";
}

.fa-black-tie:before {
  content: "\f27e";
}

.fa-fonticons:before {
  content: "\f280";
}

@font-face {
  font-family: 'icomoon';
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/icomoon.eot?4fgcz4");
  src: url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/icomoon.eot?4fgcz4#iefix") format("embedded-opentype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/icomoon.ttf?4fgcz4") format("truetype"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/icomoon.woff?4fgcz4") format("woff"), url("https://atc.gencat.cat/web/resources/fwkResponsive/seuelectronica/commonNou/fonts/icomoon.svg?4fgcz4#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
}

[class^="icon-"], [class*=" icon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'icomoon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-key_icon:before {
  content: "\e900";
}

/* -- DEVICES SIZES -- */
.titleSection {
  text-align: left;
  display: inline-block;
  font-size: 35px;
  font-family: 'openSans_Regular';
  padding: 10px 0;
  color: #444;
  margin: 0;
}

@media (max-width: 47.938em) {
  .titleSection {
    width: 100%;
  }
}
html, body {
  font-family: "openSans_Regular" !important;
  font-size: 0.938em;}

.safari .blocksList {
  width: 100%;
  margin-bottom: 50px;
  margin: 0;
}

.safari .blocksList .blockListSingle {
  width: 90%;
  height: inherit !important;
  margin: 10px 5%;
  padding: 15px;
  padding-bottom: 30px;
}
.safari .listNews li {
  width: 100%;
  margin: 0;
  padding: 15px;
}
.safari .searchAvanza .blockLeft form .titleSection i {
  top: 5px;
}
.safari .searchAvanza .blockLeft form .boxLupSearch i {
  top: 6px;
}
.safari .searchAvanza .blockRight .catalog .tab-content .hiperTabs {
  width: 110%;
}
.safari .block8-4 .blockRightWrapper a i + span {
  position: relative;
  top: 7px;
  width: 100%;
  margin-right: -25px;
}
.bgWhite {
  background: #fff;
}

.bgRed {
  background: #106BC4 !important;
}

section {
  margin: 30px 0;
}

@media (max-width: 47.950em) {
  section {
      margin: 0;
  }
}
.marginTB20 {
  margin: 20px 0 !important;
}
.marginTop20 {
  margin-top: 20px;
}

.marginTop-10 {
  margin-top: -15px;
  display: inline-block;
  width: calc(100% + 30px);
  width: -webkit-calc(100% + 30px);
}
.padding5 {
  padding-left: 5px !important;
}

.padding0 {
  padding-left: 0px !important;
}



/* -- DEVICES SIZES -- */
header .headerTop {
  min-height: 0px;
  padding: 5px 0;
}

header .headerTop nav a.logo {
  background: url("../img/NG_logo_generalitat.png") no-repeat 0 0;
  margin: 5px 0;
  margin-bottom: 0;
}

header .headerTop nav .column ul {
  margin: 0;
  padding: 0;
}

header .headerTop nav .column ul li a {
  margin-left: .8em;
}
header .headerCenter {
  background: #f5f5f5;
  padding: 15px 0;
  margin: 0;
}
header .headerCenter .blockLeft {
  display: inline-block;
}

header .headerCenter .blockLeft a {
  display: inline-block;
  max-height: 65px;
}

header .headerCenter .blockLeft a img {
  max-height: 65px;
}

header .headerCenter .blockCenter {
  display: inline-block;
  text-align: left;
  height: 100%;
  height: 65px;
  display: table;
}

header .headerCenter .blockCenter p {
  display: inline-block;
  vertical-align: bottom;
  font-size: 20px;
  color: #cd0080;
  font-weight: bold;
  padding-top: 45px;
  color: #106BC4;
  font-family: 'openSans_light';
  line-height: 1;
  margin: 0px;
}

header .headerCenter .blockRight {
  display: inline-block;
  text-align: right;
}

header .headerCenter .blockRight .searchAdva {
  font-size: 13px;
  color: #106BC4;
  line-height: 1;
  display: inline-block;
  width: auto;
  font-family: 'openSans_SemiBold';
  margin-top: 10px;
}

header .headerCenter .blockRight form {
  position: relative;
  width: 100%;
  display: inline-block;
  text-align: right;
  margin-top: 5px;
}

header .headerCenter .blockRight form input {
  width: 60%;
  margin-top: 5px;
  padding: 7px 10px;
  font-family: 'openSans_SemiBold';
  border: none;
  color: #106BC4;
  display: inline-block;
}

header .headerCenter .blockRight form .buttonSearch {
  background: none;
  font-family: 'FontAwesome';
  border: none;
  font-weight: lighter;
  font-size: 21px;
  position: absolute;
  right: 0;
  outline: none;
  width: auto;
  background: #106BC4;
  color: #fff;
  padding: 6px 11px;
}

header .headerCenter .blockRight form .buttonSearch:hover {
  text-decoration: none;
  outline: none;
}
header .searchMobil {
  display: none;
  text-align: right;
  padding: 10px;
  padding-top: 5px;
}
header .searchMobil .searchAdva {
  font-size: 13px;
  color: #106BC4;
  line-height: 1;
  display: inline-block;
  width: auto;
  font-family: 'openSans_SemiBold';
}
header .searchMobil form {
  position: relative;
  width: 100%;
  display: inline-block;
  text-align: left;
  margin-top: 0px;
  padding: 5px;
  background: #106BC4;
}
header .searchMobil form input {
  width: calc(100% - 60px);
  width: -webkit-calc(100% - 60px);
  padding: 5px 10px;
  font-family: 'openSans_SemiBold';
  border: none;
  color: #106BC4;
  display: inline-block;
  margin-left: 40px;
  background: none;
  border-left: 1px solid #43c0ff;
  color: #fff;
  outline: none;
}

header .searchMobil form input::-webkit-input-placeholder{
    color: #4DB9F2;
}
header .searchMobil form input::-moz-placeholder{
    color: #4DB9F2;
}
header .searchMobil form input::-ms-input-placeholder{
    color: #4DB9F2;
}
header .searchMobil form input::-moz-placeholder{
    color: #4DB9F2;
}
header .searchMobil form input::placeholder {
    color: #4DB9F2;
}
header .searchMobil form .buttonSearch {
  background: none;
  font-family: 'FontAwesome';
  border: none;
  font-weight: lighter;
  font-size: 20px;
  position: absolute;
  top: 0;
  outline: none;
  width: auto;
  background: #106BC4;
  color: #fff;
  padding: 9px 11px;
  left: 0;
  margin: 0px;
}

header .searchMobil form .buttonSearch:hover {
  text-decoration: none;
  outline: none;
}
header .searchMobil form .resetSearch {
  background: none;
  border: none;
  font-weight: lighter;
  font-size: 20px;
  position: absolute;
  top: 0;
  outline: none;
  width: auto;
  background: #106BC4;
  color: #fff;
  padding: 7px 11px;
  right: 0;
  margin: 0px;
}

header .searchMobil form .resetSearch:hover {
  text-decoration: none;
  outline: none;
}
header .buttonMenu {
  top: 0;
  color: #fff;
  font-size: 25px;
  float: left;
  width: auto;
  margin-left: -15px;
  padding: 9px 12px;
  background: #666460;
}
header .buttonMenu:hover, header .buttonMenu:focus, header .buttonMenu:active{
  background: rgb(0, 146, 220);
}
header .buttonMenu i {
  float: left;
  margin-left: -14px;
  width: auto;
  font-size: .85em;
}
header .navigationMenu {
  background: #106BC4;
  margin: 0;
  border: none;
  position: relative;
  border-bottom: 1px solid white;
}
header .navigationMenu .container {
    background: none;
}

header .navigationMenu > .container > ul {
  padding: 0px;
  margin: 0px;
  margin-bottom: 0;
  display: inline-block;
  float: left;
  overflow: hidden;
 width: 100%;
}
header .navigationMenu .container ul li {
  background: none;
  display: inline-block;
  margin: 0px;
  padding: 0px;
  float: left;
  position: inherit;
}

header .navigationMenu > .container > ul > li {
    background: transparent;
    display: inline-block;
    margin: 0px;
    padding: 12px 8px 14px 8px;
    float: left;
    position: inherit;
    border-right: 1px solid white;
    text-align: center;
}

header .navigationMenu > .container > ul > li:hover {
    background: #f5f5f5;
}

header .navigationMenu > .container > ul > li:hover a{
    color: #333;
}

header .navigationMenu .container ul li:last-child {
  background: #cd0080;
}

header .navigationMenu .container ul li:last-child:hover {
  background: #f5f5f5;
}

header .navigationMenu .container ul li:last-child a {
  border: none;
}
header .navigationMenu .container > ul > li > a {
  font-family: 'openSans_SemiBold';
  font-size: 17px;
  line-height: 1;
  color: #fff;
  display: inline-block;
  vertical-align: sub;
}


header .navigationMenu .container ul li .dropdown-menu ul li a {
    padding: 10px 0px;
    padding-left: 20px;
    font-size: 15px;
    color: #333;
    position: relative;
    line-height: 1.2;
    border: none;
    font-family: 'openSans_Bold';
}
header .navigationMenu .container ul li a:hover {
  text-decoration: none !important;
}
header .navigationMenu .container ul li a:hover:before {
  color: #333;
}
header .navigationMenu .container ul li a.active {
  background: #fff;
  color: #333;
}
header .navigationMenu .container ul li a i {
  display: none;
}
header .navigationMenu .container div.col-sm-4.col-md-6.col_left {
    margin-top: 40px;
}
header .navigationMenu .container ul li .folderTram {
  font-size: 13px;
  color: #333;
  padding-left: 38px;
  position: relative;
  display: inline-block;
  font-size: 17px;
  color: white;
  margin: 0px;
  padding-right: 0;
  vertical-align: sub;
  line-height: 1;
  float: none;
}

header .navigationMenu .container ul li .folderTram:before {
  position: absolute;
  left: 0px;
  content: url("../img/llaveObjeto.png");
  font-family: "FontAwesome";
  bottom: calc(50% - 8px);
  bottom: -webkit-calc(50% - 8px);
  font-size: 20px;
  color: #fff;
  left: 2px;
}

header .navigationMenu .container ul li .closeSubMenu {
  position: relative;
  float: right;
  margin-top: 0px;
  margin-bottom: -5px;
  padding: 0;
  font-size: 35px;
  font-family: 'openSans_Regular';
  color: #333;
  border: none;
  margin-right: 10px;
}

header .navigationMenu .container ul li .dropdown-menu ul li a {
    padding: 10px 0px;
    padding-left: 20px;
    font-size: 15px;
    color: #333;
    position: relative;
    line-height: 1.2;
    border: none;
    font-family: 'openSans_Bold';
    display: inline-block;
    text-align: left;
}

header .navigationMenu .container ul li .dropdown-menu {
  position: absolute;
  width: 100%;
  left: 0px;
  background: #f5f5f5;
  padding: 0 0 30px;
  margin-top: -2px;
}

header .navigationMenu .container ul li .dropdown-menu ul {
  max-height: inherit;
  border: none;
  position: relative;
  box-shadow: none;
  padding: 0;
  padding-left: 1em;
  margin-top: 20px;
}
header .navigationMenu .container li.dropdown.dropdown-submenu.first a {
    color: #cd0080 !important;
}
header .navigationMenu .container ul li .dropdown-menu ul li{
  display: block;
  float: none;
  text-align: left;
}
header .navigationMenu .container ul li .dropdown-menu ul li a {
  padding: 10px 0px;
  padding-left: 20px;
  font-size: 15px;
  color: #333;
  position: relative;
  line-height: 1.2;
  border: none;
  font-family: 'openSans_Bold';
}

header .navigationMenu .container ul li .dropdown-menu ul li:last-child {
  background: none;
}

header .navigationMenu .container ul li .dropdown-menu ul li .dropdown-menu a {
  color: #333;
  font-size: 14px;
  padding: 7px 0;
  padding-left: 40px;
  font-family: "openSans_Bold";
  position: relative;
}

header .navigationMenu .container ul li .dropdown-menu ul li .dropdown-menu a:before {
  font-family: fontawesome;
  content: "\f111";
  vertical-align: middle;
  position: absolute;
  top: 12px;
  left: 25px;
  font-size: 6px;
  color: #cd0080;
}
header .navigationMenu .container ul .folderTram {
  font-size: 13px;
  color: #333;
  line-height: 1;
  padding-left: 20px;
  position: relative;
  display: inline-block;
  float: right;
  text-align: center;
  border: none !important;
}
header .navigationMenu .container ul .folderTram a {
  position: relative;
  border: none;
  width: auto;
  color: #333;
  font-size: 13px;
  border: none !important;
}
header .navigationMenu .container ul .folderTram a:hover {
  background: none !important;
}

header .navigationMenu .container ul .folderTram a:before {
  position: absolute;
  left: -10px;
  content: "\f007";
  font-family: "FontAwesome";
  bottom: calc(50% - 5px);
  bottom: -webkit-calc(50% - 8px);
  font-size: 20px;
  top: calc(50% - 10px);
  top: -webkit-calc(50% - 8px);
  color: #cd0080;
}
header .navigationMenu > .container > ul > li.open {
  background: #f5f5f5;
  text-decoration: none !important;
}
header .navigationMenu > .container > ul > li.open a{
  color: #333;
}

@media (max-width: 1200px) {
  header .navigationMenu .container ul li{
    padding: 10px 6px 12px 6px;
  }
  header .navigationMenu .container ul li a {
    font-size: 15px;
  }
  header .navigationMenu .container ul li .dropdown-menu ul li{
    padding: 0;
  }
  header .navigationMenu .container ul li:last-child a {
    font-size: 15px;
  }
  header .navigationMenu .container ul .folderTram a:before {
    left: 0;
  }
  header .navigationMenu .container ul li .folderTram {
    padding-left: 29px;
  }
}
@media (max-width: 992px) {
  header .navigationMenu .container ul li{
    padding: 10px 5px 12px 5px;
  }
  header .navigationMenu .container ul li a {
    font-size: 13px;
  }

  header .navigationMenu .container ul li:last-child a {
    font-size: 13px;
  }
}
.navigationMenuMobile {
  display: none;
}
.titleSeuMobil {
  display: none;
}
@media (max-width: 47.938em) {
  .navbar-default .navbar-collapse {
    position: relative;
  }
  header {
    position: relative;
  }
  header .titleSeuMobil {
    float: left;
    display: inline-block;
    width: 160px;
    margin-left: .5em;
  }
  header .titleSeuMobil .blockLeft {
    display: inline-block;
    width: 100%;
    margin-left: -15px;
  }
  header .titleSeuMobil .blockLeft a {
    display: inline-block;
    width: 100%;
    margin-left: -15px;
  }
  header .titleSeuMobil .blockLeft a img {
    max-width: 100%;
    width: 160px;
    display: inline-block;
    margin-left: -13px;
    margin-top: 10px;
  }
  header .titleSeuMobil .blockCenter {
    display: inline-block;
    text-align: left;
    height: 100%;
    display: table;
  }
  header .titleSeuMobil .blockCenter p {
    display: inline-block;
    vertical-align: bottom;
    font-size: 14px;
    padding-top: 0px;
    font-family: 'openSans_regular';
    line-height: 1;
    margin: 0px;
    color: #106BC4;
    margin-left: 9px;
    margin-bottom: 10px;
    margin-top: 5px;
  }
    header .headerTop {
      padding: 0px;
      display: inline-block;
      width: 100%; }
      header .headerTop .container {
        margin-top: 0; }
      header .headerTop .navigationMenuMobile {
        width: 100%;
        margin-top: -5px;
        border: none;
        display: none; }
        header .headerTop .navigationMenuMobile .container {
          padding: 0; }
        header .headerTop .navigationMenuMobile .menuNavMobil {
          padding: 0px;
          margin: 0px; }
          header .headerTop .navigationMenuMobile .menuNavMobil li a.logo {
            background-image: url("../img/NG_logo_generalitat.png");
            background-repeat: no-repeat;
            margin: 0 0;
            margin-bottom: 0;
            background-position: 15px 5px;
            background-size: auto 80%;
            padding: 17px 15px; }
            header .headerTop .navigationMenuMobile .menuNavMobil li a.logo i {
              content: "x";
              float: right;
              text-indent: 0px;
              font-size: 14px;
              padding: 3px;
              margin: 0;
              position: relative;
              top: -9px; }
          header .headerTop .navigationMenuMobile .menuNavMobil li a {
            color: #fff;
            width: 100%;
            display: inline-block;
            padding: 10px 15px;
            border-top: 1px solid white;
            font-family: 'openSans_SemiBold';
            background: #666460; }
            header .headerTop .navigationMenuMobile .menuNavMobil li a i {
              float: right;
              border-radius: 50%;
              background: #ecedef;
              color: #666460;
              width: 20px;
              text-align: center;
              font-size: 20px; }
          header .headerTop .navigationMenuMobile .menuNavMobil li .linkThreeMenu {
            color: #cd0080; }
          header .headerTop .navigationMenuMobile .menuNavMobil li .threeMenu ul li:last-child a {
            border-bottom: none; }
          header .headerTop .navigationMenuMobile .menuNavMobil li .threeMenu ul li a {
            border-bottom: 1px solid #e2e0db;
            padding: 10px 0;
            color: #333; }
          header .headerTop .navigationMenuMobile .menuNavMobil li a.blueBlock, header .headerTop .navigationMenuMobile .menuNavMobil li a.active {
            background: #106BC4;
            color: white; }
            header .headerTop .navigationMenuMobile .menuNavMobil li a.blueBlock i, header .headerTop .navigationMenuMobile .menuNavMobil li a.active i {
              background: #fff;
              color: #106BC4; }
          header .headerTop .navigationMenuMobile .menuNavMobil li .subMenu {
            display: none; }
          header .headerTop .navigationMenuMobile .menuNavMobil li ul {
            padding: 0px;
            background: #fff; }
            header .headerTop .navigationMenuMobile .menuNavMobil li ul li {
              padding: 0 30px; }
              header .headerTop .navigationMenuMobile .menuNavMobil li ul li a {
                background: none;
                color: #98958f;
                border-bottom: 1px solid #e2e0db; }
              header .headerTop .navigationMenuMobile .menuNavMobil li ul li:last-child a {
                border: none; }
          header .headerTop .navigationMenuMobile .menuNavMobil li .active {
            background: #106BC4; }
      header .headerTop nav {
        display: inline-flex;
        width: calc(100% + 30px);
        width: -webkit-calc(100% + 30px);
        background: #252525; }
        header .headerTop nav a.logo {
          display: inline-block;
          width: 160px;
          margin-left: 5px;
          float: left;
          background-size: 100% auto;
          background-position: 0px 3px; }
        header .headerTop nav .buttonSearchMobil, header .headerTop nav .buttonTramitacio {
          float: right;
          font-size: 20px;
          padding: 8.5px 0px;
          width: 40px;
          width: 45px;
          background: #666460;
          position: relative;
          right: -15px; }
          header .headerTop nav .buttonSearchMobil i, header .headerTop nav .buttonTramitacio i {
            float: left; }
          header .headerTop nav .buttonSearchMobil:hover, header .headerTop nav .buttonSearchMobil:focus, header .headerTop nav .buttonTramitacio:hover, header .headerTop nav .buttonTramitacio:focus {
            color: #fff; }
        header .headerTop nav .buttonTramitacio {
          border-right: 1px solid #333;
          right: -20px; }
          header .headerTop nav .buttonTramitacio:hover, header .headerTop nav .buttonTramitacio:focus, header .headerTop nav .buttonTramitacio:active{
            background-color: #cd0080;
          }
          header .headerTop nav .buttonTramitacio i {
            font-size: 12px;
            margin-left: -25px;
            padding: 4px 0; }
            header .headerTop nav .buttonTramitacio i:before {
              content: "\e900";
              color: white; }
          header .headerTop nav .buttonTramitacio:hover, header .headerTop nav .buttonTramitacio:focus {
            color: #106BC4; }
    header .headerCenter {
      position: relative;
      margin-top: -5px; }
      header .headerCenter .blockRight {
        position: absolute;
        left: 0;
        top: 0px;
        background: #ecedef;
        width: 100%;
        display: none;
        z-index: 5; }
        header .headerCenter .blockRight .folderTram {
          display: none; }
        header .headerCenter .blockRight form {
          margin-bottom: 00px; }
          header .headerCenter .blockRight form input {
            width: 100%; }
          header .headerCenter .blockRight form .buttonSearch {
            top: 0; }
      header .headerCenter .blockCenter {
        margin: 0;
        display: inline-block;
        text-align: left;
        height: 100%;
        position: relative;
        min-height: 44px; }
        header .headerCenter .blockCenter p {
          text-align: right;
          font-size: 18px;
          margin: 10px 0;
          display: inline-block;
          width: calc(100% - 30px);
          width: -webkit-calc(100% - 30px);
          padding: 00;
          margin: 0;
          position: absolute;
          bottom: 0; }
      header .headerCenter .blockLeft img {
        width: auto;
        max-width: 100%; }
  .searchAdva {
    margin-top: 10px;
    margin-bottom: 7px; } }

/* -- DEVICES SIZES -- */
.breadcrumbs {
  margin: 0px;
  padding: 30px 0;
  border-top: 2px solid #fff;
  padding-bottom: 0px;
}

.breadcrumbs ul li {
  display: inline-block;
  width: auto;
}

.breadcrumbs .breadcrumbsTitle li p {
  font-size: 14px;
}

.breadcrumbs .breadcrumbsTitle li a {
  font-size: 14px;
  margin-right: 20px;
  position: relative;
  background: #f6f3eb;
  padding: 0px 7px;
  margin-bottom: 5px;
  display: inline-block;
}

.breadcrumbs .breadcrumbsTitle li a:after {
  content: "\f105";
  font-family: 'FontAwesome';
  position: absolute;
  top: 3px;
  right: -15px;
}
@-moz-document url-prefix() {
  .breadcrumbs .breadcrumbsTitle li a:after {
    top: 0px;
  }
}

  .breadcrumbs h2 {
    text-align: left;
    font-size: 35px;
    margin: 0 15px;
    padding: 0;
    width: calc(100% - 30px);
    width: -webkit-calc(100% - 30px);
    border-bottom: 3px solid #d4e3f7;
    padding-bottom: 30px; }

  .breadcrumbs .socialMediaTitle {
    text-align: right; }
    .breadcrumbs .socialMediaTitle ul {
      margin: 0px;
      margin-top: 11px;
      padding: 0px; }
      .breadcrumbs .socialMediaTitle ul li a {
        margin: 0 5px 0 0;
        cursor: pointer; }
        .breadcrumbs .socialMediaTitle ul li a i {
          font-size: 20px;
          width: 40px;
          padding: 9px 5px;
          height: 40px;
          border: 1px solid #cd0080;
          display: inline-block;
          color: #cd0080;
          position: relative;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%;
          text-align: center; }
          .breadcrumbs .socialMediaTitle ul li a i.fa-facebook {
            border-color: #3d5999;
            color: #3d5999; }
          .breadcrumbs .socialMediaTitle ul li a i.fa-twitter {
            border-color: #01abee;
            color: #01abee; }
          .breadcrumbs .socialMediaTitle ul li a i.fa-linkedin {
            border-color: #0076b5;
            color: #0076b5; }
        .breadcrumbs .socialMediaTitle ul li a:hover i,.breadcrumbs .socialMediaTitle ul li a:focus i  {
          color: white;
          background: #cd0080; }
        .breadcrumbs .socialMediaTitle ul li a:hover i.fa-facebook, .breadcrumbs .socialMediaTitle ul li a:focus i.fa-facebook {
          background: #3d5999; }
        .breadcrumbs .socialMediaTitle ul li a:hover i.fa-twitter, .breadcrumbs .socialMediaTitle ul li a:focus i.fa-twitter {
          background: #01abee; }
        .breadcrumbs .socialMediaTitle ul li a:hover i.fa-linkedin, .breadcrumbs .socialMediaTitle ul li a:focus i.fa-linkedin {
          background: #0076b5; }
    .breadcrumbs .socialMediaTitle .tornar {
      color: #cd0080;
      font-family: 'openSans_Bold';
      font-size: 14px;
      margin-top: 10px;
      margin-right: 5px;
      margin-bottom: 15px;
      display: inline-block; }
      .breadcrumbs .socialMediaTitle .tornar i {
        margin-right: 5px;
        font-weight: bold;
        font-size: 20px;
        position: relative;
        top: 2px; }
      .breadcrumbs .socialMediaTitle .tornar:hover, .breadcrumbs .socialMediaTitle .tornar:focus {
        color: #670040;
         text-decoration:underline;
        }
@media (max-width: 47.938em) {
  .breadcrumbs {
    padding: 0px; }
  .breadcrumbsTitle, .socialMediaTitle ul {
    display: none; }
  .socialMediaTitle {
    margin-top: 0px; }
  .breadcrumbs h1 {
    font-family: 'openSans_SemiBold';
    font-size: 20px;
    margin-top: 10px;
    padding-bottom: 20px; }
  .breadcrumbs h2 {
    font-size: 18px;
    margin-bottom: 5px; } }

/* -- DEVICES SIZES -- */
footer {
  background: #434443;
  border-top: 1px solid #c5c5c5;
  border-bottom: 1px solid #c5c5c5;
  padding: 15px 0;
  padding-bottom: 0; }
  footer .blockRight {
    margin-top: -4px;
    text-align: left;
    padding-left: 30px; }
    footer .blockRight p {
      display: inline-block;
      text-align: left;
      font-size: 13px;
      color: #fff; }
  footer .blockLeft a {
    display: inline-block;
    width: 100%; }
  footer .blockFirst li a i {
    display: none; }
  footer .blockCenter li a i {
    color: #cd0080; }
  footer .peuFooter {
    margin: 30px 15px;
    padding-top: 20px;
    border-top: solid 1px #aaa;
    width: calc(100% - 45px);
    width: -webkit-calc(100% - 45px); }
    footer .peuFooter .slideUp {
      color: #aaa;
      display: inline-block;
      float: right;
      line-height: 1;
      padding-left: 35px;
      position: relative; }
      footer .peuFooter .slideUp i {
        position: absolute;
        left: 0px;
        border: 2px solid #aaa;
        -webkit-border-radius: 50%;
        -moz-border-radius: 50%;
        border-radius: 50%;
        padding: 3px;
        font-size: 20px; }
  footer .blockLast {
    padding-right: 0px;
    padding-left: 30px; }
    footer .blockLast .rectFooter {
      background: #615f61;
      -webkit-border-radius: 10px;
      -moz-border-radius: 10px;
      border-radius: 10px;
      font-size: 15px;
      padding: 15px;
      line-height: 1.2;
      color: #ddd;
      margin: 0 15px;
      display: inline-block;
      margin-right: 0px;
      width: calc(100% - 30px);
      width: -webkit-calc(100% - 30px); }
      footer .blockLast .rectFooter img {
        float: left;
        margin-bottom: 5px;
        padding: 10px;
        background: white;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px; }
      footer .blockLast .rectFooter span, footer .blockLast .rectFooter a {
        display: inline-block;
        width: calc(100% - 77px);
        width: -webkit-calc(100% - 77px);
        margin-left: 10px;
        color: white; }
      footer .blockLast .rectFooter a {
        text-decoration: underline;
        margin-left: 76px;
        margin-top: 5px;
        margin-bottom: 5px; }
    footer .blockLast .contactFooter {
      margin-top: 00px; }
      footer .blockLast .contactFooter a {
        color: #fff;
        font-size: 15px;
        line-height: 1; }
        footer .blockLast .contactFooter a .fa-info-circle {
          margin-right: 3px; }
    footer .blockLast .socialMedia {
      text-align: right; }
      footer .blockLast .socialMedia a {
        font-size: 20px;
        margin: 5px;
        margin-top: 10px;
        display: inline-block;
        margin-bottom: 0; }
        footer .blockLast .socialMedia a:hover {
          color: #002c43; }
        footer .blockLast .socialMedia a .fa-twitter {
          background: #45b0e3;
          color: #fff;
          width: 30px;
          padding: 5px 0;
          text-align: center;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%; }
        footer .blockLast .socialMedia a .fa-youtube-play {
          background: #cc0015;
          color: #fff;
          width: 30px;
          padding: 6px 0;
          text-align: center;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%; }
  footer ul li {
    margin: 5px 0px; }
    footer ul li a {
      color: #fff;
      font-size: 15px;
      padding-left: 15px;
      display: inline-block; }
      footer ul li a:hover {
        color: white; }
  footer .siteExt {
    border-top: 3px solid #6a686a;
    padding-top: 30px;
    margin-top: 30px; }
  footer .mapsSite {
    border-top: 1px solid #c5c5c5;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-top: 10px;
    background: #f5f5f5; }
    footer .mapsSite strong {
      font-family: "openSans_SemiBold" !important; }
    footer .mapsSite ul li a {
      color: #333; }
    footer .mapsSite .logoBottom {
      margin-top: 15px;
      border-top: 3px solid #c5c5c5;
      padding-top: 30px; }
      footer .mapsSite .logoBottom p {
        font-size: 12px;
        color: #999;
        max-width: 80%;
        display: inline-block;}
        footer .mapsSite .logoBottom p a{
          color: #666;
          font-weight: bold;}
    footer .mapsSite .mapHiper {
      float: right;
      margin: 0px; }
      footer .mapsSite .mapHiper li {
        display: inline-block;
        margin: 0px; }
        footer .mapsSite .mapHiper li a {
          line-height: 1;
          padding: 0 5px;
          border-right: 1px solid #333;
          font-size: 12px;
          color: #333;
          padding-right: 7px; }

          footer ul.col-sm-3 ul,
          footer ul.col-sm-4 ul{
            padding-left: 0;
          }

.menuFooter {
  display: none; }

@media (max-width: 47.938em) {
  footer .marginTop-10 {
    width: -webkit-calc(100% + 30px);
    width: calc(100% + 30px);
    }
  footer .container {
    position: relative; }
  footer .blockFirst {
    margin-top: 10px;
    width: 100%; }
    footer .blockFirst li {
      margin: 0px;
      padding: 10px 0px; }
      footer .blockFirst li a {
        width: 100%;
        padding-left: 0px;
        padding-right: 25px;
        position: relative; }
        footer .blockFirst li a:hover, footer .blockFirst li a:focus {
          color: #fff; }
        footer .blockFirst li a.active {
          font-family: 'openSans_SemiBold'; }
        footer .blockFirst li a i {
          display: inline-block;
          position: absolute;
          right: 0px;
          top: 2px;
          color: #383a39;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%;
          background: #ecedef;
          width: 20px;
          text-align: center; }
          footer .blockFirst li a i:before {
            font-size: 20px; }
      footer .blockFirst li .menuFooter {
        padding: 0px;
        padding-left: 35px;
        background: #383a39;
        margin-left: -15px;
        width: -webkit-calc(100% + 30px);
        width: calc(100% + 30px);
        margin-top: 10px; }
        footer .blockFirst li .menuFooter li {
          float: none;
          padding: 0;
          border: none; }
          footer .blockFirst li .menuFooter li a {
            padding: 10px 0px; }
      footer .blockFirst li:last-child {
        border-top: solid 1px #aaa; }
  footer .blockLast {
    position: inherit; }
  footer .blockRight {
    margin-top: 0px;
    padding-left: 15px; }
    footer .blockRight p {
      font-size: 15px;
      margin-top: 10px;
      color: #aaa;
      padding-bottom: 10px;
      border-bottom: 1px solid #aaa;
      width: 100%;
      text-align: left; }
    footer .blockRight span {
      width: auto;
      display: inline-block; }
  footer .blockLeft a {
    width: auto; }
    footer .blockLeft a img {
      height: auto; }
  footer .blockCenter {
    display: none;
    padding: 0px;
    padding-left: 35px;
    background: #383a39;
    width: 100%;
    margin-top: 10px; }
    footer .blockCenter li {
      color: #fff;
      margin: 0px;
      padding: 10px 0px; }
      footer .blockCenter li a {
        width: 100%;
        padding-left: 0px;
        padding-right: 25px;
        font-family: 'openSans_regular';
        color: #fff; }
        footer .blockCenter li a.active {
          font-family: 'openSans_SemiBold'; }
  footer .blockLast .rectFooter, footer .blockLast .contactFooter {
    display: none; }
  footer .blockLast .socialMedia {
    text-align: center;
    position: absolute;
    top: 10px;
    right: 0; }
    footer .blockLast .socialMedia a {
      font-size: 25px;
      margin: 5px; }
      footer .blockLast .socialMedia a i {
        font-size: 20px;
        width: 35px; }
  footer .blockLast .languageFooter {
    text-align: center;
    width: 100%; }
    footer .blockLast .languageFooter a {
      color: #fff;
      padding: 0 10px;
      border-left: 1px solid #333;
      background: rgba(255, 255, 255, 0.5);
      border-radius: 100%;
      margin-left: .5em;
      color: #FFF;
      font-weight: normal;
      display: inline;
      font-size: .875em;
      position: relative;
      padding-top: .25em;
      text-align: center;
      line-height: 1.25em;
      padding: 4px 7px 6px;
      font-family: 'openSans_SemiBold'; }
      footer .blockLast .languageFooter a:hover {
        color: black; }
      footer .blockLast .languageFooter a:first-child {
        border: none; }
  footer .siteExt {
    display: none; }
  footer .mapsSite {
    text-align: center; }
    footer .mapsSite .logoBottom {
      text-align: center;
      margin-bottom: 10px; }
      footer .mapsSite .logoBottom a {
        margin-bottom: 10px; }
        footer .mapsSite .logoBottom a img {
          width: auto;
          margin-bottom: 15px; }
    footer .mapsSite .mapHiper {
      text-align: center;
      margin: 0px;
      width: 100%; }
      footer .mapsSite .mapHiper li a {
        line-height: 1.5; }
        footer .peuFooter {
          margin: 30px 0px;
          padding-top: 20px;
          border-top: solid 1px #aaa;
          width: -webkit-calc(100% - 30px);
          width: calc(100% - 45px);
      }}

/* -- DEVICES SIZES -- */
.noticies ul {
  padding: 0px; }

.noticies a {
  color: #106BC4; }
  .noticies a h3 {
    color: #106BC4;
    font-size: 18.5px;
    font-family: 'openSans_SemiBold'; }
  .noticies a:hover h3 {
    color: #106BC4; }
  .noticies a .dateNews {
    background: #fafafa;
    padding: 5px 10px;
    color: #333;
    font-size: 14px;
    border: 1px solid #e2e1e1;
    display: inline-block;
    margin-top: 10px; }

.noticies .seeMore {
  color: #333;
  float: right;
  font-family: 'openSans_SemiBold';
  position: relative;
  padding-right: 20px; }
  .noticies .seeMore:hover {
    color: black; }
  .noticies .seeMore i {
    font-weight: bold;
    font-size: 25px;
    margin-left: 10px;
    position: absolute;
    right: 0px;
    bottom: -2px; }

.listNews {
  margin: 50px 0;
  margin-top: 10px; }
  .listNews li {
    margin: 0 15px;
    padding: 0;
    width: calc(25% - 30px);
    width: -webkit-calc(25% - 30px);
    border-bottom: 1px solid #e2e1e1;
    max-height: inherit;
    display: inline-block;
    margin-bottom: 30px; }
    .listNews li a {
      padding-bottom: 20px;
      display: inline-block;
      width: 100%; }
      .listNews li a:focus  {
      color:#106BC4;
     }
      .listNews li a h2 {
        color: #106BC4;
        font-size: 15px;
        display: inline-block;
        font-family: 'openSans_SemiBold'; }
      .listNews li a:hover h2 {
        color: #004e76; }
      .listNews li a p {
        color: #8c8a8a;
        font-size: 13px;
        display: inline-block; }
      .listNews li a:hover {
        text-decoration: none !important; }
        .listNews li a:hover h3 {
          text-decoration: underline !important; }

.paginationNews {
  width: 100%;
  display: inline-block;
  margin-top: 30px;
  text-align: center; }
  .paginationNews ul {
    border-bottom: none !important; }
  .paginationNews li {
    margin: 0 10px;
    padding: 0;
    width: auto;
    border: none;
    display: inline-block;
    border-bottom: none !important; }
    .paginationNews li a {
      font-size: 20px; }
      .paginationNews li a.next {
        padding-right: 20px; }
        .paginationNews li:first-child a:hover,
        .paginationNews li:last-child a:hover {
          text-decoration: none !important;
        }
        .paginationNews li:first-child a:hover span,
        .paginationNews li:last-child a:hover span {
          text-decoration: underline !important;
        }
        .paginationNews li a:hover.prev.desactive span,
        .paginationNews li a:hover.next.desactive span,
        .paginationNews li a:hover.active{
          text-decoration: none !important;
        }
        .paginationNews ul li a:hover, .paginationNews ul li a:focus {
          text-decoration: none;
        }
        .paginationNews li a.next span {
          margin-right: 20px; }
        .paginationNews li a.next:after {
          content: "\f105";
          color: #cd0080;
          font-family: "fontawesome";
          font-size: 24px;
          font-weight: bold;
          position: relative;
          top: 2px; }
      .paginationNews li a.prev {
        padding-left: 20px; }
        .paginationNews li a.prev span {
          margin-left: 20px; }
        .paginationNews li a.prev:before {
          content: "\f104";
          color: #cd0080;
          font-family: "fontawesome";
          font-size: 24px;
          font-weight: bold;
          position: relative;
          top: 2px; }
      .paginationNews li a.active, .paginationNews li a.desactive {
        color: #e2e1e1; }
        .paginationNews li a.active:before, .paginationNews li a.active:after, .paginationNews li a.desactive:before, .paginationNews li a.desactive:after {
          color: #e2e1e1; }
        .paginationNews li a.active:hover, .paginationNews li a.desactive:hover {
          color: #e2e1e1;
          cursor: default; }
      .paginationNews li a:hover {
        color: #002c43; }

.paginacio {
  width: 100%;
  display: inline-block;
  margin-top: 30px;
  text-align: center;
  padding: 0;
  float: none; }
  .paginacio ul {
    border-bottom: none !important;
    border: none; }
  .paginacio li {
    margin: 0 5px;
    padding: 0;
    width: auto;
    border: none;
    display: inline-block;}

  .paginacio li:has(a:focus) {
    border: 2px solid;
    border-color: #cd0080;
    border-radius: 5px;
  }

  .paginacio li:has(last-child a:focus) {
    border: 2px solid;
    border-color: #cd0080;
    border-radius: 5px;
  }
  .pagination li:first-child a.desactivat:hover, .pagination li:first-child a.desactivat, .paginacio li:first-child a.desactivat:before{
    color:#333 !important;
  }
    .paginacio li:first-child a {
      background: none !important;
      position: relative;}
      .paginacio li:first-child a:before {
       content: "\f104";
        font-family: 'FontAwesome';
        position: absolute;
        left: 0px;
        font-size: 25px;
        font-weight: bold;
        top: -3px;
        color: #cd0080; }
    .paginacio li:last-child a {
      background: none !important;
      position: relative; }
      .paginacio li:last-child a:before {
        content: "\f105";
        font-family: 'FontAwesome';
        position: absolute;
        right: 0;
        font-size: 25px;
        font-weight: bold;
        top: -3px;
        color: #cd0080; }
    .paginacio li .desactivat:before {
      color: #ddd !important; }
    .paginacio li .actiu {
      color: #333 !important; }
    .paginacio li .actiu2, .paginacio li a.actiu2:hover {
      cursor: context-menu;
      color: #333 !important;
      background-color: #eee !important;
      font-weight: 600;
      padding: 0 10px;
      text-decoration: none !important;
    }
    .paginacio li a {
      font-size: 20px;
      color: #106BC4;
      padding: 0 5px; }
      .paginacio li a:hover {
        text-decoration: underline !important;}
      .paginacio li a:focus {
        text-decoration: underline !important;
        background-color: white !important;
        margin: 0px !important}
      .paginacio li a.next {
        padding-right: 20px; }
        .paginacio li a.next span {
          margin-right: 20px; }
        .paginacio li a.next:after {
          content: "\f105";
          color: #cd0080;
          font-family: "fontawesome";
          font-size: 24px;
          font-weight: bold;
          position: relative;
          top: 2px; }
      .paginacio li a.prev {
        padding-left: 20px; }
        .paginacio li a.prev span {
          margin-left: 20px; }
        .paginacio li a.prev:before {
          content: "\f104";
          color: #cd0080;
          font-family: "fontawesome";
          font-size: 24px;
          font-weight: bold;
          position: relative;
          top: 2px; }
      .paginacio li a.active, .paginacio li a.desactive {
        color: #e2e1e1; }
        .paginacio li a.active:before, .paginacio li a.active:after, .paginacio li a.desactive:before, .paginacio li a.desactive:after {
          color: #e2e1e1; }
        .paginacio li a.active:hover, .paginacio li a.desactive:hover {
          color: #e2e1e1;
          cursor: default; }
      .paginacio li a:hover {
        color: #cd0080 !important; }
        .pagination>li>a, .pagination>li>span {
          font-family: 'openSans_Regular';
        font-size: 20px;
        color: #cd0080 !important;
        background-color: white !important;}
@media (max-width: 47.938em) {
  .noticies a {
    color: #5788d5;
    width: 100%;
    display: inline-block; }
  .noticies .seeMore {
    width: auto; }
  .listNews .listBlockNews li {
    width: -webkit-calc(100% - 30px);
    width: calc(100% - 30px);
    height: auto !important; }
  .listNews .paginationNews li {
    margin: 0 5px;
    width: auto; }
    .listNews .paginationNews li span {
      display: none; }

    }
/* -- DEVICES SIZES -- */
  @media (min-width: 48.000em) {
    .bgGrey {
      background: #f5f5f5;
      border-top: 1px solid #f5f5f5;
      border-bottom: 1px solid #f5f5f5;
      padding: 50px 0; }

  }
  @media (max-width: 47.950em) {
    .bgGrey {
      padding: 20px 0; } }

section.undefined {
  background: #f5f5f5;
  border-top: 1px solid #f5f5f5;
  border-bottom: 1px solid #f5f5f5;
  padding: 50px 0; }
  @media (max-width: 47.938em) {
    section.undefined {
      padding: 20px 0; } }

.imgList h2 {
  margin-bottom: 10px;
  width: 100%;
  color: #595853; }


  .imgList .blockImgList a:hover {
    color: black; }
  .imgList .blockImgList:after {
    content: "";
    background-image: url(../img/somBox.png);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 100%;
    position: absolute;
    bottom: -15px;
    height: 15px; }

.imgList .firstBlockImgList {
  border: none;
  padding-left: 0px;
  margin-left: 0px;
  position: relative;
  background: #106BC4; }
  .imgList .firstBlockImgList a {
    padding: 10px 20px;
    width: 100%;
    display: table-cell;
    color: #fff;
    font-size: 22px;
    height: 230px;
    vertical-align: middle;
    font-family: 'openSans_light'; }
    .imgList .firstBlockImgList a:focus {
      text-decoration: underline;}
    .imgList .firstBlockImgList a span {
      display: block;
      color: #fff;
      font-size: 30px;
      font-family: 'openSans_Bold'; }
    .imgList .firstBlockImgList a:hover {
      color: #fff; }
  .imgList ul li {
    padding: 10px 0px;
    font-size: 15px;
    color: #999;
    position: relative;
    line-height: 1.2;
    border-bottom: 2px solid #ddd; }
    .imgList ul li:last-child {
      border: none; }
    .imgList ul li .colorPink {
      color: #cd0080; }
      .imgList ul li .colorPink:hover {
        color: #7b256f; }
    .imgList ul li a {
      color: #333; }
    @media (min-width: 48.000em) {
      .imgList ul {
        width: 100%;
        padding: 20px 30px;
        background: white;
        height: 230px;
        margin: 0; }
      .imgList .blockImgList {
        border-left: 1px solid #ecedef;
        height: 230px;
        width: 32%;
        width: -webkit-calc(33.33333333333% - 10px);
        width: calc(33.33333333333333% - 10px);
        margin: 0;
        padding: 0;
        margin-right: 10px;
        position: relative; }
    }
      @media (max-width: 992px) {
        .imgList ul li a {
          font-size: 14px; } }
    .imgList ul li i {
      display: none;
      color: #cd0080;
      font-size: 10px;
      position: absolute;
      left: 0px;
      top: 18px; }

@media (min-width: 1200px) {
  .imgList .blockImgList:first-child {
    width: -webkit-calc(33% - 25px);
    width: calc(33% - 25px);
    margin-right: 10px; } }

@media (max-width: 47.950em) {
  .imgList {
    border: none;
    padding: 0;
    margin-bottom: 10px; }
    .imgList h2 {
      padding: 0 15px; }
      .imgList ul {
        width: 100%;
        padding: 20px 0px; }
    .imgList .blockImgList:last-child {
      margin-top: -10px; }
      .imgList .blockImgList:last-child li:last-child {
        border: none; }
    .imgList .blockImgList {
      height: auto;
      margin: 10px 0px;
      width: 100%;
      border-left: 0;
      margin-top: 0; }
      .imgList .blockImgList:after {
        display: none; }
    .imgList .firstBlockImgList {
      margin-top: 10px; }
    .imgList ul {
      height: auto;
      padding: 0px 5px;
      background: transparent;
      width: 100%;
      margin: 0; }
      .imgList ul li:last-child {
        border-bottom: 2px solid #ddd; } }

/* -- DEVICES SIZES -- */
.directTo {
  margin-top: 20px;
  margin-bottom: 20px; }
  .directTo .veuresMes {
    float: right;
    margin-top: 10px;
    color: #cd0080; }
    .directTo .veuresMes i {
      font-size: 25px;
      position: relative;
      top: 2px;
      margin-left: 5px; }
  .directTo .blocksDirect {
    display: inline-block;
    width: 100%;
    margin-top: 10px; }
    .directTo .blocksDirect .blockDirect {
      height: 170px;
      display: inline-block;
      width: calc(16.6% - 12px);
      width: -webkit-calc(16.6% - 12px);
      padding: 5px;
      margin-right: 15px; }
      .directTo .blocksDirect .blockDirect:hover{
      background: #106BC4;
      }
      .directTo .blocksDirect .blockDirect:last-child {
        margin: 0; }
      .directTo .blocksDirect .blockDirect:hover a {
        color: #ecedef;
        text-decoration: none !important; }
        .directTo .blocksDirect .blockDirect:hover a i {
          border: 3px solid #ecedef; }
          .directTo .blocksDirect .blockDirect:hover a i:before {
            color: #ecedef; }
      .directTo .blocksDirect .blockDirect a {
        height: 100%;
        width: 100%;
        display: inline-block;
        text-align: left;
        padding: 10px;
        color: #106BC4;
        font-family: 'openSans_SemiBold';
        border-top: 3px solid #106BC4;
        padding-top: 20px; }
        .directTo .blocksDirect .blockDirect a:hover {
          background: #106BC4;
          color: #fff;
          border-top: 3px solid #fff;
          text-decoration: none !important; }
          .directTo .blocksDirect .blockDirect a:hover i {
            border: 3px solid #ecedef; }
          .directTo .blocksDirect .blockDirect a:hover p {
            text-decoration: underline !important; }
          .directTo .blocksDirect .blockDirect a:focus p {
            text-decoration: underline !important; }
        .directTo .blocksDirect .blockDirect a i {
          width: 70px;
          height:70px;
          display: inline-block;
          -webkit-border-radius: 100%;
          -moz-border-radius: 100%;
          border-radius: 100%;
          border: 3px solid #106BC4;
          float: none;
          font-size: 30px;
          background: #106BC4;
          color: #fff;
          text-align: center;
          padding: 10px 0; }
          .directTo .blocksDirect .blockDirect a i.marginLeft:before {
            left: 2px;
            position: relative; }
        .directTo .blocksDirect .blockDirect a p {
          display: table;
          width: 100%;
          height: 40px;
          line-height: 1;
          margin: 0;
          margin-top: 5px; }
          .directTo .blocksDirect .blockDirect a p span {
            display: table-cell;
            vertical-align: middle;
            font-size: 20px;
            line-height: 1.3; }

@media (max-width: 992px) {
  .directTo .blocksDirect .blockDirect {
    margin: 0;
    width: 16.6%;
    font-size: 18px;
    border-bottom: 2px solid #fff; }
    .directTo .blocksDirect .blockDirect a p span {
      font-size: 16px; } }

@media (max-width: 47.950em) {
  .directTo{
    padding: 0 15px;
  }
  .directTo .blocksDirect .blockDirect {
    width: 33.33%;
    padding: 5px; }
    .directTo .blocksDirect .blockDirect a {
      text-align: center; } }

@media (max-width: 430px) {
  .directTo {
    margin-top: 0;
    margin-bottom: 40px; }
    .directTo .veuresMes {
      margin: 0px;
      margin-bottom: 10px; }
  .directTo .blocksDirect .blockDirect {
    width: 50%;
    padding: 5px; }
    .directTo .blocksDirect .blockDirect a {
      text-align: center; } }

/* -- DEVICES SIZES -- */
.sliderHome:after {
  content: "";
  background-image: url(../img/somBox.png);
  background-size: 100%;
  background-repeat: no-repeat;
  width: 100%;
  position: absolute;
  bottom: -15px;
  height: 15px; }

.sliderHome h2 a {
  bottom: 0;
  position: absolute;
  z-index: 0;
  color: white;
  font-size: 25px;
  padding: 0 20px;
  width: 100%;
  color: #fff;
  /* Permalink - use to edit and share this gradient: http://colorzilla.com/gradient-editor/#000000+0,000000+14&1+0,0+72 */
  background: -moz-radial-gradient(center, ellipse cover, black 0%, rgba(0, 0, 0, 0.81) 14%, transparent 72%);
  /* FF3.6-15 */
  background: -webkit-radial-gradient(center, ellipse cover, black 0%, rgba(0, 0, 0, 0.81) 14%, transparent 72%);
  /* Chrome10-25,Safari5.1-6 */
  background: radial-gradient(ellipse at center, black 0%, rgba(0, 0, 0, 0.81) 14%, transparent 72%);
  /* W3C, IE10+, FF16+, Chrome26+, Opera12+, Safari7+ */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#000000', endColorstr='#00000000',GradientType=1 );
  /* IE6-9 fallback on horizontal gradient */
  font-family: 'openSans_SemiBold'; }
  .sliderHome h2 a span {
    font-family: 'openSans_light'; }

.sliderHome .carousel-inner .item {
  height: 365px;
  background-size: cover;
  background-position: center;
  border: 1px solid #ecedef;
  border-bottom: none; }
    .sliderHome .carousel-inner .item > a > div{
        width:100%;
        height:100%;
    }
.buttonSlider {
  position: relative;
  display: inline-block;
  width: 100%;
  border: 1px solid #ecedef; }
  .buttonSlider .buttonLeft {
    display: inline-block; }
    .buttonSlider .buttonLeft a {
      opacity: 1;
      background: transparent; }
    .buttonSlider .buttonLeft i {
      font-size: 55px;
      font-weight: normal;
      border: 2px solid #fff;
      width: 60px;
      height: 60px;
      position: relative;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%;
      opacity: 1; }
      .buttonSlider .buttonLeft i:before {
        position: absolute;
        left: 17px; }
  .buttonSlider .buttonRight {
    position: absolute;
    top: 25%;
    right: -15px;
    z-index: 1;
    display: inline-block; }
    .buttonSlider .buttonRight a {
      opacity: 1;
      background: transparent; }
    .buttonSlider .buttonRight i {
      font-size: 55px;
      font-weight: normal;
      border: 2px solid #cd0080;
      width: 60px;
      height: 60px;
      position: relative;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%;
      color: #cd0080; }
      .buttonSlider .buttonRight i:before {
        position: absolute;
        right: 17px; }
  .buttonSlider .buttonCircle {
    display: inline-block;
    width: 100%; }
    .buttonSlider .buttonCircle ol {
      display: inline-block;
      height: 0;
      position: relative;
      margin: 0;
      width: auto;
      margin-left: 0;
      left: 0;
      text-align: left;
      bottom: 0;
      height: auto;
      line-height: 1;
      z-index:0;
      margin: 15px;
      margin-bottom: 20px;
      margin-left: 20px; }
      .buttonSlider .buttonCircle ol li {
        background: #106BC4;
        border: none;
        width: 7px;
        height: 7px;
        margin: 0 3px; }
        .buttonSlider .buttonCircle ol li.active {
          background: #cd0080; }
    .buttonSlider .buttonCircle .pauseSlider {
      background: transparent;
      position: relative;
      left: 20px;
      border: 2px solid #fff;
      text-align: center;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      color: white;
      font-size: 10px;
      display: inline-block;
      padding-top: 3px;
      bottom: 4px; }
      .buttonSlider .buttonCircle .pauseSlider a {
        position: absolute; }
        .tabs .nav-tabs li a:hover,  .tabs .nav-tabs li a:focus{
          background: #106BC4 !important;
          text-decoration: underline;
          color:#fff;
        }
        .tabs .nav-tabs li.active a:hover,  .tabs .nav-tabs li.active a:focus{
          color:#000;
        }
.tabs {
  border: 1px solid #ecedef;
  position: relative;
  min-height: 418px; }
  .tabs:after {
    content: "";
    background-image: url(../img/somBox.png);
    background-size: 100%;
    background-repeat: no-repeat;
    width: 100%;
    position: absolute;
    bottom: -16px;
    height: 15px; }
  .tabs ul li {
    width: 50%;
    border: none; }
    .tabs ul li a {
      font-family: 'openSans_SemiBold';
      padding: 10px 14px;
      width: 100%;
      background: #106BC4;
      color: #fff;
      border: none; }
      .tabs ul li a:hover, .tabs ul li a:focus {
        border: none !important;
        background: white !important;
        text-decoration: underline;
        font-family: 'openSans_SemiBold';
        color: black;      }


  .tabs ul li.active {
    border: none; }
    .tabs ul li.active a {
      background: #fff;
      color: #333;
      font-family: 'openSans_SemiBold';
      border: none; }
      .tabs ul li.active a:hover, .tabs ul li.active a:focus {
        border: none !important;
        background: #fff !important;}
  .tabs .tab-content {
    margin-top: 20px;
    border-top: 1px solid #ecedef; }
    .tabs .tab-content .hiperHomeMobil {
      background: #666;
      padding: 10px 10px;
      color: white;
      font-family: "open";
      font-family: 'openSans_SemiBold';
      -webkit-border-top-left-radius: 7px;
      -webkit-border-top-right-radius: 7px;
      -moz-border-radius-topleft: 7px;
      -moz-border-radius-topright: 7px;
      border-top-left-radius: 7px;
      border-top-right-radius: 7px;
      border: none;
      margin-top: 10px;
      margin-bottom: 00px; }
      .tabs .tab-content .hiperHomeMobil i {
        float: right;
        border-radius: 50%;
        background: #ecedef;
        color: #666460;
        width: 20px;
        text-align: center;
        font-size: 20px; }
    .tabs .tab-content .borderRadiusOnly {
      -webkit-border-radius: 7px;
      -moz-border-radius: 7px;
      border-radius: 7px; }
    .tabs .tab-content ul {
      padding: 0;
      margin: 0;
      display: inline-block;
      width: 100%; }
      .tabs .tab-content ul li {
        width: 100%;
        display: inline-block;
        padding-left: 40px;
        position: relative; }
        .tabs .tab-content ul li a {
          line-height: 1;
          display: inline-block;
          width: 100%;
          font-size: 15px;
          background: none;
          color: #333;
          padding: 12px 15px;
          font-family: 'openSans_SemiBold';
          line-height: 1.4;
          position: relative; }
          .tabs .tab-content ul li a span {
            font-family: 'openSans_regular';
            font-size: 14px;
            width: 100%;
            display: inline-block; }
          .tabs .tab-content ul li a:hover {
            background: none !important; }
        .tabs .tab-content ul li span.fa-icon-after:after {
          content: "";
          width: 95%;
          position: absolute;
          height: 1px;
          left: 2.5%;
          background: #ecedef;
          bottom: 0; }
        .tabs .tab-content ul li span.fa-icon-before:before {
          font-family: 'FontAwesome';
          content: "\f105";
          position: absolute;
          top: 15px;
          border: 1px solid #106BC4;
          -webkit-border-radius: 50%;
          -moz-border-radius: 50%;
          border-radius: 50%;
          width: 20px;
          text-align: center;
          height: 20px;
          color: #106BC4;
          left: 20px;
          padding: 2px 0px 0px 2px;}
       .tabs .tab-content ul li:last-child {
          padding-left: 25px;
          border-top: 1px solid #ecedef; }
          .tabs .tab-content ul li:last-child a {
            color: #cd0080;
            padding: 20px 0;
            font-family: 'openSans_SemiBold';
            text-underline-offset: 3px;
            text-decoration-thickness: 0.9px !important;
          }
          .tabs .tab-content ul li:last-child a:hover {
            text-decoration-style: solid !important;
          }
           .tabs .tab-content ul li:last-child a:focus {
            outline: 5px auto;
            outline-offset: 4px;
            padding: 0;
            width: auto;
            text-decoration-style: solid !important;
            outline-color: #cd0080;
            margin: 20px 0;
            background: none !important;
          }
          .tabs .tab-content ul li:last-child:before, .tabs .tab-content ul li:last-child:after {
            display: none; }

@media (max-width: 992px) {
  .sliderHome .buttonSlider .buttonRight {
    left: -webkit-calc(100% - 70px);
    left: calc(100% - 70px);}
  .sliderHome .buttonSlider .buttonCircle {
    width: 100%;
    left: 0; }
  .sliderHome .container h2 {
    margin-left: 12%;
    margin-right: 12%;
    padding: 0;
    left: 0;
    z-index: 3; }
  .vistsMoth {
    float: left;
    height: inherit;
    margin-top: 0px;
    margin: 0px;
    width: 100%; }
    .vistsMoth .blockVisitMoth {
      height: inherit;
      margin: 0px;
      width: 100%;
      padding: 20px 0; } }

@media (max-width: 47.938em) {
  .sliderHome {
    margin-top: -5px; }
  .block-6 {
    margin-bottom: 30px; }
    .block-6 .col-md-6 {
      padding: 0px; }
      .block-6 .col-md-6 .tabs {
        padding: 0 10px;
        margin-top: 30px;
        border: none;
        height: auto;
        min-height: inherit; }
        .block-6 .col-md-6 .tabs .tab-content {
          border: none; }
          .block-6 .col-md-6 .tabs .tab-content .tab-pane {
            border: 1px solid #e2e0db;
            -webkit-border-bottom-right-radius: 7px;
            -webkit-border-bottom-left-radius: 7px;
            -moz-border-radius-bottomright: 7px;
            -moz-border-radius-bottomleft: 7px;
            border-bottom-right-radius: 7px;
            border-bottom-left-radius: 7px; }
          .block-6 .col-md-6 .tabs .tab-content ul li:after {
            width: 90%;
            left: 5%; }
          .block-6 .col-md-6 .tabs .tab-content ul li:last-child {
            border: none;
            padding-right: 35px;
            text-align: right;
            position: relative; }
            .block-6 .col-md-6 .tabs .tab-content ul li:last-child a:after {
              content: "\f105";
              font-family: 'FontAwesome';
              position: absolute;
              top: 12px;
              right: -20px;
              font-size: 26px; }
        .block-6 .col-md-6 .tabs .nav-tabs {
          display: none; } }

/* -- DEVICES SIZES -- */
.block8-4 {
  margin: 0px 0 0 0;
  margin-bottom: 50px; }
  .block8-4 .blockLeftWrapper h2 {
    color: #595853;
    font-size: 20px;
    font-family: 'openSans_semiBold';
    line-height: 1.5;
    margin-bottom: 30px;
    display: inline-block; }
  .block8-4 .blockLeftWrapper img {
    display: inline-block;
    width: 100%;
    margin-top: 20px; }
  .block8-4 .blockLeftWrapper img.widthResponsiveATC{
      width:auto;
  }
  @media (max-width: 40.000em) {
  .block8-4 .blockLeftWrapper img.widthResponsiveATC{
      width:100%;
      height: auto;
  }
  .blockDespleInfo .contentInfo iframe{
    width:100%;
  }
}
  .block8-4 .blockLeftWrapper .pieFoto {
    display: inline-block;
    width: 100%;
    margin-bottom: 30px;
    font-size: 12px; }
  .block8-4 .blockLeftWrapper p {
    font-size: 15px;
    line-height: 1.5;
    margin-bottom: 10px;
    color: #595853; }
    .block8-4 .blockLeftWrapper p a {
      color: #cd0080;
      font-family: 'openSans_Regular';
      text-decoration: underline !important;
      text-decoration-style: dashed !important;
      text-underline-offset: 3px;
      text-decoration-thickness: 0.9px !important; }
      .block8-4 .blockLeftWrapper p a:focus {
      text-decoration-style: solid !important;}
      .block8-4 .blockLeftWrapper p a:hover {
      text-decoration-style: solid !important;}
    .block8-4 .blockLeftWrapper p strong {
      font-family: "openSans_Bold" !important; }
    .block8-4 .blockLeftWrapper p small {
      color: #595853;
      font-size: 12px;
      font-family: 'openSans_semiBold'; }
  .block8-4 .blockLeftWrapper .depleInfo .blockDespleInfo .contentInfo {
    padding-left: 40px; }
  .block8-4 .blockRightWrapper .mesInfoList {
    background: #106BC4; }
    .block8-4 .blockRightWrapper .mesInfoList .titleInfo {
      color: white;
      font-size: 20px;
      padding: 20px;
      margin-bottom: 0; }
    .block8-4 .blockRightWrapper .mesInfoList ul li {
      border: none;
      border-top: 2px solid #a4c7ee; }
      .block8-4 .blockRightWrapper .mesInfoList ul li:last-child {
        border-top: 2px solid #a4c7ee; }
      .block8-4 .blockRightWrapper .mesInfoList ul li a {
        padding: 10px 0px;
        padding-bottom: 15px; }
        .block8-4 .blockRightWrapper .mesInfoList ul li a:focus, .block8-4 .blockRightWrapper .mesInfoList ul li a:hover {
       text-decoration: underline; }
        .block8-4 .blockRightWrapper .mesInfoList ul li a span {
          font-size: 14px;
          font-family: 'openSans_regular';
          line-height: 1.2; }
        .block8-4 .blockRightWrapper .mesInfoList ul li a p {
          margin-top: 10px;
          margin-left: 0px;
          left: 0px; }
  .block8-4 .blockRightWrapper ul {
    background: #106BC4;
    padding: 0 20px;
    margin-bottom: 40px; }
    .block8-4 .blockRightWrapper ul li {
      border-bottom: 2px solid #a4c7ee;
      padding: 0;}
      .block8-4 .blockRightWrapper ul li:last-child {
        border: none;
        margin-bottom: 0 !important; }
  .block8-4 .blockRightWrapper a {
    width: 100%;
    color: #fff;
    display: inline-block;
    padding: 15px 0px;
    font-size: 20px;
    font-family: "openSans_semiBold" !important;
    position: relative;
    padding-bottom: 20px; }
  .block8-4 .blockRightWrapper a:hover span,.block8-4 .blockRightWrapper a:focus span{
     text-decoration: underline !important;
  }
    .block8-4 .blockRightWrapper a .arrowBlock {
      position: relative;
      font-size: 20px;
      display: table;
      width: 24px;
      height: 20px;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%;
      border: 2px solid;
      text-align: center;
      float: left;
      margin-top: 10px; }
      .block8-4 .blockRightWrapper a .arrowBlock:before {
        content: "\f105";
        display: table-cell;
        vertical-align: middle;
        position: relative;
        top: -1px;
        left: 2px; }
    .block8-4 .blockRightWrapper a i + span {
      padding-left: 15px;
      display: inline-block;
      width: calc(100% - 40px);
      width: -webkit-calc(100% - 40px);
      position: relative;
      top: 7px; }
    .block8-4 .blockRightWrapper a p {
      display: block;
      margin-top: 15px;
      margin-left: 15px;
      font-size: 14px;
      color: #e4edfa;
      position: relative;
      width: calc(100% - 57px);
      width: -webkit-calc(100% - 57px);
      left: 42px;
      margin-bottom: 0px; }
      .block8-4 .blockRightWrapper a p i {
        font-size: 25px;
        margin-right: 15px; }
  .block8-4 .blockRightWrapper .blockBlueRight {
    background: #106BC4;
    color: #fff; }
    .block8-4 .blockRightWrapper .blockBlueRight span i {
      position: relative;
      font-size: 20px;
      left: 5px;
      top: 0; }
  .block8-4 .blockRightWrapper .blockBlueRight + .infoList {
    margin-top: 20px; }
  .block8-4 .blockRightWrapper .infoList {
    border: 1px solid #c5c5c5;
    padding: 25px;
    padding-bottom: 20px; }
    .block8-4 .blockRightWrapper .infoList .titleInfo {
      font-family: "openSans_semiBold" !important;
      color: #106BC4;
      font-size: 18.5px; }
    .block8-4 .blockRightWrapper .infoList ul {
      padding: 0px;
      margin: 0px;
      background: none; }
      .block8-4 .blockRightWrapper .infoList ul li {
        padding: 0px;
        background: none;
        border: none; }
        .block8-4 .blockRightWrapper .infoList ul li a {
          padding: 5px 0px;
          padding-left: 25px;
          font-size: 14px;
          color: #333;
          position: relative;
          line-height: 1.2;
          border: none;
          background: none;
          margin-bottom: 5px; }
          .block8-4 .blockRightWrapper .infoList ul li a i {
            color: #106BC4;
            font-size: 7px;
            position: absolute;
            left: 0px;
            top: 10px;
            display: inline-block; }
          .block8-4 .blockRightWrapper .infoList ul li a span {
            width: 100%;
            display: inline-block; }
            .block8-4 .blockRightWrapper .infoList ul li a span.icon-destaquem {
                top: 0;
                width: auto;
                padding-left: 0;}
            .block8-4 .blockRightWrapper .infoList ul li a span i {
              position: relative;
              font-size: 25px;
              color: #106BC4;
              margin-right: 10px;
              top: 4px; }

@media (max-width: 47.938em) {
  .block8-4 {
    margin: 20px 0; }
  .block8-4 .blockLeftWrapper h2 {
    font-size: 18px;
    line-height: 1.3; }
  .block8-4 .blockRightWrapper ul {
    margin-bottom: 0px; }
  .block8-4 .blockRightWrapper .infoList ul li a {
    font-family: "openSans_regular" !important;
    font-size: 14px; }
    .block8-4 .blockRightWrapper .infoList ul li a span {
      margin-top: 5px; }
      .block8-4 .blockRightWrapper .infoList ul li a span i {
        font-size: 15px;
        top: 0;
        color: #cd0080; } }

/* -- DEVICES SIZES -- */
.blocksList {
  display: inline-block;
  width: calc(100% - 30px);
  width: -webkit-calc(100% - 30px);
  margin: 50px 15px; }
  .blocksList .blockListSingle {
    border: 1px solid #d4d5d7;
    padding: 25px;
    margin: 2px;
    width: calc(33.33333333333333% - 4px);
    width: -webkit-calc(33.33333333333333% - 4px);
    display: inline-block; }
    .blocksList .blockListSingle h3 {
      margin-top: 0px;
      font-size: 18.5px;
      color: #106BC4;
      line-height: 1.2;
      font-family: 'openSans_SemiBold';
      margin-bottom: 15px;
      min-height: 42px; }
      .blocksList .blockListSingle a:focus h3 {
        text-decoration: underline;
      }
    .blocksList .blockListSingle p {
      font-size: 15px;
      color: #595853; }
    .blocksList .blockListSingle ul {
      padding: 0px;
      margin: 0px;
      display: inline-block;
      width: 100%;
      margin-bottom: 10px;
      padding-left: 10px; }
      .blocksList .blockListSingle ul li {
        padding: 5px 0px;
        padding-left: 20px;
        font-size: 14px;
        color: #333;
        position: relative;
        line-height: 1.2;
        border: none; }
        .blocksList .blockListSingle ul li i {
          color: #106BC4;
          font-size: 7px;
          position: absolute;
          left: 5px;
          top: 10px; }
    .blocksList .blockListSingle:hover {
      background: #106BC4;
      text-decoration: none !important; }
      .blocksList .blockListSingle:hover * {
        color: #fff !important; }

@media (max-width: 47.938em) {
  .blocksList {
    margin-bottom: 50px; }
    .blocksList .blockListSingle {
      width: 100%;
      height: inherit !important;
      margin: 10px 0px; } }

.bolck12Supe {
  margin-top: 0px; }
  .bolck12Supe h2 {
    font-size: 16px;
    color: #666666;
    line-height: 1.5; }
  .bolck12Supe p {
    font-size: 12px;
    font-family: 'openSans_Bold';
    margin-top: 10px;
    margin-bottom: 0px;
    display: inline-block;
    border-bottom: 1px solid #c5c5c5;
    width: 100%;
    padding-bottom: 20px;
    color: #666666; }

@media (max-width: 47.938em) {
  .bolck12Supe {
    margin: 0; } }

/* -- DEVICES SIZES -- */
.depleInfo .blockDespleInfo {
  display: inline-block;
  width: 100%;
  padding: 0px;
list-style: none;}
  .depleInfo .blockDespleInfo li {
    padding-top: 10px;
    margin-bottom: 15px;
    border-top: 1px solid #d4e3f7; }
  .depleInfo .blockDespleInfo li:first-child .contentInfo {
    display: none; }
  .depleInfo .blockDespleInfo li ul, .depleInfo .blockDespleInfo li ol{
    margin-bottom: 10px;
  }
  .depleInfo .blockDespleInfo li ul li, .depleInfo .blockDespleInfo li ol li{
    border:0;
    margin:0;
    padding:0 0 6px 0;
    font-size:15px;
  }
  .depleInfo .blockDespleInfo li ul li ul, .depleInfo .blockDespleInfo li ol li ol, .depleInfo .blockDespleInfo li ul li ol, .depleInfo .blockDespleInfo li ol li ul{
     margin-top:10px;
    }

  .blockLeftWrapper ul{
    list-style: disc;
  }
  .blockLeftWrapper ul li, .blockLeftWrapper ol li{
    margin:0;
    padding-bottom: 6px;
    font-size: 15px;
  }
  .blockLeftWrapper ul li ul, .blockLeftWrapper ol li ol, .blockLeftWrapper ul li ol, .blockLeftWrapper ol li ul{
      margin-top:10px;
  }
  .depleInfo .blockDespleInfo h3 {
    margin: 0px;
    position: relative; }
.depleInfo .blockDespleInfo .hiperDesple {
    font-family: "openSans_SemiBold";
    font-size: 18px;
    color: #333;
    display: block;
    margin-left: 2.2em;
    line-height: 1.5;
    text-decoration:none !important;
}
.depleInfo .blockDespleInfo .hiperDesple:hover,  .depleInfo .blockDespleInfo .hiperDesple:focus{
    text-decoration:underline !important;
}
    .depleInfo .blockDespleInfo .hiperDesple i {
      background: #cd0080;
      color: white;
      width: 25px;
      height: 25px;
      text-align: center;
      -webkit-border-radius: 50%;
      -moz-border-radius: 50%;
      border-radius: 50%;
      font-size: 25px;
      top: 2px;
      margin-right: 15px;
      position: absolute;
      left: 0; }
      .depleInfo .blockDespleInfo .hiperDesple i:before {
        position: relative;
        top: -1px; }
    .depleInfo .blockDespleInfo .hiperDesple .fa-angle-down:before {
      top: 1px; }
  .depleInfo .blockDespleInfo .contentInfo {
    padding-left: 40px;
    margin-top: 20px;
    display: none; }
    .depleInfo .blockDespleInfo .contentInfo p {
      margin-bottom: 10px;
      font-size: 15px; }
    .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore {
      text-align: left;
      margin-top: 20px; }
      .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore .seeMore {
        font-family: "openSans_Regular";
        font-size: 15px;
        color: #333;
        display: inline-block;
        color: #cd0080;
        text-decoration: underline !important;
        text-decoration-style: dashed !important;
        text-underline-offset: 3px;
        text-decoration-thickness: 0.9px !important;
         }
        .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore .seeMore:hover {
          text-decoration-style: solid !important;}
        .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore .seeMore:focus {
          text-decoration-style: solid !important;}
        .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore .seeMore i {
          color: #cd0080;
          font-size: 22px;
          position: relative;
          top: 2px;
          margin-left: 5px; }

@media (max-width: 47.938em) {
  .depleInfo .blockDespleInfo li {
    padding-bottom: 15px;
    margin-bottom: 15px; }
    .depleInfo .blockDespleInfo li:last-child {
      margin: 0px !important; }
  .depleInfo .blockDespleInfo .hiperDesple i {
    float: right; }
  .depleInfo .blockDespleInfo .contentInfo {
    padding: 0px; }
    .depleInfo .blockDespleInfo .contentInfo p {
      font-size: 14px; }
    .depleInfo .blockDespleInfo .contentInfo .hiperSeeMore .seeMore {
      margin-bottom: 20px; } }

/* -- DEVICES SIZES -- */
.mapaWeb {
  margin-top: 10px; }
  .mapaWeb ul {
    padding: 0px; }
    .mapaWeb ul li {
      padding: 25px;
      margin: 20px 0;
      border: 1px solid #d4d5d7;
      display: block; }
      .mapaWeb ul li .titleMaps {
        margin-top: 0px;
        font-size: 18px;
        color: #106BC4;
        line-height: 1.2;
        font-family: 'openSans_SemiBold';
        margin-bottom: 15px; }
      .mapaWeb ul li .listPageWeb li {
        padding: 0px;
        padding-right: 15px;
        padding-left: 15px;
        border: none;
        margin: 0; }
        .mapaWeb ul li .listPageWeb li ul li {
          padding: 5px 0px;
          padding-left: 20px;
          font-size: 15px;
          color: #333;
          position: relative;
          line-height: 1.2;
          border: none;
          margin: 10px 0; }
          .mapaWeb ul li .listPageWeb li ul li a {
            font-family: 'openSans_SemiBold'; }
          .mapaWeb ul li .listPageWeb li ul li i {
            color: #cd0080;
            font-size: 10px;
            position: absolute;
            left: 5px;
            top: 8px; }

/* -- DEVICES SIZES -- */
.fpca_glossari {
  padding-top: 0px;
  padding-bottom: 0px; }
  .fpca_glossari .glossari {
    padding: 0px;
    border: 1px solid #dddddd;
    display: inline-block;
    width: 100%;
    text-align: center; }
    .fpca_glossari .glossari li {
      display: inline-block; }
      .fpca_glossari .glossari li a {
        padding: 10px;
        color: #106BC4;
        display: inline-block;
        font-size: 19px;
        font-family: 'openSans_SemiBold';
        position: relative; }
        .fpca_glossari .glossari li a:hover {
          background: #cd0080;
          color: #fff; }
        .fpca_glossari .glossari li a:hover:after, .fpca_glossari .glossari li a:focus:after {
          content: '';
          width: 0px;
          height: 0px;
          border-top: 15px solid #cd0080;
          border-left: 10px solid transparent;
          border-right: 10px solid transparent;
          display: inline-block;
          vertical-align: middle;
          margin-right: 5px;
          position: absolute;
          bottom: -15px;
          left: 6px; }
        .fpca_glossari .glossari li a.active {
          background: #cd0080;
          color: #fff; }
          .fpca_glossari .glossari li a.active:after {
            content: '';
            width: 0px;
            height: 0px;
            border-top: 15px solid #cd0080;
            border-left: 10px solid transparent;
            border-right: 10px solid transparent;
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
            position: absolute;
            bottom: -15px;
            left: 6px; }

.contenidor-lletres {
  margin-top: 20px;
  padding: 0px;
  padding-top: 20px;
  border-top: 2px solid #ddd; }
  .contenidor-lletres .ocultaLletra {
    display: none; }
  .contenidor-lletres .mostraLletra {
    display: block; }
  .contenidor-lletres dl {
    padding: 20px 15px;
    padding-bottom: 0px;
    border-bottom: 1px solid #ddd; }
    .contenidor-lletres dl dt {
      margin: 0px;
      font-size: 24px;
      font-family: 'OpenSansRegular',Helvetica,Arial,sans-serif;
      font-weight: normal;
      margin-top: 20px;
      border-top: 1px solid #ddd;
      padding-top: 15px; }
      .contenidor-lletres dl dt:first-child {
        margin: 0px;
        padding: 0px;
        border: none; }
    .contenidor-lletres dl .glos_relacionats {
      margin-top: 20px; }
      .contenidor-lletres dl .glos_relacionats a {
        color: #333; }
    .contenidor-lletres dl dd {
      margin: 15px 0; }
    .contenidor-lletres dl a {
      font-size: 24px;
      margin-bottom: 10px;
      display: inline-block; }
    .contenidor-lletres dl p {
      line-height: 1.5;
      margin: 0px;
      color: #666666;
      font-size: 14px; }
    .contenidor-lletres dl .glos_relacionats {
      padding: 0px; }
    .contenidor-lletres dl .fileList {
      font-family: 'openSans_Bold';
      font-size: 14px;
      padding-left: 20px;
      position: relative;
      margin-bottom: 0px;
      line-height: 2; }
      .contenidor-lletres dl .fileList span {
        font-family: 'openSans_SemiBold';
        position: relative;
        padding-left: 30px;
        margin-left: 5px;
        display: inline-block; }
        .contenidor-lletres dl .fileList span i {
          color: #106BC4;
          font-size: 30px;
          position: absolute;
          left: 0;
          top: -5px; }
      .contenidor-lletres dl .fileList:before {
        content: "\f111";
        font-family: 'FontAwesome';
        position: absolute;
        top: 5px;
        left: 5px;
        font-size: 8px;
        color: #cd0080; }

.seeMoreGlossary {
  padding: 10px 15px;
  text-align: right; }
  .seeMoreGlossary a {
    font-size: 14px;
    font-family: 'openSans_Regular';
    color: #cd0080;
    margin: 0px; }

/* -- DEVICES SIZES -- */

  @media (max-width: 47.938em) {
    .breadcrumbsModalitat {
      background: none; }
    .breadcrumbsModalitat ul li {
      width: 100%;
      margin: 0px; }
    .breadcrumbsModalitat ul li a {
      height: auto !important;
      font-size: 16px !important;
      border: none !important;
      border-bottom: 1px solid #eeeeee !important;
      padding: 10px 12.5px !important;
      color: #106BC4 !important;
      border-left: 5px solid #fff !important; }
    .breadcrumbsModalitat ul li a.active {
      border-left-color: #106BC4 !important; }}

    .modalitat .navScroll {
      overflow: hidden;
      width: 100%;
      border-right: 1px solid #eee;
      position: relative;
      background: #cde1f6;
    }

    .modalitat .navScroll .scrollMoveRight {
      position: absolute;
      height: 92px;
      display: table;
      width: 25px;
      text-align: center;
      background: #106BC4;
      z-index: 1;
      right: 0px;
      top: 0;
    }

    .modalitat .navScroll .scrollMoveRight:hover {
      text-decoration: none !important;
    }

    .modalitat .navScroll .scrollMoveRight i {
      height: 100%;
      display: table-cell;
      vertical-align: middle;
      font-size: 25px;
      font-weight: bold;
      color: #fff;
    }

    .modalitat .navScroll .scrollMoveLeft {
      position: absolute;
      height: 92px;
      display: table;
      width: 25px;
      text-align: center;
      background: #106BC4;
      z-index: 1;
      top: 0;
      display: none;
    }

    .modalitat .navScroll .scrollMoveLeft:hover {
      text-decoration: none !important;
    }

    .modalitat .navScroll .scrollMoveLeft i {
      height: 100%;
      display: table-cell;
      vertical-align: middle;
      font-size: 25px;
      font-weight: bold;
      color: #fff;
    }

    .modalitat ul.nav {
      position: relative;
    }

    .modalitat ul.nav li {
      border: none;
      position: relative;
    }

    .modalitat ul.nav li:first-child a {
      border-left: none !important;
    }

    .modalitat ul.nav li a {
      background: #cde1f6;
      font-family: 'openSans_SemiBold';
      font-size: 22px;
      padding: 30px 34px;
      border-bottom: 1px solid #ddd;
      display: inline-block;
      border-right: 1px solid #ecedef;
      border-top: 1px solid #ddd;
      border-left: none;
    }

    .modalitat ul.nav li.active a {
      border-left: none !important;
      border-right: none !important;
      border-bottom: 1px solid #fff;
      background: #fff;
    }

    .modalitat ul.nav li:last-child a {
      border-right: none !important;
    }

    .modalitat ul.nav li.active:last-child a:after {
      content: "";
      height: calc(100% + 1px);
      position: absolute;
      left: 100%;
      background: #fff;
      width: 1000px;
      top: 0;
    }

.modalitat {
  border: 1px solid #eeeeee; }
  @media (max-width: 47.938em) {
    .modalitat {
      margin-top: 10px;
      border-top: none; }
      .modalitat .depleInfo {
        margin-top: 0px !important; }
        .modalitat .depleInfo .blockDespleInfo li:first-child {
          border-top: none; }
        .modalitat .depleInfo .blockDespleInfo .hiperDesple i {
          margin-right: 0;
          top: -2px; } }
  .modalitat .depleInfo {
    margin-top: 20px;
    padding: 20px; }

.depleInfo .blockDespleInfo li .block8-4 {
  margin: 0px; }
  .depleInfo .blockDespleInfo li .block8-4 .imgHiperPlay {
    background-size: cover;
    background-position: center;
    height: 250px;
    margin-bottom: 50px;
    position: relative;
    margin-top: -20px; }
    .depleInfo .blockDespleInfo li .block8-4 .imgHiperPlay a {
      width: 100%;
      display: inline-block;
      padding: 15px;
      font-size: 20px;
      font-family: "openSans_regular" !important;
      position: relative;
      margin-bottom: 10px;
      margin: 0px;
      position: absolute;
      bottom: 0px; }

.depleInfo .blockDespleInfo li .accesButton {
  display: inline-block;
  width: 100%;
  text-align: center;
  padding: 20px 0; }
  .depleInfo .blockDespleInfo li .accesButton a {
    background: #106BC4;
    color: #fff;
    padding: 25px 20px;
    display: inline-block;
    font-size: 20px;
    font-family: "openSans_semiBold" !important; }
    .depleInfo .blockDespleInfo li .accesButton a span i {
      position: relative;
      font-size: 20px;
      left: 5px;
      top: 0; }

@media (max-width: 47.938em) {
  .depleInfo .blockDespleInfo li .accesButton a {
    padding: 25px 10px; }
  .modalitat .breadcrumbsModalitat {
    margin: 0px;
    background: transparent;}
    .modalitat .breadcrumbsModalitat ul {
      background: none; }
      .modalitat .breadcrumbsModalitat ul li {
        float: none;
        margin: 0px;
        overflow: inherit;
        padding: 0px;
        display: block;
        border-bottom: 1px solid #ddd;
        background: #bde0f0; }
        .modalitat .breadcrumbsModalitat ul li:first-child {
          border-top: 1px solid #ddd; }
        .modalitat .breadcrumbsModalitat ul li a {
          border: 0px;
          padding: 10px;
          font-size: 14px;
          border-left: 5px solid #bde0f0 !important;
          color: #fff;
          background: #bde0f0;
          width: 100%;}
          .modalitat ul.nav li:first-child a {
            border-left: 5px solid #bde0f0 !important;
          }
          .modalitat .breadcrumbsModalitat ul li a:hover {
            border: none;
            border-left: 5px solid #cd0080 !important; }
            .modalitat .breadcrumbsModalitat ul li a:hover:after, .breadcrumbsModalitat ul li a:hover:before {
                display: none; }
            .modalitat .breadcrumbsModalitat ul li a:after, .breadcrumbsModalitat ul li a:before {
                display: none; }
            .modalitat .breadcrumbsModalitat ul li.active a {
              border-left: 5px solid #cd0080 !important; }
    .modalitat .breadcrumbsModalitat h2 {
      text-align: center;
      margin: 0px;

      font-size: 22px;
      font-family: "openSans_semiBold";
      color: #106BC4;
      margin-top: 15px; } }

/* -- DEVICES SIZES -- */
.catalog {
  padding: 50px 0;
  padding-top: 0px; }
  .catalog ul.nav  {
    border: none;
    position: relative; }
    .catalog ul.nav li {
      border: none; }
      .catalog ul.nav li a {
        background: #cde1f6;
        font-family: 'openSans_SemiBold';
        font-size: 22px;
        padding: 30px 35px;
        border-bottom: 1px solid #ddd;
        display: inline-block;
        border-right: 1px solid #ecedef;
        border-top: 1px solid #ddd;
        border-left: none; }
      .catalog ul.nav li.active a {
        border-left: none !important;
        border-right: 0 !important;
        background: #fff;}
      .catalog ul.nav li:first-child a {
        border-left: 1px solid #ddd !important; }
  .catalog .tab-content .tab-pane > h3 {
    margin: 0;
    padding-bottom: 10px;
    font-size: 20px; }
  .catalog .navScroll {
    overflow: hidden;
    width: 100%;
    border-right: 1px solid #ccc;
    position: relative;
    background: #cde1f6; }
  .catalog .navScroll ul.nav li.active:last-child a:after {
    content: "";
    height: calc(100% + 1px);
    position: absolute;
    left: 100%;
    background: #fff;
    width: 1000px;
    top: 0;}
  .catalog ul.nav-tabs li:last-child a {
    border-right: none !important;
  }
  @media (max-width: 47.938em) {
    .catalog {
      margin-top: 10px;
      border-top: none;}
    .catalog .navScroll {
      margin-top: -3px; }
    .catalog .navScroll ul.nav-tabs{
      background: none; }
    .catalog .navScroll ul.nav-tabs li{
      width: 100%;
      float: none;
      margin: 0px;
      overflow: inherit;
      padding: 0px;
      display: block;
      border-bottom: 1px solid #ddd;
      background: #bde0f0;}
    .catalog .navScroll ul.nav-tabs li a{
      border: 0px;
      padding: 10px;
      font-size: 14px;
      border-left: 5px solid #bde0f0 !important;
      color: #fff;
      background: #bde0f0;
      height: auto !important;
      font-size: 16px !important;
      border-bottom: 1px solid #eeeeee !important;
      padding: 10px 12.5px !important;
      color: #106BC4 !important;
      width: 100%;}
    .catalog ul.nav-tabs li.active a{
      border: 0px;
      padding: 10px;
      font-size: 14px;
      border-left: 5px solid #bde0f0 !important;
      color: #fff;
      background: #fff;
      width: 100%;}
    .catalog ul.nav-tabs li:first-child a {
      border-left: 5px solid #bde0f0 !important;}
    .catalog ul.nav-tabs li.active a {
      border-left: 5px solid #cd0080 !important;}
    .catalog a.hiperHomeMobil {
      text-align: center;
      margin: 0px;
      font-size: 22px;
      font-family: "openSans_semiBold";
      color: #106BC4;
      margin-top: 15px;
    }
    .catalog a.hiperHomeMobil > i.fa.fa-angle-up,
    .catalog a.hiperHomeMobil > i.fa.fa-angle-down {
      margin-left: .5em;
    }
    .catalog .hiperScroll{
      display: none !important;
    }
  }
    .catalog .navScroll .scrollMoveRight {
      position: absolute;
      height: 92px;
      display: table;
      width: 25px;
      text-align: center;
      background: #106BC4;
      z-index: 1;
      right: 0px;
      top: 0; }
      .catalog .navScroll .scrollMoveRight:hover {
        text-decoration: none !important; }
      .catalog .navScroll .scrollMoveRight i {
        height: 100%;
        display: table-cell;
        vertical-align: middle;
        font-size: 25px;
        font-weight: bold;
        color: #fff; }
    .catalog .navScroll .scrollMoveLeft {
      position: absolute;
      height: 92px;
      display: table;
      width: 25px;
      text-align: center;
      background: #106BC4;
      z-index: 1;
      top: 0;
      display: none; }
      .catalog .navScroll .scrollMoveLeft:hover {
        text-decoration: none !important; }
      .catalog .navScroll .scrollMoveLeft i {
        height: 100%;
        display: table-cell;
        vertical-align: middle;
        font-size: 25px;
        font-weight: bold;
        color: #fff; }
  .catalog .tab-content {
    display: block;
    width: 100%;
    border: 1px solid #ccc;
    padding: 30px 15px;
    position: relative;
    border-top: 0; }
    @media (max-width: 47.938em) {
      .catalog .tab-content {
        width: 100%;
        padding-bottom: 15px; } }
    .catalog .tab-content:after {
      content: "";
      background-image: url(../img/somBox.png) !important;
      width: 70%;
      height: 20px;
      position: absolute;
      bottom: -21px;
      background-size: 100%;
      background-repeat: no-repeat;
      left: 15%; }
    .catalog .tab-content ul li a {
      border: none;
      background: none;
      padding: 0px; }
    .catalog .tab-content .depleInfo .blockDespleInfo li h3 {
      font-family: "openSans_SemiBold";
      font-size: 20px;
      color: #106BC4; }
    .catalog .tab-content .depleInfo .blockDespleInfo li .contentInfo {
      padding: 20px 40px; }
    .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog {
      padding-right: 30px;
      width: calc(50% - 5px);
      width: -webkit-calc(50% - 5px);
      display: inline-block;
      margin-bottom: 10px;
      padding: 20px 30px;
      border: 1px solid #ddd; }
      .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog:nth-child(even) {
        margin-left: 2.5px; }
      .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog:nth-child(odd) {
        margin-right: 2.5px; }
      @media (max-width: 47.938em) {
        .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog {
          width: 100%;
          padding-right: 0px; } }
      .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul {
        padding: 0px; }
        .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul li {
          border: none;
          margin-bottom: 0 !important;
          padding-top: 15px !important;
          padding-left: 20px;
          position: relative; }
          .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul li:before {
            font-family: fontawesome;
            content: "\f111";
            vertical-align: middle;
            position: absolute;
            top: 22px;
            left: 5px;
            font-size: 6px;
            color: #106BC4; }
          .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul li a {
            font-size: 15px;
            font-family: "openSans_regular";
            color: #333;
            border: none !important; }
            .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul li a span {
              font-family: "openSans_regular";
              color: #333;
              width: 100%;
              display: inline-block; }
          .catalog .tab-content .depleInfo .blockDespleInfo li .blocCatalog ul li:last-child {
            border: none; }

/* -- DEVICES SIZES -- */
.searchAvanza .blockLeft form {
  margin: 50px 0;
  margin-top: 0px;
  border: 1px solid #ddd;
  border-top: none; }
  .searchAvanza .blockLeft form .titleSection {
    padding: 10px;
    background: #ecedef;
    color: #106BC4;
    font-size: 18px;
    width: 100%;
    margin: 0px;
    border-bottom: 1px solid #ddd;
    border-top: 1px solid #ddd;
    position: relative; }
    .searchAvanza .blockLeft form .titleSection i {
      position: absolute;
      right: 10px;
      top: calc(50% - 15px);
      top: -webkit-calc(50% - 15px);
      font-size: 30px;
      font-weight: bold; }
  .searchAvanza .blockLeft form .buttonSearch {
    width: 100%;
    display: inline-block;
    text-align: center;
    padding: 20px 0;
    margin-bottom: 10px; }
    .searchAvanza .blockLeft form .buttonSearch #btnCerca {
      background: #cd0080;
      color: #fff;
      border: none;
      padding: 10px 20px;
      -webkit-border-radius: 5px;
      -moz-border-radius: 5px;
      border-radius: 5px;
      font-family: 'openSans_SemiBold';
      font-size: 15px; }
  .searchAvanza .blockLeft form .boxLupSearch {
    margin: 15px;
    display: inline-block;
    width: calc(100% - 40px);
    width: -webkit-calc(100% - 40px);
    position: relative; }
    .searchAvanza .blockLeft form .boxLupSearch i {
      position: absolute;
      right: 10px;
      top: calc(50% - 15px);
      top: -webkit-calc(50% - 15px);
      font-size: 20px; }
    .searchAvanza .blockLeft form .boxLupSearch input {
      width: 100%;
      padding: 5px 10px;
      border: 1px solid #ddd;
      padding-right: 30px;
      margin-bottom: 10px; }
  .searchAvanza .blockLeft form .checkBox {
    padding: 10px; }
    .searchAvanza .blockLeft form .checkBox .titleToogle {
      padding: 20px 0;
      display: inline-block;
      width: 100%;
      font-size: 18px;
      font-family: 'openSans_SemiBold';
      color: #666;
      position: relative;
      text-decoration: none !important; }
      .searchAvanza .blockLeft form .checkBox .titleToogle i {
        font-size: 25px;
        position: absolute;
        right: 20px;
        color: #cd0080;
        font-weight: bold; }
    .searchAvanza .blockLeft form .checkBox ul {
      padding: 0px 0;
      padding-left: 10px;
      padding-bottom: 20px;
      border-bottom: 1px solid #ddd;
      margin: 0; }
      .searchAvanza .blockLeft form .checkBox ul li label {
        position: relative;
        width: 100%;
        padding-left: 20px;
        font-size: 14px; }
        .searchAvanza .blockLeft form .checkBox ul li label input {
          position: absolute;
          left: 0px; }

.searchAvanza .blockRight .nav-tabs {
  border-bottom: 1px solid #ddd; }

.searchAvanza .blockRight .tab-content {
  border: none;
  padding: 0px;
  padding-left: 30px; }
  .searchAvanza .blockRight .tab-content:after {
    display: none; }
  .searchAvanza .blockRight .tab-content .resultats {
    font-size: 15px;
    margin: 0px;
    margin-top: 50px; }
    .searchAvanza .blockRight .tab-content .resultats strong {
      font-family: "openSans_Bold" !important; }
  .searchAvanza .blockRight .tab-content ul {
    padding: 0px;
    padding-bottom: 10px; }
    .searchAvanza .blockRight .tab-content ul li {
      padding: 20px 0px;
      border-bottom: 1px solid #ddd; }
      .searchAvanza .blockRight .tab-content ul li h3 {
        margin: 0px; }
      .searchAvanza .blockRight .tab-content ul li .titleResultat {
        font-size: 18px;
        font-family: 'openSans_SemiBold'; }
      .searchAvanza .blockRight .tab-content ul li p {
        font-size: 14px;
        margin-top: 10px; }
      .searchAvanza .blockRight .tab-content ul li .linkHiper {
        font-size: 12px;
        color: #666666; }
      .searchAvanza .blockRight .tab-content ul li ul {
        border: none; }
        .searchAvanza .blockRight .tab-content ul li ul li {
          border: none;
          font-size: 14px;
          padding: 0px;
          display: inline-block;
          color: #106BC4; }
          @media (max-width: 47.938em) {
            .searchAvanza .blockRight .tab-content ul li ul li {
              width: auto; } }
          .searchAvanza .blockRight .tab-content ul li ul li a {
            font-size: 14px;
            color: #666666;
            font-family: 'openSans_SemiBold'; }

@media (max-width: 47.938em) {
  .searchAvanza {
    margin: 0px; }
    .searchAvanza .blockLeft {
      padding: 0px;
      margin: 0px; }
      .searchAvanza .blockLeft form {
        margin: 10px 0; }
        .searchAvanza .blockLeft form .titleSection {
          margin-bottom: 10px;
          cursor: pointer; }
          .searchAvanza .blockLeft form .titleSection:last-child {
            margin: 0px; }
        .searchAvanza .blockLeft form .boxLupSearch {
          display: none; }
        .searchAvanza .blockLeft form .checkBox {
          display: none; }
    .searchAvanza .blockRight {
      padding: 0px;
      margin: 0px; }
      .searchAvanza .blockRight .catalog {
        padding: 20px 0; }
        .searchAvanza .blockRight .catalog .tab-content {
          padding: 0 10px;
          width: 100%; }
          .searchAvanza .blockRight .catalog .tab-content .hiperTabs {
            font-size: 15px;
            background: #ecedef;
            border-top: 1px solid #ddd;
            border-bottom: 1px solid #ddd;
            color: #333;
            padding: 10px;
            font-family: 'openSans_SemiBold';
            width: calc(100% + 20px);
            width: -webkit-calc(100% + 20px);
            margin-left: -10px;
            display: inline-block;
            position: relative;
            color: #106BC4; }
            .searchAvanza .blockRight .catalog .tab-content .hiperTabs i {
              font-size: 25px;
              position: absolute;
              right: 20px;
              color: #333;
              font-weight: bold;
              top: 10px; }
            .searchAvanza .blockRight .catalog .tab-content .hiperTabs.active {
              color: #333; }
          .searchAvanza .blockRight .catalog .tab-content .resultats {
            margin-top: 30px; }
          .searchAvanza .blockRight .catalog .tab-content .paginationNews {
            margin: 0px; }
            .searchAvanza .blockRight .catalog .tab-content .paginationNews li a span {
              display: none; }
  .paginationNews ul {
    padding: 0; }
  .paginationNews ul li {
    width: auto !important; }
    .paginationNews ul li a span {
      display: none; } }

/* -- DEVICES SIZES -- */
.fpca_preguntesFrequents {
  margin-top: 0px; }
  .fpca_preguntesFrequents .shadowBox2 {
    overflow: inherit; }
    .fpca_preguntesFrequents .shadowBox2 p {
      font-size: 15px;
      color: #666666;
      line-height: 1.5;
      margin: 0;
      margin-top: -20px;
      margin-bottom: 10px; }
    @media (max-width: 47.938em) {
      .fpca_preguntesFrequents .shadowBox2 p {
        margin-top: 20px; } }

/* -- DEVICES SIZES -- */
.llistat_collapse .panel-group {
  display: inline-block;
  width: 100%;
  padding: 0 15px;
  margin: 0; }
  .llistat_collapse .panel-group .panel-default {
    margin-bottom: 15px;
    border-top: 1px solid #d4e3f7; }
    .llistat_collapse .panel-group .panel-default:first-child {
      border: none; }
      .llistat_collapse .panel-group .panel-default:first-child .contentInfo {
        display: block; }
        .llistat_collapse .panel-group .panel-default .panel-collapse .panel-body a {
          color: #cd0080;
          font-family: "openSans_Regular";
          text-decoration: underline !important;
          text-decoration-style: dashed !important;
          text-underline-offset: 3px;
          text-decoration-thickness: 0.9px !important;}
          .llistat_collapse .panel-group .panel-default .panel-collapse .panel-body a:hover {
            text-decoration-style: solid !important;}
          .llistat_collapse .panel-group .panel-default .panel-collapse .panel-body a:focus {
            text-decoration-style: solid !important;}
  .llistat_collapse .panel-group h3 {
    margin: 0px; }
  .llistat_collapse .panel-group .panel-heading a {
    font-family: "openSans_SemiBold";
    font-size: 18px;
    color: #333;
    display: block;
    position: relative;
    padding-left: 1.650em; }
  .llistat_collapse .panel-group .panel-heading i {
    display: none;
    /*background: #cd0080;
    color: white;
    width: 25px;
    height: 25px;
    text-align: center;
    -webkit-border-radius: 50%;
    -moz-border-radius: 50%;
    border-radius: 50%;
    font-size: 25px;
    top: 2px;
    margin-right: 15px;
    position: absolute;
    left: 0;*/
}
/*.llistat_collapse .panel-group .panel-heading i:before {
  position: relative;
  top: -1px;
}*/

.llistat_collapse .panel-group .panel-heading .fa-angle-down:before {
  top: 1px;
}

.llistat_collapse .panel-group .contentInfo {
  padding-left: 30px;
  margin-top: 20px;
  display: none;
}

.llistat_collapse .panel-group .contentInfo p {
  margin-bottom: 20px;
  font-size: 15px;
  color: #595853;
}

.llistat_collapse .panel-group .contentInfo .hiperSeeMore {
  text-align: right;
}

.llistat_collapse .panel-group .contentInfo .hiperSeeMore .seeMore {
  font-family: "openSans_SemiBold";
  font-size: 15px;
  color: #333;
  display: inline-block;
}

.llistat_collapse .panel-group .contentInfo .hiperSeeMore .seeMore i {
  color: #106BC4;
  font-size: 22px;
  position: relative;
  top: 2px;
  margin-left: 5px;
}
@media (max-width: 47.938em) {
  .llistat_collapse .panel-group .panel-heading a{
    padding-left: 0;
  }
  .depleInfo .blockDespleInfo li {
    padding-bottom: 0px;
    margin-bottom: 15px;
  }
  .depleInfo .blockDespleInfo li:last-child {
    margin: 0px !important;
  }
  .depleInfo .blockDespleInfo .hiperDesple i {
    float: right;
  }
  .depleInfo .blockDespleInfo .contentInfo {
    padding: 0px !important;
  }
  .depleInfo .blockDespleInfo .contentInfo p {
    font-size: 14px;
  }
}

@media (max-width: 47.938em) {
  .bolck12Supe {
    margin: 0;

  }
}

/* -- DEVICES SIZES -- */
.blockRightWrapper .googleMaps {
  width: 100%;
  height: 300px;
  background: #106BC4;
  margin-top: 35px; }

.blockLeftWrapper .dateContacte {
  width: 100%;
}

.blockLeftWrapper .dateContacte h3 {
  margin-top: 0;
}

.blockLeftWrapper .dateContacte p {
  font-size: 14px;
  margin-bottom: 0px;
  line-height: 1.4;
}

.blockLeftWrapper .dateContacte p span {
  margin-top: 20px;
  display: inline-block;
  font-size: 13px;
}

.blockLeftWrapper .dateContacte p.margin-top20 {
  margin-top: 20px;
  display: inline-block;
}

.block100 {
  width: 100%;
  display: inline-block;
}

@media (max-width: 47.938em) {
  .block100 {
    padding: 0 15px;
  }
}

.block100 .dateContacte {
  width: 100%;
  margin-top: 40px;
  display: inline-block;
  font-size: 14px;
  margin-bottom: 0px;
  line-height: 1.4;
}

.block100 .dateContacte span {
  margin-top: 20px;
  display: inline-block;
  font-size: 13px;
}

.block100 .dateContacte.margin-top20 {
  margin-top: 20px;
  display: inline-block;
 }

.block100 .dateContacte p a {
  color: #cd0080;
  font-weight: normal;
}

.block100 .dateContacte .hiperSocialMedia {
  color: #cd0080;
  font-weight: normal;
  margin-top: 10px;
  display: inline-block;
  font-size: 13px;
}

.block100 .dateContacte ul {
  padding: 0px;
  display: inline-block;
}

.block100 .dateContacte ul li {
  padding-left: 15px;
  position: relative;
}

.block100 .dateContacte ul li:before {
  font-family: fontawesome;
  content: "\f111";
  vertical-align: middle;
  position: absolute;
  top: 15px;
  left: 0px;
  font-size: 6px;
  color: #106BC4;
}

.block100 .dateContacte ul li p {
  margin: 0px 0px 2px;
}

.block100 .dateContacte ul li strong {
  font-weight: bold !important;
  margin-top: 10px;
  display: inline-block;
}

.block100 .dateContacte ul + h3 {
  margin-top: 50px;
}

@media (max-width: 47.938em) {
  .infoContact {
    padding: 0 15px;
    margin: 0 15px;
  }
}

.infoContact ul {
  padding: 0px;
}

.infoContact ul li {
  display: inline-block;
  width: 47.5%;
  padding: 10px 15px;
  border-top: 1px solid #d1cfc4;
  position: relative;
}
.infoContact ul li:nth-child(odd){
  float: left;
}

.infoContact ul li:nth-child(even){
  float: right;
}
.infoContact ul li a.articleRel{
    font-size:1em;
    font-weight:bold;
}

@media (max-width: 47.938em) {
  .infoContact ul li {
    width: 100%;
    margin: 0px !important;
  }
  .infoContact ul li:nth-child(2) {
    border-top: 1px solid #d1cfc4 !important;
  }
}

.infoContact ul li:nth-child(1) {
  border-top: 3px solid #d1cfc4;
}

.infoContact ul li:nth-child(2) {
  border-top: 3px solid #d1cfc4;
}

.infoContact ul li:nth-child(even) {
  margin-left: 1%;
}

.infoContact ul li:nth-child(odd) {
  margin-right: 1%;
}

.infoContact ul li:before {
  font-family: fontawesome;
  content: "\f111";
  vertical-align: middle;
  position: absolute;
  top: 18px;
  left: 5px;
  font-size: 6px;
  color: #106BC4;
}

/*Formulario envia amigo*/
#formulari_envia .panel-group li{
  width: 100%;
  margin-top: 15px;
}

#formulari_envia .panel-group .formAmic1 .panel {
    border-bottom: none !important;
    text-align: left;
    background: #f5f5f5;
    padding-left: 0.8em;
}

#formulari_envia .form-control {
    background-color: #FFFFFF;
    color: #555555;
}

#formulari_envia .formAmic1 div#myCaptcha label {
    margin: 20px 10px 8px 0;
    display: block;
}

#formulari_envia .displaynone {
    display: none;
}

@media (min-width: 47.939em){

  #formulari_envia .panel-group {
    padding: 30px;
    margin: 0;
    width: 100%;

    padding: 10px 20px;
    background: #f5f5f5;
    border: 1px solid #DDDDDD;
    box-shadow: 0.188em 0.188em 0.25em rgba(0, 0, 0, 0.14);
    margin-top: 8px;
    text-align: left;
  }

  #formulari_envia {
    position: absolute;
    z-index: 11;
    margin-top: 0;
    right: 0;
    width: 100%;
    top: 65px;
    right: 16px;
    min-width: 480px;
    background-image: url(../img/flecha.png);
    background-position: 98% top;
    background-repeat: no-repeat;
    background-color: rgba(0, 0, 0, 0);
    margin-top: -8px;
    border: none;
    box-shadow: none;
  }
}

footer .twitterBox {
    line-height: 1.5;
    color: #FFF;
    -webkit-border-radius: 5px 5px 0px 0px;
    background-position: 100% -120px;
    background-color: #434443 !important;
    background-position: 0 0;
    border-radius: 5px 5px 5px 5px;
}
footer .twitterBox .tweet_header {
    background-position: 100% -10px !important;
    background-color: #434443 !important;
    padding-right: 10px;
    border-radius: 15px 15px 15px 15px;
}
footer .twitterBox .tweet_content {
    background-position: 100% -100px !important;
    background-color: #434443 !important;
    padding-right: 10px;
    border-radius: 0px 0px 11px 12px;
}
footer .twitterBox .tweet_footer {
    background-position: 100% -120px;
    background-color: #434443 !important;
    padding-right: 0px;
    border-radius: 0px 0px 45px 45px;
}
footer .twitterBox .tweet_list li .tweet_avatar {
    padding-left: 0;
}

footer #fContainer_right {
    border-top: none;
}

footer .blockFirst ul, footer .blockCenter ul, footer .siteExt ul{
  padding-left: 0;
}

/**********ATC FORMS**************/
.atc_form label {
display:block;
}

.atc_form input[type="submit"]{
border: 1px solid transparent;
    border-radius: 4px;
background: #cd0080;
-webkit-appearance: button;
    cursor: pointer;
float: right !important;
color: #fff !important;
    display: inline-block;
    padding: 6px 19px;
    margin-bottom: 0;
    font-size: .875em;
    font-weight: normal;
    line-height: 1.428571429;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
}
 .atc_form select {
    -webkit-appearance: menulist-button;
    font-size: 14px;
    background-color: #FFFFFF;
    background-image: none;
    border: 1px solid #CCCCCC;
    border-radius: 4px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset;
    color: #555555;
        line-height: 1.42857;
    padding: 6px 12px;
    transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
        height: 2.4em !important;
    padding-bottom: 0;
    padding-top: 0;
    background: #FFF;

margin: .5em;
 }

@media(min-width:768px) {
    .atc_form select {
        display: block !important;
        width: 100% !important;
    }
}
@media(min-width:992px) {
    .atc_form select {
        display: block !important;
        width: 100% !important;
    }
}
@media(min-width:1200px) {
    .atc_form select {
        display: inline-block !important;
        width: 30% !important;
    }
}
/**********ATC FORMS FI************************************/

/**********TAULES******************************************/

/*Afegeixo classes per a la correcta visualitzaciÃ¯Â¿Â½ de les taules*/

.table-responsive th, .table-responsive tr:first-child td {
    background: #106BC4;
    color: #fff;
}
.table-responsive th p, .table-responsive tr:first-child td p{
    color: #fff !important;
}
.table-responsive .table>thead>tr>th,
.table>tbody>tr>th,
.table>tfoot>tr>th,
.table>thead>tr>td,
.table>tbody>tr>td,
.table>tfoot>tr>td {
    padding: 8px;
    line-height: 1.428571429;
    vertical-align: top;
    border: 1px solid #ddd;
}

.table-responsive tr:nth-child(odd) {
    background-color: #FCFCFC;
}
.whiteBackground .table-responsive tr:nth-child(even) {
    background-color: #f5f5f5;
}

.block8-4 .blockLeftWrapper .table-responsive p {
    margin-bottom: 0;
}


@media(max-width: 772px) {
    /*.pd-15{
    padding: 0px;
  }*/
    /*.pd-15s-m{
    padding-right: 15px;
  }*/

    .pd-15st-left {
        padding-left: 15px;
    }
    .npd-15st-right {
        margin-right: 15px;
    }
}

@media(max-width: 750px) {
    .mgn-15-left {
        margin-left: 0px;
    }
    .mgn-15-left-m {
        margin-left: -15px !important;
    }
    .col-break-2,
    .col-break-3,
    .col-break-4 {
        -webkit-column-count: 1;
        -moz-column-count: 1;
        column-count: 1;
        /******Padding opcional*********
    -webkit-column-gap: 33px;
    -moz-column-gap: 33px;
    column-gap: 33px;
    *******************************/
        /* column-rule: 4px outset #ff00ff; */

        padding: 0;
    }
    .pd-30 {
        padding: 0px 30px 0px 30px !important;
    }
    .col-xs-12 .pd-30,
    .col-xs-8 .pd-30,
    .col-xs-6 .pd-30,
    .col-xs-4 .pd-30 {
        padding: inherit !important;
    }
    .col-xs-12 .pd-15,
    .col-xs-8 .pd-15,
    .col-xs-6 .pd-15,
    .col-xs-4 .pd-15 {
        padding: 0px 15px 0px 15px !important;
    }
    .pd-15 {
        padding: 0px 15px 0px 15px;
    }
    .pd-10 {
        padding: 0px 10px 0px 10px;
    }
    .pd-15s-m {
        padding: 0px 15px 0px 15px;
    }
    .off-padding-full-m {
        padding: 0px !important;
    }
    .off-margin-left {
        margin: -15px !important;
    }
    .mg-n15-nm {
        margin: 0px 0px 0px 0px;
    }
    .pd-15x {
        padding: 0px 15px 0px 15px;
    }
    .footer-xarxes .llistat_xarxes_socials .social_text {
        display: block;
        font-style: normal !important;
    }
    .header2 {
        box-sizing: border-box;
        color: rgb(51, 51, 51);
        display: block;
        font-family: OpenSansRegular, Helvetica, Arial, sans-serif;
        font-size: 20px;
        font-weight: 500;
        line-height: 22px;
        margin-bottom: 16px;
        margin-left: 0px;
        margin-right: 0px;
        padding-bottom: 0px;
        position: relative;
        width: 320px;
    }
    footer .avis_legal .fi_peu {
        margin: 0;
    }

    footer .avis_legal .idiomes {
        height: auto;
        z-index: 50;
        position: relative;
    }
    footer .avis_legal .torna_amunt {
        /*margin-top: 0;*/
    }
}
/************* TAULES FI*********************/

.listBlockNewsATC{display: -webkit-box; display: flex; flex-wrap: wrap;}
.listBlockNewsATC li{display: -webkit-box; display: flex; width: 22%; margin: 0 15px; padding: 0; border-bottom: 1px solid #e2e1e1; margin-bottom: 30px;}
@media (max-width: 1200px){
	.listBlockNewsATC li {width: 28%}
}
@media (max-width: 100px){
	.listBlockNewsATC li {width: 45%;}
}
@media (max-width: 768px){
	.listBlockNewsATC li {width: 100%;}
}

body.org-opencms-gwt-client-ui-css-I_CmsDirectEditCss-editButtonsVisible {
    margin-top: 50px;
}

.block8-4 .blockLeftWrapper ol a, .block8-4 .blockLeftWrapper ul a {
  color: #cd0080;
  font-family: 'openSans_Regular';
  text-decoration: underline !important;
  text-decoration-style: dashed !important;
  text-underline-offset: 3px;
  text-decoration-thickness: 0.9px !important;
}
.block8-4 .blockLeftWrapper ol a:focus, .block8-4 .blockLeftWrapper ul a:focus {
    text-decoration-style: solid !important;
}
.block8-4 .blockLeftWrapper ol a:hover, .block8-4 .blockLeftWrapper ul a:hover {
    text-decoration-style: solid !important;
}