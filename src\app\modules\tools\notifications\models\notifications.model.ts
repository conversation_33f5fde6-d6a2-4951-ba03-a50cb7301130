import { PtTableIconTemplate, PtTableTagTemplate } from "pt-ui-components-mf-lib";
import { NotificationFilters, NotificationResponseContent } from "./notifications-endpoint.model";

export enum AvisoEnum {
	AREA_PRIVADA = "AREA_PRIVADA"
}
export type AvisoT = keyof typeof AvisoEnum;
export type AvisoValues<AvisoEnum> = AvisoEnum[keyof AvisoEnum];

export enum NotificationModalModeEnum {
	FILTER = "FILTER",
	EDIT = "EDIT",
	NEW = "NEW"
}
export type NotificationModalModeT = keyof typeof NotificationModalModeEnum;
export type NotificationModalModeValues<NotificationModalModeEnum> = NotificationModalModeEnum[keyof NotificationModalModeEnum];

export enum NotificationTypeEnum {
	warning = "warning",
	error = "error",
	info = "info",
	success = "success",
	cad = "cad"
}
export type NotificationTypeT = keyof typeof NotificationTypeEnum;
export type NotificationTypeValues<NotificationTypeEnum> = NotificationTypeEnum[keyof NotificationTypeEnum];

export interface NotificationsRow extends NotificationResponseContent {
	tipoRow?: PtTableTagTemplate
	contextoRow?: string;
	activoRow?: PtTableIconTemplate;
}

export interface NotificationModalInput {
	title: string;
	mode: NotificationModalModeEnum;
	notification?: NotificationsRow | NotificationFilters;
}