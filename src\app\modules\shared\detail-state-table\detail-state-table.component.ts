import { Component, OnInit, Input } from '@angular/core';
import { PtTableColumn } from 'pt-ui-components-mf-lib';
import { StateChangesTableRow } from './models/detail-state-table.model';
import { TranslateService } from '@ngx-translate/core';

@Component({
	selector: 'app-detail-state-table',
	templateUrl: './detail-state-table.component.html'
})
export class DetailStateTableComponent implements OnInit {

	@Input() stateTranslations: string;
	@Input() firstDate: Date | string;
	@Input() set stateChanges(value: StateChangesTableRow[]) {
		this.setTableValues(value);
	}

	stateChangesColumns: PtTableColumn[] = [];
	stateChangesRows: StateChangesTableRow[] = [];

	constructor(
		private translateService: TranslateService
	) { }

	ngOnInit(): void {
		this.setUpTableColumns();
	}

	// Definición de datos y de tipos de datos de las tablas
	private setUpTableColumns(): void {
		this.stateChangesColumns = [
			{
				id: "dateChange",
				label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.STATE_HISTORY_TABLE.COLUMNS.date",
				isResizable: false,
				isSortable: true,
				template: "date",
				options: { dateFormat: 'dd/MM/yyyy HH:mm:ss' },
				width: "70px"
			},
			{
				id: "stateColumn",
				label: "MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.DETAIL.STATE_HISTORY_TABLE.COLUMNS.state",
				isResizable: false,
				isSortable: false,
				template: 'icon',
				width: "90px"
			}
		];
	}

	private setTableValues(stateChanges: StateChangesTableRow[]) {
		this.stateChangesRows.length = 0;

		const stateRows = this.setLastStateChangesRow(stateChanges, this.firstDate);
		this.stateChangesRows = stateRows?.map((state: StateChangesTableRow,index:number) => {
			// Map autoliquidacio entity into the autoliquidacio table row
			const stateValue = this.stateTranslations && state ? this.translateService.instant(`${this.stateTranslations}.${state?.current}`) : "";
			return Object.assign(
				{},
				// Default autoliquidacio entity
				state,
				// Table row model
				{
					dateChange: new Date(state.dateChange),
					stateColumn: {
						id:`button${index}`,
						icon: state.currentStateIcon,
						iconPos: 'left',
						label: stateValue
					}
				}
			);
		}) || [];
	}

	private setLastStateChangesRow (stateChanges: StateChangesTableRow[], firstDate: Date | string) : StateChangesTableRow[] {
		if (!stateChanges?.length || stateChanges?.length <= 0) return [];

		const firstState = stateChanges[0];
		const newState: StateChangesTableRow = {
			...firstState,
			current: firstState?.before,
			[firstDate ? 'dateChange' : undefined]: new Date(firstDate),
			currentStateIcon: firstState?.previousStateIcon
		}
		return [newState,...stateChanges];
	}
}
