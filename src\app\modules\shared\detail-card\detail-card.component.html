<pt-card2
	[_title]="title"
	[_type]="Card2Mode.SUMMARY"
>
	<form [formGroup]="detailForm">
		<div class="row">
			<ng-container *ngFor="let item of detailCardFields">
				<ng-container
					*ngTemplateOutlet="
						inputs;
						context: {
							id: item.id,
							fieldIcon: item.fieldIcon,
							fieldLabel: item.fieldLabel,
							fieldType: item.fieldType,
							fieldContainerClass: item.fieldContainerClass,
							fieldNumberMode: item.fieldNumberMode,
							fieldValue: item.fieldValue
						}
					"
				></ng-container>
			</ng-container>
		</div>
	</form>
</pt-card2>

<ng-template
	#inputs
	let-id="id"
	let-fieldLabel="fieldLabel"
	let-fieldType="fieldType"
	let-fieldContainerClass="fieldContainerClass"
	let-fieldNumberMode="fieldNumberMode"
	let-fieldValue="fieldValue"
>
	<ng-container [ngSwitch]="fieldType">
		<div
			*ngSwitchCase="DetailFieldType.state"
			[ngClass]="
				fieldContainerClass || 'col-4 col-sm-4 col-md-4 col-lg-4'
			"
		>
			<span class="pt-field">
				<span class="field-label">{{ fieldLabel | translate }}</span>
			</span>

			<pt-link
				*ngIf="stateIcon === StatesIcons.ERROR"
				[_id]="id"
				[_icon]="stateIcon"
				[_label]="stateLabel"
				_wrapperClass="pt-link-sm"
				(_clickFn)="onStateClick($event)"
			>
			</pt-link>

			<span *ngIf="stateIcon !== StatesIcons.ERROR" class="pt-field">
				<span
					class="pt-field d-flex justify-content-start align-items-center mt-1"
				>
					<em [class]="stateIcon"></em>
					<input
						class="p-component p-inputtext"
						[ngClass]="{ 'ml-2': stateIcon }"
						[value]="(stateLabel | translate) || '-'"
						disabled
					/>
				</span>
			</span>
		</div>

		<div
			*ngSwitchCase="DetailFieldType.link"
			[ngClass]="
				fieldContainerClass || 'col-4 col-sm-4 col-md-4 col-lg-4'
			"
		>
			<span class="pt-field">
				<span class="field-label">{{ fieldLabel | translate }}</span>
			</span>

			<pt-link
				[_id]="id"
				[_icon]="fieldIcon"
				[_label]="fieldValue"
				_wrapperClass="pt-link-sm"
				(_clickFn)="onLinkClick(id)"
			>
			</pt-link>
		</div>
		<div
			*ngSwitchCase="DetailFieldType.icon"
			[ngClass]="
				fieldContainerClass || 'col-4 col-sm-4 col-md-4 col-lg-4'
			"
		>
			<span class="pt-field">
				<span class="field-label">{{ fieldLabel | translate }}</span>
			</span>

			<span class="pt-field">
				<span
					class="pt-field d-flex justify-content-start align-items-center mt-1"
				>
					<em [class]="stateIcon"></em>
					<input
						class="p-component p-inputtext"
						[ngClass]="{ 'ml-2': stateIcon }"
						[value]="(stateLabel | translate) || '-'"
						disabled
					/>
				</span>
			</span>
		</div>

		<div
			*ngSwitchCase="DetailFieldType.number"
			[ngClass]="
				fieldContainerClass || 'col-4 col-sm-4 col-md-4 col-lg-4'
			"
		>
			<pt-input-number
				[_id]="id"
				[_label]="fieldLabel"
				[_formGroup]="detailForm"
				[_formControlName]="id"
				[_useGrouping]="false"
				[_minFractionDigits]="2"
				[_maxFractionDigits]="2"
				[_mode]="fieldNumberMode"
			>
			</pt-input-number>
		</div>

		<!-- concepto tributario -->
		<div
			*ngSwitchDefault
			[ngClass]="
				fieldContainerClass || 'col-4 col-sm-4 col-md-4 col-lg-4'
			"
		>
			<pt-input-text
				[_id]="id"
				[_label]="fieldLabel"
				[_formGroup]="detailForm"
				[_formControlName]="id"
			>
			</pt-input-text>
		</div>
	</ng-container>
</ng-template>
