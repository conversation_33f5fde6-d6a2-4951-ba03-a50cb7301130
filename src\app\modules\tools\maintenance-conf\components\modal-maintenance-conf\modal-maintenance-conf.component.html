<pt-confirmation-message [_data]="confirmationData" ngbAutofocus>
	<pt-card 
	[_title] = "modalInput.title"
	_styleClass="pt-card-secondary">

		<!-- Formulario -->
		<form [formGroup]="maintenanceForm" class="pt-gray-form-section">
			<div class="row">
				<!-- Initial Date-->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-datepicker 
						_id="mantenimentInici"
						_label="MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.maintenanceStart"
						[_formGroup]="maintenanceForm" 
						_formControlName="mantenimentInici"
						[_showTime]="true"
						[_yearNavigator]="true"
						[_yearRange]="yearRange"
						[_minDate]="minDate"
						(_yearChangeEvent)="setYearRange($event)"
						(_changeEvent)="onChange($event)"
						(_inputEvent)="onInputEvent()">
					</pt-datepicker>
				</div>
	
				<!-- Final Date-->
				<div class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-datepicker 
						_id="mantenimentFinal"
						_label="MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.maintenanceEnd"
						[_formGroup]="maintenanceForm" 
						_formControlName="mantenimentFinal"
						[_showTime]="true"
						[_yearNavigator]="true"
						[_yearRange]="yearRange"
						[_minDate]="minDate"
						(_yearChangeEvent)="setYearRange($event)"
						(_changeEvent)="onChange($event)"
						(_inputEvent)="onInputEvent()">
					</pt-datepicker>
				</div>
				
				<!-- Context -->
				<div
					*ngIf="modalInput.mode === maintenanceEnum.FILTER || modalInput.mode === maintenanceEnum.CREATE_CONF" 
					class="col-12 col-sm-4 col-md-4 col-lg-4">
					<pt-select 
						_id="aplicacio" 
						_label="MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_MAINTENANCE_CONF.TABLE.COLUMNS.application"
						[_formGroup]="maintenanceForm" 
						_formControlName="aplicacio" 
						[_options]="contextOptions">
					</pt-select>
				</div>
			</div>
		</form>
	
		<!-- Form buttons -->
		<div class="action-buttons text-right">
			<!-- Clear filters -->
			<pt-button 
				_label="UI_COMPONENTS.BUTTONS.CLEAR" 
				_type="button" 
				_class="p-button-outlined" 
				(_clickFn)="clearFilter()">
			</pt-button>
	
			<!-- Submit -->
			<pt-button 
				[_label]="modalInput.mode === maintenanceEnum.FILTER ? 'UI_COMPONENTS.BUTTONS.SEARCH' : 'UI_COMPONENTS.BUTTONS.CONTINUE'" 
				_type="submit" 
				_class="p-button-primary"
				[_icon]="modalInput.mode === maintenanceEnum.FILTER && 'sli2-magnifier'"
				(_clickFn)="submit()" 
				[_disabled]="maintenanceForm.invalid">
			</pt-button>
		</div>
	
	</pt-card>
</pt-confirmation-message>
