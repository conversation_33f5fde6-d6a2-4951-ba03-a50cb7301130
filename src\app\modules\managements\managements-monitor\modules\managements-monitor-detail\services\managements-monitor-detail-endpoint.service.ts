import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ResponseGetManagements } from '../models/managements-monitor-detail-endpoint.model';

@Injectable({
	providedIn: 'root'
})
export class ManagementsMonitorDetailEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: GET > Get self assessment by id
	getManagement(idEntity: string): Observable<ResponseGetManagements> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlGestionsAdmin,
			url: `/detall/${idEntity}`,
			method: 'get'
		}
		return this.httpService.get(requestOptions);
	}

}