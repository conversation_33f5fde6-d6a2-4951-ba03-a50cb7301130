import { Injectable } from '@angular/core';
import { PtHttpRequest, PtHttpService } from 'pt-ui-components-mf-lib';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';
import { DraftsRequest } from '../models/drafts.model';
import { DraftsResponse } from '../models/drafts-endpoints.model';

@Injectable({
	providedIn: 'root'
})
export class DraftsEndpointsService {

	constructor(
		private httpService: PtHttpService
	) { }

	// Request: POST > Search autoliquidacions by criteria
	getDraftsList(criteria?: DraftsRequest | object): Observable<DraftsResponse> {
		const requestOptions: PtHttpRequest = {
			baseUrl: environment.baseUrlTributs,
			url: "/tracking/esborrany/v1/llistat",
			body: criteria,
			method: 'post'
		}
		return this.httpService.post(requestOptions);
	}
}