import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { TaxesRoutes } from 'src/app/core/models/config.model';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: '',
		redirectTo: TaxesRoutes.SELF_ASSESSMENT_V1,
		pathMatch: 'full'
	},
	{
		path: TaxesRoutes.SELF_ASSESSMENT_V1,
		loadChildren: () => import(`./autoliquidacions-v1/autoliquidacions-v1-routing.module`).then(module => module.AutoliquidacionsV1RoutingModule),
		canActivate:[AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN,UserRoles.TECHNICIAN,UserRoles.READ_ONLY]
		}
	},
	{
		path: TaxesRoutes.SELF_ASSESSMENT_V2,
		loadChildren: () => import(`./autoliquidacions-v2/autoliquidacions-v2-routing.module`).then(module => module.AutoliquidacionsV2RoutingModule),
		canActivate:[AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN,UserRoles.TECHNICIAN,UserRoles.READ_ONLY]
		}
	},
	{
		path: TaxesRoutes.DRAFTS,
		loadChildren: () => import(`./drafts/drafts.module`).then(module => module.DraftsModule),
		canActivate:[AuthGuardService],
		data: {
			allowRoles: [UserRoles.ADMIN,UserRoles.TECHNICIAN,UserRoles.READ_ONLY]
		}
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class TaxesRoutingModule { }
