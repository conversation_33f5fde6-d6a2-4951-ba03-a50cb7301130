import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { DetailPageComponent } from './detail-page.component';
import { DetailCardModule } from '../detail-card';
import { DetailStateTableModule } from '../detail-state-table';
import { DetailTabsModule } from '../detail-tabs';
import { LazyElementsModule } from '@angular-extensions/elements';

@NgModule({
	declarations: [
		DetailPageComponent
	],
	imports: [
		CommonModule,
    DetailCardModule,
    DetailStateTableModule,
    DetailTabsModule,
		TranslateModule.forChild(),
		LazyElementsModule,
		PtUiComponentsMfLibModule
	],
  exports: [DetailPageComponent]
})
export class DetailPageModule { }
