import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LazyElementsModule } from '@angular-extensions/elements';
import { PtUiComponentsMfLibModule, UserRoles } from 'pt-ui-components-mf-lib';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { NifEncriptacioComponent } from './nif-encriptacio.component';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';

const routes: Routes = [
	{
		path: '',
		component: NifEncriptacioComponent,
		canActivate:[AuthGuardService],
    data: { 
      title: 'MODULE_TRACKING.COMPONENT_GESTIONS.COMPONENT_NIF_ENCRYPTION.PAGE_TITLE' ,
      allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN]
    }
	}
];

@NgModule({
	declarations: [
		NifEncriptacioComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule,
		LazyElementsModule,
		RouterModule.forChild(routes)
	]
})
export class NifEncriptacioModule { }
