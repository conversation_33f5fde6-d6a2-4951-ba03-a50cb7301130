import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { PtTabsSection } from 'pt-ui-components-mf-lib';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';

@Component({
	selector: 'app-biometric-signature',
	template: `
		<pt-tabs [_panels]="panels">
			<ng-template [tabContent]="1">
				<app-biometric-signature-table
					[mode]="'actives'"
				></app-biometric-signature-table>
			</ng-template>
			<ng-template [tabContent]="2">
				<app-biometric-signature-table
					[mode]="'historic'"
				></app-biometric-signature-table>
			</ng-template>
		</pt-tabs>
	`,
})
export class BiometricSignatureComponent implements OnInit, OnDestroy {
	translateKey = 'MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABS.';

	panels: PtTabsSection[] = [
		{
			id: '1',
			title: this.translateService.instant(this.translateKey + 'ACTIVES'),
		},
		{
			id: '2',
			title: this.translateService.instant(this.translateKey + 'HISTORIC'),
		},
	];

	private _unsubscribe: Subject<void> = new Subject<void>();

	constructor(
		private translateService: TranslateService,
		private aRouter: ActivatedRoute
	) {}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	ngOnInit(): void {
		this.aRouter.queryParams
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((params) => {
				if (params['tab']) {
					this.panels.forEach((panel) => {
						panel.selected = panel.id === params['tab'];
					});
				}
			});
	}
}
