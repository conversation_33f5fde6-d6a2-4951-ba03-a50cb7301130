<pt-card2 [_type]="Card2Mode.DEFAULT">
    <form [formGroup]="componentForm">
        <div class="col-8 row">
    
            <!-- Login type -->
            <div class="col-12 col-sm-4 col-md-3 col-lg-6">
                <pt-select 
                    _id="method" 
                    _label="MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.FIELDS.METHOD"
                    [_formGroup]="componentForm" 
                    _formControlName="method" 
                    [_options]="loginOptions"
                    (_changeEvent)="onLoginChange()">
                </pt-select>
            </div>

            <!-- Certificate type -->
            <div 
                *ngIf="componentForm.get('method').value === LoginSimulatMethods.CERTIFICAT" 
                class="col-12 col-sm-4 col-md-3 col-lg-6">
                <pt-select 
                    _id="certificateType" 
                    _label="MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.FIELDS.CERTIFICATE"
                    [_formGroup]="componentForm" 
                    _formControlName="certificateType" 
                    [_options]="certificateOptions"
                    (_changeEvent)="onCertChange()">
                </pt-select>
            </div>
        
            <!-- User NIF -->
            <div class="col-12 col-sm-4 col-md-3 col-lg-6">
                <pt-input-text 
                    _id="nifUser" 
                    _label="MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.FIELDS.USER_NIF" 
                    [_formGroup]="componentForm"
                    _formControlName="nifUser">
                </pt-input-text>
            </div>

            <!-- Company NIF -->
            <div 
                *ngIf="componentForm.get('method').value === LoginSimulatMethods.CERTIFICAT && isSelectedCertJuridic()"
                class="col-12 col-sm-4 col-md-3 col-lg-6">
                <pt-input-text 
                    _id="nifCompany" 
                    _label="MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.FIELDS.COMPANY_NIF"
                    [_formGroup]="componentForm" 
                    _formControlName="nifCompany">
                </pt-input-text>
            </div>

            <div class="col-12 col-sm-4 col-md-3 col-lg-6">
                <pt-datepicker
                    _id="dateLogin"
                    _label="MODULE_TRACKING.COMPONENT_SIMULATED_LOGIN.FIELDS.SIMULATION_DATE"
                    [_formGroup]="componentForm"
                    _formControlName="dateLogin"
                    [_yearNavigator]="true"
                    [_showTime]="true"
                    [_yearRange]="yearRange"
                    (_changeEvent)="changeRangeYear($event)"
                >
                </pt-datepicker>
            </div>
        </div>

        <!-- Action button -->
        <div class="action-buttons text-right">
            <pt-button 
                _label="UI_COMPONENTS.BUTTONS.CONTINUE" 
                _type="button" 
                _class="p-button-primary"
                (_clickFn)="submit()"
                [_disabled]="componentForm.invalid">
            </pt-button>
        </div>
    </form>
</pt-card2>