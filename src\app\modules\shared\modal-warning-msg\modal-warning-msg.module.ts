import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { PtUiComponentsMfLibModule } from 'pt-ui-components-mf-lib';
import { ModalWarningMsgComponent } from './modal-warning-msg.component';

@NgModule({
	declarations: [
    ModalWarningMsgComponent
	],
	imports: [
		CommonModule,
		FormsModule,
		ReactiveFormsModule,
		TranslateModule.forChild(),
		PtUiComponentsMfLibModule
	],
  exports: [ModalWarningMsgComponent]
})
export class ModalWarningMsgModule { }
