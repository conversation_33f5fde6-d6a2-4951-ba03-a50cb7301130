/* To learn more about this file see: https://angular.io/config/tsconfig. */
{
	"compileOnSave": false,
	"compilerOptions": {
		"baseUrl": "./",
		"outDir": "./dist/out-tsc",
		"sourceMap": true,
		"declaration": false,
		"downlevelIteration": true,
		"experimentalDecorators": true,
		"moduleResolution": "node",
		"importHelpers": true,
		"target": "es2015",
		"module": "es2020",
		"lib": [
			"es2018",
			"dom"
		]
	},
	"angularCompilerOptions": {
		"enableI18nLegacyMessageIdFormat": false
	}
}
