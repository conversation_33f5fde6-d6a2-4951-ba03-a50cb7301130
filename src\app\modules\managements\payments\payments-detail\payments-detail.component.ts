import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { WcComponentInput } from 'pt-ui-components-mf-lib';
import { AppRoutes } from 'src/app/core/models/config.model';

@Component({
  selector: 'app-payments-detail',
  templateUrl: './payments-detail.component.html'
})
export class PaymentsDetailComponent implements OnInit {

  pagamentsWcInput: WcComponentInput;

  constructor(private router: Router) { 
    //
  }


  ngOnInit(): void {
    this.pagamentsWcInput = {
			module: null,
			component: 'payment-tracking-detail',
			service: null,
		}
  }

  goToList(): void {
    this.router.navigate([AppRoutes.PAGAMENTS])
  }
}
