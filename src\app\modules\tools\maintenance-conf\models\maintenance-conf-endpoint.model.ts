import { PtHttpResponse } from "pt-ui-components-mf-lib";
import { ConfFilters as ConfFilters } from "./maintenance-conf.model";
import { LazyLoadEvent } from "primeng/api";

export class ConfRequest {
	filter: Partial<ConfFilters>;
	options: LazyLoadEvent;

	constructor(params: Partial<ConfFilters>, options: LazyLoadEvent) {
		this.filter = params;
		this.options = options;
	}
}

export interface ConfListResponse extends PtHttpResponse {
	content: {
		results: ConfResponseContent[],
		total: number,
		size: number
	}
}

export interface ConfResponseContent {
	id: string,
	accessible: boolean,
	mantenimentInici: number,
	mantenimentFinal: number,
	isManteniment: boolean
	aplicacio: string,
	nouCapcalera: boolean
}

export interface CreateConfRequest {
	aplicacio: string;
	mantenimentFinalMils: number;
	mantenimentIniciMils: number;
}

export interface UpdateConfRequest extends CreateConfRequest{
	id: string
}