import { Injectable } from '@angular/core';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
	DataStorageService,
	iCommonError,
	PtAuthService,
	PtConfirmationModalService,
	PtModalErrorData,
	PtModalErrorsService,
	PtSelfAssessmentService,
} from 'pt-ui-components-mf-lib';
import { DeleteModalData, States } from '../models/autoliquidacions-v1.model';
import { StatesIcons } from 'src/app/core/models/config.model';
import { ModalWarningMsgComponent } from 'src/app/modules/shared/modal-warning-msg';

@Injectable({
	providedIn: 'root',
})
export class AutoliquidacionsV1Service {
	mapSourceOrigin: Map<string, string> = new Map([
		['1', 'Gaudi'],
		['2', 'e-Spriu'],
		['3', 'BDF'],
		['4', 'SRC'],
		['5', 'DWH'],
		['6', 'Gestor sortides'],
		['7', 'Dali'],
		['8', 'BigSTDE'],
		['9', 'Padoct'],
		['10', 'Portal'],
		['11', 'Altres'],
		['15', 'MIRO'],
		['99', 'Evolució'],
	]);

	constructor(
		public confirmationService: PtConfirmationModalService,
		public translateService: TranslateService,
		public modalErrorsService: PtModalErrorsService,
		public dataStorageService: DataStorageService,
		public modalService: NgbModal,
		public selfAssessmentService: PtSelfAssessmentService,
		public authService: PtAuthService
	) {}

	getStateIcon = (
		state: string,
		mode: 'detail' | 'self-assessment'
	): string => {
		switch (state?.toUpperCase()) {
			case States.CONFIRMED_ERROR:
			case States.TRAMITATED_ERROR:
			case States.DRAFT_ERROR:
			case States.PAYING_ERROR:
			case States.PRESENTING_ERROR:
			case States.VALIDATING_ERROR:
			case States.ERROR:
			case States.NOTIFIED_ERROR:
			case States.DRAFT_GROUPING_ERROR:
			case States.PROCESSING_ERROR:
				return StatesIcons.ERROR;
			case States.CONFIRMED:
			case States.TRAMITATED:
			case States.DRAFT_VALIDATED:
			case States.PAID:
			case States.DRAFT:
			case States.PRESENTED:
			case States.PROCESSED:
			case States.RECIVED:
			case States.RECEIVED:
			case States.VALIDATED:
			case States.DRAFT_GROUPED:
				return StatesIcons.SUCCESS;
			case States.TRAMITATING:
			case States.DRAFT_VALIDATING:
			case States.PAYING:
			case States.PROCESSING:
			case States.PRESENTING:
			case States.VALIDATING:
			case States.DRAFT_GROUPING:
			case States.NOTIFYING:
				return mode === 'self-assessment'
					? StatesIcons.SPINNER
					: StatesIcons.WAITING_WARNING;
			case States.GENERATED:
			case States.DRAFT_REVALIDATE:
				return StatesIcons.WAITING_WARNING;
			case States.NO_PRESENTED:
				return StatesIcons.CLOSE_INFO;
			default:
				return '';
		}
	};

	showErrors = (errorsRows: iCommonError[]): void => {
		if (errorsRows?.length > 0) {
			// Open modal
			const errorData: PtModalErrorData = {
				techMode: true,
				errors: errorsRows,
			};

			this.modalErrorsService.openModal(errorData);
		}
	};

	deleteModal = (deleteData: DeleteModalData): void => {
		if (!this.selfAssessmentService.isAllowDeleteState(deleteData.state)) {
			// Open modal
			const modalRef = this.modalService.open(ModalWarningMsgComponent, {
				size: 'lg',
				windowClass: '',
			});

			// Modal input data
			modalRef.componentInstance.state = deleteData.state;

			return;
		}

		const msgType = deleteData.idReceipt ? 'RECEIPT' : 'NO_RECEIPT';
		this.confirmationService.openModal({
			severity: 'warning',
			title: 'MODULE_TRACKING.COMPONENT_LIQUIDACIO.BUTTONS.DELETE_SELF_ASSESSMENT',
			subtitle: this.translateService.instant(
				`MODULE_TRACKING.COMPONENT_LIQUIDACIO.CONFIRMATION_MESSAGES.DELETE_SELF_ASSESSMENT_${msgType}`,
				{ numJustificant: deleteData.idReceipt }
			),
			confirmLabel: 'UI_COMPONENTS.BUTTONS.CONFIRM',
			confirmFn: deleteData.confirmFn.bind(
				this,
				deleteData.idReceipt,
				deleteData.idSelfAssessment
			),
		});
	};
}
