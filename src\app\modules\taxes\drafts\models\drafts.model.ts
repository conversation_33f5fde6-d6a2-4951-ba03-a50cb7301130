import { LazyLoadEvent } from 'primeng/api';
import { CommonError, PtTableLinkTemplate } from 'pt-ui-components-mf-lib';

export const DRAFTS_FILTER_STORAGE = 'drafts-filter-storage';
export const DRAFTS_DETAIL_STORAGE = 'drafts-detail-storage';

export enum States {
	GENERAT = 'GENERAT',
	ESBORRANY = 'ESBORRANY',
	NO_PRESENTAT = 'NO_PRESENTAT',
	PRESENTANT = 'PRESENTANT',
	PENDENT_PRESENTACIO ="PENDENT_PRESENTACIO",
	PRESENTACIO_ERROR = 'PRESENTACIO_ERROR',
	PRESENTAT = 'PRESENTAT',
	PAGAT = 'PAGAT',
	PAGANT = 'PAGANT',
	PENDENT_PAGAMENT = 'PENDENT_PAGAMENT',
	PAGAMENT_ERROR = 'PAGAMENT_ERROR',
	PAGAMENT_CANCELLAT = 'PAGAMENT_CANCELLAT',
	NOTIFICANT_PAGAMENT = "NOTIFICANT_PAGAMENT",
	NOTIFICACIO_ERROR = 'NOTIFICACIO_ERROR',
	PAGAMENT_NOTIFICAT = 'PAGAMENT_NOTIFICAT',
	TRAMITAT = 'TRAMITAT',
	TRAMITANT = 'TRAMITANT',
	TRAMITACIO_ERROR = 'TRAMITACIO_ERROR',
	CONSOLIDANT = 'CONSOLIDANT',
	CONSOLIDAT = 'CONSOLIDAT',
	CONSOLIDACIO_ERROR = 'CONSOLIDACIO_ERROR',
	ERROR = 'ERROR',
	ESBORRANY_VALIDANT = 'ESBORRANY_VALIDANT',
	ESBORRANY_VALIDAT = 'ESBORRANY_VALIDAT',
	ESBORRANY_ERROR = 'ESBORRANY_ERROR',
	ESBORRANY_AGRUPANT = 'ESBORRANY_AGRUPANT',
	ESBORRANY_AGRUPANT_ERROR = "ESBORRANY_AGRUPANT_ERROR",
	ESBORRANY_AGRUPAT = 'ESBORRANY_AGRUPAT',
	DELETE = 'DELETE'
}

export type StatesT = keyof typeof States;
export type StatesValues<States> = States[keyof States];

export enum FilterStates {
	GENERAT = 'GENERAT',
	DELETE = 'DELETE',
	TRAMITANT = 'TRAMITANT',
	ESBORRANY = 'ESBORRANY',
}

export interface IDrafts {
	id:string;
	impost: string;
	model: string;
	dataAlta:Date;
	dataModificacio:Date;
	nomPresentador: string;
	nifPresentador: string;
	nomTitular: string;
	subjectesPassius: PassiveSubject[];
	nifTitular: string;
	errors: CommonError[];
	tipus:string;
	idioma: string;
	estat: States;
	indEsborrany: boolean;
	indErrEsborrany: boolean;
	indCopia: boolean;
	warnings: CommonError[];
	canviEstat: CanviEstat[];
	dadesAddicionals:string;
}

export interface PassiveSubject {
	dataNaixement: string
	declaracioResponsable: boolean
	edatAlMomentDeLaMeritacio: number
	idPers: string
	indEstranger: boolean
	nif: string
	nom: string
	porcentatgeDret: number
	uuid: string
}

export interface DraftsRow extends IDrafts {
	stateRow: PtTableLinkTemplate;
}

export interface CanviEstat {
	estatAnterior: string,
	estat: string,
	dataCanvi: Date
}

/**
 * Autoliquidacions: search filters
 */
export interface IDraftsFilter {
	dateFromForm: Date;
	dateToForm: Date;
	dateFrom: number;
	dateTo: number;
	model:string;
	nifPresentador:string;
	nifSubjectePassiu:string;
	nifTitular:string;
	esborrany: boolean;
	estat: States[];
}

export class DraftsRequest {
	filter: Partial<IDraftsFilter>;
	options: LazyLoadEvent;

	constructor(params: Partial<IDraftsFilter>, options: LazyLoadEvent) {
		const df = new Date(params.dateFromForm);
		const dt = new Date(params.dateToForm);
		const from: Date = new Date(df.getFullYear(), df.getMonth(), df.getDate(), 0, 0, 0);
		const to: Date = new Date(dt.getFullYear(), dt.getMonth(), dt.getDate() + 1, 0, 0, 0);

		this.filter = {
			...params,
			dateFrom: from.getTime(),
			dateTo: to.getTime(),
			estat: params?.estat?.length > 0 ? params?.estat : null
		}
		this.options = options
	}
}
