import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { SelfAssessmentV2Routes } from 'src/app/core/models/config.model';

const routes: Routes = [
	{
		path: '', 
		redirectTo: SelfAssessmentV2Routes.TABLE, 
		pathMatch: 'full'
	},
	{
		path: SelfAssessmentV2Routes.TABLE,
		loadChildren: () => import(`./modules/autoliquidacions-v2-table/autoliquidacions-v2-table.module`).then(module => module.AutoliquidacionsV2TableModule),
	},
	{
		path: SelfAssessmentV2Routes.DETAIL_ID, 
		loadChildren: () => import(`./modules/autoliquidacions-v2-detail/autoliquidacions-v2-detail.module`).then(module => module.AutoliquidacionsV2DetailModule),
	}
];

@NgModule({
	imports: [RouterModule.forChild(routes)],
	exports: [RouterModule]
})
export class AutoliquidacionsV2RoutingModule { }