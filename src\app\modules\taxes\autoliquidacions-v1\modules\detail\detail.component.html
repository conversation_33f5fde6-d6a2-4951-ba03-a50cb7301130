<app-detail-page
	[pageTitle]="'MODULE_TRACKING.COMPONENT_LIQUIDACIO.DETAIL_MODAL.TITLE'"
	[statesTableTranslation]="'MODULE_TRACKING.STATES'"
	[deleteButtonLabel]="'MODULE_TRACKING.COMPONENT_LIQUIDACIO.BUTTONS.DELETE_SELF_ASSESSMENT'"
	[detailCardTitle]="'MODULE_TRACKING.COMPONENT_LIQUIDACIO.SECTION_RESULTS.MODAL_TITLE'"

	[procedureData]="selfAssessment"
	[idEntity]="idSelfAssessment"
	[idTramit]="idTramitacio"
	[idReceipt]="idReceipt"

	[stateChangeFirstDate]="selfAssessment?.loadDate"
	[stateChanges]="stateChangesRows"
	[stateIcon]="stateIcon"
	[stateLabel]="stateLabel"
	[detailCardFields]="detailCardFields"
	[detailTabInput]="detailTabInput"

	[currentIndex]="currentIndex"
	[totalRows]="totalRows"
	[backUrl]="navigateBackUrl"

	[showPresentationButtons]="showPresentationButtons"
	[downloadButtonsData]="downloadButtonsData"
	[showDeleteButton]="showDeleteButton"
	[deleteAllowRoles]="deleteAllowRoles"

	(getProcedureList)="getSelfAssessmentList()"
	(getProcedure)="getSelfAssessment()"
	(showErrors)="onShowErrors()"
	(showDeleteModal)="deleteModal()"

	(incrementIndex)="currentIndex = currentIndex + 1"
	(decrementIndex)="currentIndex = currentIndex - 1"
>
</app-detail-page>
