﻿html {
    font-size: 100%;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

img {
    /* Responsive images (ensure images don't scale beyond their parents) */
    max-width: 100%;
    /* Part 1: Set a maxium relative to the parent */
    width: auto\9;
    /* IE7-8 need help adjusting responsive images */
    height: auto;
    /* Part 2: Scale the height according to the width, otherwise you get stretching */
    vertical-align: middle;
    border: 0;
    -ms-interpolation-mode: bicubic;
}


button, input, select, textarea {
    margin: 0;
    font-size: 100%;
    vertical-align: middle;
}

button, input {
    *overflow: visible;
    line-height: normal;
}

    button::-moz-focus-inner, input::-moz-focus-inner {
        padding: 0;
        border: 0;
    }

    button, html input[type="button"], input[type="reset"], input[type="submit"] {
        -webkit-appearance: button;
        cursor: pointer;
    }

    label, select, button, input[type="button"], input[type="reset"], input[type="submit"], input[type="radio"], input[type="checkbox"] {
        cursor: pointer;
    }

.clearfix {
    *zoom: 1;
}

    .clearfix:before, .clearfix:after {
        display: table;
        content: "";
        line-height: 0;
    }

    .clearfix:after {
        clear: both;
    }

@media (min-width: 1200px) {
    input, textarea, .uneditable-input {
        margin-left: 0;
    }
}

@media (max-width: 767px) {
    body {
        padding-left: 20px;
        padding-right: 20px;
    }

    input[type="checkbox"], input[type="radio"] {
        border: 1px solid #ccc;
    }
}

@media (max-width: 979px) {
    body {
        padding-top: 0;
    }
}


.payment-credit-cards .payment-credit-cards__list {
    display: flex;
    padding: 16px 0 0;
}

.payment-credit-cards .payment-credit-cards__card:not(last-child) {
    margin-right: 8px;
}

/*
.payment-credit-cards .payment-credit-cards__card img {
    height: 32px
}
    */


form {
    margin: 0 0 20px;
}

label, input, button, select, textarea {
    font-size: 14px;
    font-weight: normal;
    line-height: 20px;
}

input, button, select, textarea {
    font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
}

label {
    display: block;
    margin-bottom: 5px;
}

select, textarea, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .uneditable-input {
    display: inline-block;
    height: 20px;
    padding: 4px 6px;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 20px;
    color: #555;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    vertical-align: middle;
}

input, textarea, .uneditable-input {
    width: 206px;
}

textarea {
    height: auto;
}

textarea, input[type="text"], input[type="password"], input[type="datetime"], input[type="datetime-local"], input[type="date"], input[type="month"], input[type="time"], input[type="week"], input[type="number"], input[type="email"], input[type="url"], input[type="search"], input[type="tel"], input[type="color"], .uneditable-input {
    background-color: #fff;
    border: 1px solid #ccc;
    -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -moz-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
    -webkit-transition: border linear .2s, box-shadow linear .2s;
    -moz-transition: border linear .2s, box-shadow linear .2s;
    -o-transition: border linear .2s, box-shadow linear .2s;
    transition: border linear .2s, box-shadow linear .2s;
}

input[type="radio"], input[type="checkbox"] {
    margin: 4px 0 0;
    *margin-top: 0;
    /* IE7 */
    margin-top: 1px \9;
    /* IE8-9 */
    line-height: normal;
}

input[type="file"], input[type="image"], input[type="submit"], input[type="reset"], input[type="button"], input[type="radio"], input[type="checkbox"] {
    width: auto;
}

select, input[type="file"] {
    height: 30px;
    /* In IE7, the height of the select element cannot be changed by height, only font-size */
    *margin-top: 4px;
    /* For IE7, add top margin to align select with labels */
    line-height: 30px;
}

select {
    width: 220px;
    border: 1px solid #ccc;
    background-color: #fff;
}

input, textarea, .uneditable-input {
    margin-left: 0;
}

.help-block, .help-inline {
    color: #595959;
}

.help-inline {
    display: inline-block;
    *display: inline;
    /* IE7 inline-block hack */
    *zoom: 1;
    vertical-align: middle;
    padding-left: 5px;
}

.tab-content {
    overflow: auto;
}

.tabs-below > .nav-tabs, .tabs-right > .nav-tabs, .tabs-left > .nav-tabs {
    border-bottom: 0;
}

.tab-content > .tab-pane, .pill-content > .pill-pane {
    display: none;
}

.tab-content > .active, .pill-content > .active {
    display: block;
}

.btn {
    display: inline-block;
    *display: inline;
    /* IE7 inline-block hack */
    *zoom: 1;
    padding: 4px 12px;
    margin-bottom: 0;
    font-size: 14px;
    line-height: 20px;
    text-align: center;
    vertical-align: middle;
    cursor: pointer;
    color: #333;
    text-shadow: 0 1px 1px rgba(255, 255, 255, 0.75);
    background-color: #f5f5f5;
    background-image: -moz-linear-gradient(top, #fff, #e6e6e6);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#fff), to(#e6e6e6));
    background-image: -webkit-linear-gradient(top, #fff, #e6e6e6);
    background-image: -o-linear-gradient(top, #fff, #e6e6e6);
    background-image: linear-gradient(to bottom, #fff, #e6e6e6);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffffff', endColorstr='#ffe6e6e6', GradientType=0);
    border-color: #e6e6e6 #e6e6e6 #bfbfbf;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    *background-color: #e6e6e6;
    /* Darken IE7 buttons by default so they stand out more given they won't have borders */
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
    border: 1px solid #ccc;
    *border: 0;
    border-bottom-color: #b3b3b3;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    *margin-left: .3em;
    -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
    -moz-box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
    box-shadow: inset 0 1px 0 rgba(255,255,255,.2), 0 1px 2px rgba(0,0,0,.05);
}

    .btn:hover, .btn:focus, .btn:active, .btn.active, .btn.disabled, .btn[disabled] {
        color: #333;
        background-color: #e6e6e6;
        *background-color: #d9d9d9;
    }

    .btn:active, .btn.active {
        background-color: #cccccc \9;
    }

    .btn:first-child {
        *margin-left: 0;
    }

.btn-primary {
    color: #fff;
    text-shadow: 0 -1px 0 rgba(0, 0, 0, 0.25);
    background-color: #006dcc;
    background-image: -moz-linear-gradient(top, #08c, #0044cc);
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#08c), to(#0044cc));
    background-image: -webkit-linear-gradient(top, #08c, #0044cc);
    background-image: -o-linear-gradient(top, #08c, #0044cc);
    background-image: linear-gradient(to bottom, #08c, #0044cc);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ff0088cc', endColorstr='#ff0044cc', GradientType=0);
    border-color: #0044cc #0044cc #002a80;
    border-color: rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.1) rgba(0, 0, 0, 0.25);
    *background-color: #0044cc;
    /* Darken IE7 buttons by default so they stand out more given they won't have borders */
    filter: progid:DXImageTransform.Microsoft.gradient(enabled = false);
}

button.btn, input[type="submit"].btn {
    *padding-top: 3px;
    *padding-bottom: 3px;
}

html, body {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

p {
    margin: 0;
}

*, *:before, *:after {
    box-sizing: inherit;
}

.clearfix:after {
    display: block;
    clear: both;
    content: "";
}

.payment__row {
    margin: 0;
}

.col-1 {
    width: 8.33%;
}

.col-2 {
    width: 16.66%;
}

.col-3 {
    width: 25%;
}

.col-4 {
    width: 33.33%;
}

.col-5 {
    width: 41.66%;
}

.col-6 {
    width: 25%;
}

.col-7 {
    width: 58.33%;
}

.col-8 {
    width: 66.66%;
}

.col-9 {
    width: 75%;
}

.col-10 {
    width: 83.33%;
}

.col-11 {
    width: 91.66%;
}

.col-12 {
    width: 100%;
}

[class^="col-"] {
    float: left;
    padding-right: 10px;
    padding-left: 10px;
    /*&:first-of-type {
		padding-left: 0;
	}

	&:last-of-type {
		padding-right: 0;
	} */
}

input, select {
    box-sizing: border-box;
    width: 100%;
}

.sr-only {
    position: absolute;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    border: 0;
}

.payment {
    margin: 0;
}

    .payment input, .payment select {
        font-family: HelveticaNeue, arial, san-serif;
        font-size: 0.875em;
        line-height: 1;
        height: auto;
        margin: 0;
        margin-bottom: 0;
        padding: 11px 10px 11px;
        -webkit-border-radius: 8px;
        -moz-border-radius: 8px;
        border-radius: 8px;
        max-width: 150px;
    }

    .payment select {
        height: 40px;
    }

    .payment img {
        float: right;
        width: auto;
        /*height: 20px;*/
    }

.payment__row {
    margin-right: -10px;
    margin-left: -10px;
}

    .payment__row::before {
        display: table;
        content: "";
    }

.payment__row--card {
    margin: 0;
    padding: 0 20px 10px;
    border-bottom: 1px solid #D9D9D9;
}

.payment .tab-content {
    overflow: visible;
}

.payment__main {
    padding: 25px 20px 10px;
    background: #EEEEEE;
}

.payment__main--card {
    padding: 65px 0 0;
}

.payment__title {
    font-family: HelveticaNeue, arial, san-serif;
    font-size: 1.125em;
    line-height: 1;
    margin-top: 0;
    margin-bottom: 24px;
}

.payment__label {
    font-family: "HelveticaNeue", arial, san-serif;
    font-size: 0.9375em;
    color: #333333;
    line-height: 1;
    margin-bottom: 8px;
    padding-top: 25px;
}


.payment__footer {
    min-height: 65px;
    padding: 12px 0 13px;
    background-color: #D9D9D9;
}

.payment__iframe {
    width: 100%;
}

    .payment__iframe .col-lg-4 {
        width: 100%;
    }

    .payment__iframe .payment__main {
        float: none;
        width: 100%;
    }

    .payment__iframe .payment__main--card {
        padding: 25px 20px 10px;
    }

    .payment__iframe .payment__title {
        margin-bottom: 24px;
    }

    .payment__iframe {
        float: none;
        width: 100%;
        min-height: 65px;
    }

    .payment__iframe input[type="submit"].payment__btn, .payment__iframe input[type="submit"].payment__btn:hover, .payment__iframe input[type="submit"].payment__btn:focus {
        margin: 0 auto;
    }

    .payment__iframe .card-payment__cvv-col {
        clear: unset;
    }

.custom-checkbox {
    position: relative;
    float: left;
    width: 130px;
    cursor: pointer;
}

    .custom-checkbox label {
        position: absolute;
        z-index: 3;
        top: 9px;
        width: 100%;
        height: 100%;
    }

    .custom-checkbox .custom-control-input {
        position: relative;
        display: none;
        opacity: 0;
    }

        .custom-checkbox .custom-control-input ~ .custom-control-indicator {
            color: #0070ba;
            text-align: center;
            position: relative;
            top: 9px;
            left: 0;
            display: block;
            float: left;
            width: 20px;
            height: 20px;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            pointer-events: none;
            border: 1px solid #CDCDCD;
            -webkit-border-radius: 25%;
            -moz-border-radius: 25%;
            border-radius: 25%;
            background: white;
        }

.card-payment__cvv-wrapper {
    position: relative;
    min-height: 47px;
    min-height: 36px;
    margin-top: -2px;
}

.card-payment__cvv-text {
    font-family: HelveticaNeue, arial, san-serif;
    font-size: 0.875em;
    color: #666666;
    position: absolute;
    top: 11px;
    float: left;
    padding-left: 27px;
}

.card-payment__info {
    font-size: 0.75em;
    color: #333333;
    line-height: 15px;
    text-align: center;
    vertical-align: middle;
    position: absolute;
    top: 11px;
    right: 10px;
    display: table;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #0070ba;
}

.card-payment__cvv-text--help-inline {
    font-family: HelveticaNeue, arial, san-serif;
    font-size: 0.875em;
    font-weight: bold;
    color: #0070ba;
    position: absolute;
    top: 11px;
    right: 25px;
    float: left;
    padding-right: 8px;
    padding-left: 25px;
}

.tooltip > .tooltipinfo {
    font-family: Neo Sans, arial, san-serif;
    font-size: 1.16667em;
    color: black;
    text-align: left;
    position: absolute;
    z-index: 1;
    right: 0;
    bottom: 28px;
    display: block;
    visibility: hidden;
    width: 265px;
    padding: 15px 16px 16px 9px;
    transition: opacity 1s;
    /* Fade in tooltip */
    opacity: 0;
    background-color: #3DBBD1;
}

    .tooltip > .tooltipinfo::after {
        position: absolute;
        top: 100%;
        right: 0;
        content: "";
        border-width: 5px;
        border-style: solid;
        border-color: #3DBBD1 transparent transparent #3DBBD1;
    }

.tooltip:hover > .tooltipinfo {
    visibility: visible;
    opacity: 1;
}

.tooltip__text {
    line-height: 1.28571em;
    text-align: initial;
    position: relative;
    float: right;
    width: 202px;
}

.tooltipinfo__icon {
    font-family: Neo Sans, arial, san-serif;
    font-size: 1.5em;
    color: #434544;
    line-height: 24px;
    text-align: center;
    float: left;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: white;
}

.tooltipinfo__text {
    padding-left: 38px;
}

input[type="submit"].finish__btn, input[type="submit"].payment__btn, input[type="submit"].payment__btn:hover, input[type="submit"].payment__btn:focus {
    font-family: HelveticaNeue, arial, san-serif;
    font-size: 1.125em;
    font-weight: bold;
    color: white;
    line-height: 40px;
    text-align: center;
    display: block;
    width: 140px;
    margin: 0 auto;
    padding: 0;
    border: none;
    background-color: #0070ba;
    background-image: none;
    text-shadow: none;
}

/* IE9 uses \9 */
@media all and (monochrome: 0) {
    .custom-control-indicator {
        visibility: visible;
    }
}

/* IE10 and IE11 both use -ms-high-contrast */
@media all and (-ms-high-contrast: none) {
    .custom-control-indicator {
        visibility: visible;
    }
}

.card-payment__card-number {
    width: 100%;
}

.card-payment__expiry-date {
    width: 100%;
}

.card-payment__cvv {
    width: 100%;
}

.payment__footer {
    width: 100%;
}

#cvvNumber {
    max-width: 70px;
}

#numero1, #numero2, #numero3, #numero4 {
    max-width: 80px;
}

.card-payment__card-number > div {
    width: 20%;
}

@media (min-width: 768px) {
    .payment__main {
        min-height: 310px;
        padding-top: 20px;
    }

    .tab-content--select {
        margin-top: 0;
    }

    .card-payment__cvv-col {
        clear: both;
    }

    .payment__iframe {
        width: 100%;
    }

    .main__container {
        padding: 30px 90px;
    }

    .main__text {
        display: block;
    }

    .main__image {
        width: 220px;
        max-width: 220px;
    }

    .main__float--card {
        padding-bottom: 50px;
    }

    .payment__modal .modal-dialog {
        width: 640px;
    }

    .payment__modal .main__image {
        width: 72px;
    }

    .payment__modal .main__float__body {
        padding-left: 55px;
    }

    .card-payment__card-number {
        width: 50%;
    }

    .card-payment__expiry-date {
        width: 60%;
    }

    .card-payment__cvv {
        width: 50%;
    }

    .payment__footer {
        width: 50%;
    }
}

/* Large devices (tablets, 1280px and up) */
@media (min-width: 1280px) {
    .payment__main {
        float: left;
        width: 78.125%;
        min-height: 285px;
        background-color: #EEEEEE;
    }

    .payment__title {
        margin-bottom: 20px;
    }

    .payment__footer {
        float: left;
        width: 21.875%;
        min-height: 285px;
    }

    input[type="submit"].payment__btn,
    input[type="submit"].payment__btn:hover,
    input[type="submit"].payment__btn:focus {
        margin-top: 104px;
    }

    .card-payment__cvv-col {
        clear: unset;
    }

    .payment__iframe {
        width: 100%;
    }

    .payment--netplus .payment__iframe {
        position: relative;
        padding: 0 50px 0 40px;
    }

    .payment--netplus .payment__iframe:before {
        position: absolute;
        top: 40%;
        left: -10px;
        content: "\A";
        border-width: 21px 0 21px 25px;
        border-style: solid;
        border-color: transparent transparent transparent #FFFFFF;
    }

    .main__logo {
        width: 165px;
        max-width: 165px;
    }

    .main__title {
        margin-bottom: 21px;
    }

    .card-payment__card-number {
        width: 50%;
    }

    .card-payment__expiry-date {
        width: 35%;
    }

    .card-payment__cvv {
        width: 50%;
    }

    .payment__footer {
        width: 50%;
    }
}


.payment-credit-cards__list {
    list-style: none;
}

    .payment-credit-cards__list .payment-credit-cards__card {
        position: relative;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: vertical;
        -webkit-box-direction: normal;
        -ms-flex-direction: column;
        flex-direction: column;
        margin: 0 16px 0 0;
        padding: 10px;
        -webkit-box-pack: center;
        -ms-flex-pack: center;
        justify-content: center;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        width: 100px;
        height: 50px;
        -webkit-box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.16);
        box-shadow: 0 1px 9px 0 rgba(0, 0, 0, 0.16);
        background: white;
        border-radius: 5px;
        color: #333333;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }

        .payment-credit-cards__list .payment-credit-cards__card:last-of-type {
            margin: 0;
        }

.payment__iframe .payment__title {
    font-family: Verdana,Arial,sans-serif;
    font-size: 11px;
    font-weight: normal;
    margin: 0;
}

.payment__label {
    font-family: Verdana,Arial,sans-serif;
    font-size: 11px;
    position: relative;
    margin-bottom: 12px;
}

.payment input, .payment select {
    height: 35px;
    font-family: Verdana,Arial,sans-serif;
    font-size: 11px;
    color: #333333;
    -webkit-box-shadow: none;
    box-shadow: none;
    border-radius: 5px;
    border: solid 1px #595853;
    outline: none;
    background-color: #ffffff;
}

.payment select {
    background: #fff url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' width='4' height='5' viewBox='0 0 4 5'%3e%3cpath fill='%23343a40' d='M2 0L0 2h4zm0 5L0 3h4z'/%3e%3c/svg%3e") no-repeat right 0.75rem center/8px 10px;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.card-payment__info {
    background: white;
    color: #004e9b;
    border: 1px solid #004e9b;
    width: 17px;
    height: 17px;
}

.tooltip > .tooltipinfo {
    background-color: #004e9b;
    color: white;
}

    .tooltip > .tooltipinfo:after {
        border-color: #004e9b transparent transparent #004e9b;
    }

.card-payment__expiry-date {
    display: flex;
    float: left;
}

.card-payment__cvv-text--help-inline {
    color: #333;
}

.payment__iframe input[type="submit"].payment__btn:hover, .payment__iframe input[type="submit"].payment__btn:focus, .payment__iframe input[type="submit"].payment__btn {
    background: #004e9b;
    color: white;
    font-weight: 400;
}

.payment__main, .payment__footer {
    background: white;
}

.custom-checkbox .custom-control-input:checked ~ .custom-control-indicator {
    background: #004e9b;
    color: white;
    border: 1px solid #004e9b;
}


#pay {
    display: flex;
    float: right;
    width: 195px;
    height: 40px;
    line-height: 0px !important;
    margin: 1px !important;
    padding: 0px 10px 0px 10px;
    background: white;
    color: black;
    border: 1px solid #686868;
    border-style: solid;
    border-width: thin;
    cursor: pointer;
    font-family: Verdana,Arial,sans-serif;
    font-size: 11px;
    font-weight: normal;
    text-align: center;
    zoom: 1;
    overflow: visible;
    max-width: none;
    width: 160px !important;
}

#pay:hover {
    border: 1px solid #686868;
    background: #333333;
    font-weight: normal;
    color: #ffffff;
}

#expiryMonth {
    max-width: 80px;
    min-width: 50px;
}

#expiryYear {
    max-width: 80px;
    min-width: 70px;
}

.col-6 {
    width: 80px;
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
    .payment-credit-cards .payment-credit-cards__card img {
        height: 100%;
    }

    .payment input, .payment select {
        height: 30px;
        font-size: 12px;
        padding: 4px 5px 4px 5px;
    }

    .col-3 {
        width: 20%;
    }

    .payment-credit-cards__list .payment-credit-cards__card {
        padding: 0px 0px;
    }

    .payment__iframe .payment__title {
        font-size: 12px;
    }

    .payment__label {
        font-size: 12px;
    }

}

iframe{
    width: 100%;
    border: none;
    height: 600px;
}