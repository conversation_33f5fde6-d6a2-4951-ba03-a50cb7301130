import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import { GestionsComponent } from './gestions.component';
import { UserRoles } from 'pt-ui-components-mf-lib';
import { AuthGuardService } from 'src/app/core/services/auth-guard.service';
import { LazyElementsModule } from '@angular-extensions/elements';


const routes: Routes = [
	{
		path: '',
		component: GestionsComponent,
		canActivate: [AuthGuardService],
		data: {
			title: 'MODULE_TRACKING.COMPONENT_AREA_PRIVADA.COMPONENT_GESTIONS.PAGE_TITLE',
			allowRoles: [UserRoles.ADMIN, UserRoles.TECHNICIAN],
			isElementVisible: false
		},
	}
];

@NgModule({
  declarations: [GestionsComponent],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	]
})


export class GestionsModule { }
