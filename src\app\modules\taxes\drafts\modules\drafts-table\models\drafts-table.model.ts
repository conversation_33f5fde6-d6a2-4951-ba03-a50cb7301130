import { PtValidations } from "pt-ui-components-mf-lib";
import { FilterStates, IDraftsFilter, States } from "../../../models/drafts.model";
import { Validators } from "@angular/forms";
import { FilterFieldType, FilterFieldsData } from "src/app/modules/shared/filter-inputs";
import { ExportColumnsData } from "src/app/core/models/export-table.model";

export enum TableColumns {
  id = 'id',
  nomPresentador = 'nomPresentador',
  nifPresentador = 'nifPresentador',
  nomTitular = 'nomTitular',
  nifTitular = 'nifTitular',
  model = 'model',
  nomSubjectePassiu = 'nomSubjectePassiu',
  nifSubjectePassiu = 'nifSubjectePassiu',
  dataModificacio = 'dataModificacio',
  dataAlta = 'dataAlta',
  estat = 'estat',
  stateRow = 'stateRow',
  tableActions = 'tableActions',
  esborrany = 'esborrany',
  indEsborrany = 'indEsborrany',
	indErrEsborrany = 'indErrEsborrany',
  indCopia = 'indCopia'
}

export enum TableSortFields {
  nifPresentador = "presentador.nifPresentador",
  nomPresentador = "presentador.nombrePresentador",
  nifSubjectePassiu ="subjectePassiu.nif",
  nomSubjectePassiu = "subjectePassiu.nom",
  stateRow = 'estat',
  nomTitular = 'presentador.nombreCompleto',
  nifTitular = 'presentador.nif',
}

export const EXPORT_COLUMNS_DATA: ExportColumnsData[] = [
  {
    id: TableColumns.id,
    columnTitle: "ID",
    columnType: 'text',
  },
  {
    id: TableColumns.nifPresentador,
    columnTitle: "Nif_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.nomPresentador,
    columnTitle: "Nom_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.nifTitular,
    columnTitle: "Nif_Titular",
    columnType: 'text',
  },
  {
    id: TableColumns.nomTitular,
    columnTitle: "Nom_Titular",
    columnType: 'text',
  },
  {
    id: TableColumns.model,
    columnTitle: "Model",
    columnType: 'text',
  },
  {
    id: "subjectesPassius",
    columnTitle: "Nif_Subjecte_Passiu",
    columnType: 'array',
    attr: 'nif',
  },
  {
    id: "subjectesPassius",
    columnTitle: "Nom_Subjecte_Passiu",
    columnType: 'array',
    attr: 'nom',
  },
  {
    id: TableColumns.dataModificacio,
    columnTitle: "Data_Modificacio",
    columnType: 'date',
  },
  {
    id: TableColumns.dataAlta,
    columnTitle: "Data_Alta",
    columnType: 'date',
  },
  {
    id: TableColumns.indErrEsborrany,
    columnTitle: "Err_Esborrany",
    columnType: 'boolean',
  },
  {
    id: TableColumns.indEsborrany,
    columnTitle: "Esborrany",
    columnType: 'boolean',
  },
  {
    id: TableColumns.indCopia,
    columnTitle: "Copia",
    columnType: 'boolean',
  },
  {
    id: TableColumns.estat,
    columnTitle: "Estat",
    columnType: 'translation',
    translation: 'MODULE_TRACKING.MODULE_DRAFTS.STATES'
  },
  // {
  //   id: TableColumns.esborrany,
  //   columnTitle: "Estat",
  //   columnType: 'textToBoolean'
  // },
]

export enum FilterFormFields {
  dateFromForm = 'dateFromForm',
  dateToForm = 'dateToForm',
  model = 'model',
	nifPresentador = 'nifPresentador',
	nifTitular = 'nifTitular',
	nifSubjectePassiu = 'nifSubjectePassiu',
	esborrany = 'esborrany',
	errEsborrany = 'errEsborrany',
	estat='estat',
  indCopia='indCopia'
}
export type FilterFormFieldsT = keyof typeof FilterFormFields;

const FILTER_FIELDS_TRANSLATIONS = "MODULE_TRACKING.MODULE_DRAFTS.TABLE.FILTER.FIELDS";
export const FILTER_FORM_DATA:FilterFieldsData[][] = [
  [ 
    {
      id: FilterFormFields.dateFromForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateFromForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.dateToForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateToForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.estat,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.estat}`,
      fieldType: FilterFieldType.multiSelect,
      optionsValue: Object.entries(FilterStates),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_DRAFTS.STATES'
    },
    {
      id: FilterFormFields.model,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.model}`,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.nifPresentador,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifPresentador}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.nifTitular,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifTitular}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.nifSubjectePassiu,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.nifSubjectePassiu}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text
    },
  ],
  [
    {
      id: FilterFormFields.esborrany,
      label: `${FILTER_FIELDS_TRANSLATIONS}.esborranyLabel`,
      fieldType: FilterFieldType.checkbox,
      options: [
        { id: 'esborrany', label: `${FILTER_FIELDS_TRANSLATIONS}.esborrany` },
      ]
    },
    {
      id: FilterFormFields.errEsborrany,
      containerClass: 'col-12 col-sm-3 col-md-3 col-lg-3 align-self-end',
      fieldType: FilterFieldType.checkbox,
      options: [
        { id: 'errEsborrany', label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.errEsborrany}` },
      ]
    },
    {
      id: FilterFormFields.indCopia,
      containerClass: 'col-12 col-sm-3 col-md-3 col-lg-3 align-self-end',
      fieldType: FilterFieldType.checkbox,
      options: [
        { id: 'indCopia', label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.indCopia}` },
      ]
    }
  ]
]

export class FilterFormValue implements IDraftsFilter {
  dateFromForm: Date;
	dateToForm: Date;
	dateFrom: number;
	dateTo: number;
	model:string;
	nifPresentador:string;
	nifSubjectePassiu:string;
	nifTitular:string;
	esborrany: boolean;
	estat: States[];

  constructor(
    data?: IDraftsFilter
  ) {
    const dateFrom = data?.dateFromForm ? new Date(data.dateFromForm) : new Date();
    const dateTo = data?.dateToForm ? new Date(data.dateToForm) : new Date();
    if (!data?.dateFromForm) dateFrom.setDate(dateFrom.getDate() - 30);

    Object.assign(this, {
      ...data,
      dateFromForm: dateFrom,
      dateToForm: dateTo,
    });
  }
  
}