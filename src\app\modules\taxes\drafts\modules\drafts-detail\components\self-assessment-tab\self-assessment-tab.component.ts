import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { PtTableColumn } from 'pt-ui-components-mf-lib';
import { Subject } from 'rxjs';
import { DraftsDetailEndpointsService } from '../../services/drafts-detail-endpoint.service';
import { takeUntil } from 'rxjs/operators';
import { DraftsService } from '../../../../services/drafts.service';
import { Router } from '@angular/router';
import { AppRoutes, SelfAssessmentV2Routes, TaxesRoutes } from 'src/app/core/models/config.model';

@Component({
	selector: 'app-self-assessment-tab',
	templateUrl: './self-assessment-tab.component.html',
	styleUrls: ['./self-assessment-tab.component.sass'],
})
export class SelfAssessmentTabComponent implements OnInit, OnDestroy {
	@Input() set idDraft(id: string) {
    this.setTableRows(id);
	}

	// Table
	tableColumns: PtTableColumn[] = [];
	tableRows: any[] = [];

	private _unsubscribe: Subject<void> = new Subject();

	constructor(
    private endpointsService: DraftsDetailEndpointsService, 
    private draftService: DraftsService,
		private router: Router
  ) {}

	ngOnDestroy(): void {
		this._unsubscribe.next();
		this._unsubscribe.complete();
	}

	ngOnInit(): void {
		this.tableColumns = this.setTableColumns();
	}

	private setTableColumns(): PtTableColumn[] {
		return [
			{
				id: 'numJustificant',
				label: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.numJustificant',
				isResizable: true,
				isSortable: true,
				template: 'link',
				width: '20%',
			},
			{
				id: 'stateRow',
				label: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.estat',
				isResizable: true,
				isSortable: true,
				template: 'icon',
				width: '15%',
				options: {
					removeOpacity: true,
					cellClass: 'justify-content-start',
				},
			},
      {
				id: 'nomSubjectePassiu',
				label: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nifSubjectePassiu',
				isResizable: true,
				isSortable: true,
				width: '25%',
			},
			{
				id: 'nifSubjectePassiu',
				label: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.nifSubjectePassiu',
				isResizable: true,
				isSortable: true,
				width: '25%',
			},
			{
				id: 'importe',
				label: 'MODULE_TRACKING.MODULE_SELFASSESSMENT_V2.TABLE.COLUMNS.totalIngressar',
				isResizable: true,
				isSortable: true,
				width: '15%',
				template: 'currency',
			},
		];
	}

	private setTableRows(idDraft: string): void {
		this.tableRows = [];
		
		if(!idDraft) return;

		this.endpointsService
			.getSelfAssessments(idDraft)
			.pipe(takeUntil(this._unsubscribe))
			.subscribe((response) => {
				if (response?.content?.length) {
					this.tableRows = response.content.map(selfAssessment => {
            const stateIcon = this.draftService.getStateIcon(
              selfAssessment?.estat,
              'detail'
            );

            return Object.assign(
              {},
              selfAssessment,
              {
								numJustificant: {
									id: `button${selfAssessment.id}`,
									label: selfAssessment.numJustificant,
									clickFn: this.navigateToSelfAssessmentDetail.bind(
										this,
										selfAssessment.id
									),
									disabled: !selfAssessment.id,
								},
                stateRow: {
                  id: `button${selfAssessment.id}`,
                  icon: stateIcon,
                  iconPos: 'left',
                  label: selfAssessment.estat
                    ? this.draftService.translateService.instant(
                        `MODULE_TRACKING.MODULE_DRAFTS.STATES.${selfAssessment.estat}`
                      )
                    : '',
                },
              }
            )
          });
				}
			});
	}

	private navigateToSelfAssessmentDetail(id:string) {
		const urlTree = this.router.createUrlTree(['/' + AppRoutes.TAXES.concat('/',TaxesRoutes.SELF_ASSESSMENT_V2,'/',SelfAssessmentV2Routes.DETAIL), id]);
		const url = this.router.serializeUrl(urlTree);
		
		window.open(url, '_blank');
	}
}
