import { Component, OnInit } from '@angular/core';
import { WebComponentsService } from 'pt-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Component({
	selector: 'app-tearc-monitor',
	templateUrl: './tearc-monitor.component.html',
	styleUrls: ['./tearc-monitor.component.sass']
})
export class TearcMonitorComponent {
	
	// Webcomponent > URLs
	wcUrlCss = environment.wcUrlTearcMonitorCss;

	constructor(
		private webComponentService: WebComponentsService
	) {
		this.webComponentService.setWebComponentStyle(this.wcUrlCss, 'tearc-monitor');
	}

}
