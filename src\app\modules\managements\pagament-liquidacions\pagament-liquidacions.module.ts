import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PagamentLiquidacionsComponent } from './pagament-liquidacions.component';
import { RouterModule, Routes } from '@angular/router';
import { LazyElementsModule } from '@angular-extensions/elements';


const routes: Routes = [
	{
		path: '', component: PagamentLiquidacionsComponent,
		data: { 
			title: '',
			isElementVisible: false
		}
	}
];

@NgModule({
	declarations: [
		PagamentLiquidacionsComponent
	],
	imports: [
		CommonModule,
		RouterModule.forChild(routes),
		LazyElementsModule
	],
	exports: [
		RouterModule
	]
})
export class PagamentLiquidacionsModule { }
