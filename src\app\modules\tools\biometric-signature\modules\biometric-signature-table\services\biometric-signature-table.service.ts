import { Injectable } from '@angular/core';
import { PtDateFormatService, PtHeaderTagList, PtTableColumn, PtTableColumnTemplate, PtValidations } from 'pt-ui-components-mf-lib';
import { FilterModalInput } from 'src/app/modules/shared/modal-filter';
import { TableColumns, FilterFormValue, FILTER_FORM_DATA, BiometricSignatureFilter, TranslateTags, FilterFormFields, BiometricSignatureFilterModal } from '../models';
import { TranslateService } from '@ngx-translate/core';

const FILTER_FIELDS_TRANSLATIONS = "MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.FILTER.FIELDS";
@Injectable({
  providedIn: 'root'
})
export class BiometricSignatureTableService {

	constructor(
		private dateFormatService: PtDateFormatService,
		private translateService: TranslateService,
	) { }

	getTableColumns = (): PtTableColumn[] => [
		{
			id: TableColumns.idPeticio, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.idPeticio}`,
			isResizable: true, isSortable: true, width: "14%",
		},
		{
			id: TableColumns.nomDocument, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.nomDocument}`,
			isResizable: true, isSortable: true, width: "16%"
		},
		{
			id: TableColumns.metadades, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.metadades}`,
			isResizable: true, isSortable: false, width: "10%", template: PtTableColumnTemplate.link,
		},
		{
			id: TableColumns.nomDispositiu, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.nomDispositiu}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.repositoriDocumental, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.repositoriDocumental}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.nomSignant, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.nomSignant}`,
			isResizable: true, isSortable: true, width: "14%"
		},
		{
			id: TableColumns.tipusIdentificadorSignant, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.tipusIdentificadorSignant}`,
			isResizable: true, isSortable: true, width: "10%"
		},
		{
			id: TableColumns.identificadorSignant, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.identificadorSignant}`,
			isResizable: true, isSortable: true, width: "8%"
		},
		{
			id: TableColumns.tipusSignatura, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.tipusSignatura}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.aplicacio, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.aplicacio}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.estatPeticio, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.estatPeticio}`,
			isResizable: true, isSortable: true, template: 'link', width: "10%",
			options: { removeOpacity: true, cellClass: 'justify-content-start' }
		},
		{
			id: TableColumns.idVidSigner, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.idVidSigner}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.idRepositoriDocumental, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.idRepositoriDocumental}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.dataCreacio, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.dataCreacio}`,
			isResizable: true, isSortable: true, width: "11%",template: "date",
			options: { dateFormat: 'dd/MM/yyyy HH:mm' }
		},
		{
			id: TableColumns.dataActualitzacio, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.dataActualitzacio}`,
			isResizable: true, isSortable: true, width: "11%",template: "date",
			options: { dateFormat: 'dd/MM/yyyy HH:mm' }
		},
		{
			id: TableColumns.reintents, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.reintents}`,
			isResizable: true, isSortable: true, isToggled: true, width: "10%"
		},
		{
			id: TableColumns.errors, label: `MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.TABLE.COLUMNS.${TableColumns.errors}`,
			isResizable: true, isSortable: false, width: "8%", template: PtTableColumnTemplate.link,
		},
		{
			id: TableColumns.tableActions, label: null, isResizable: false, isSortable: false,
			template: PtTableColumnTemplate.buttons, width: '4%'
		}
	];

	getModalInput = (formValue): FilterModalInput => {
		return {
			formValue,
			formResetValue: new FilterFormValue(),
			formFieldsData: FILTER_FORM_DATA,
			formGroupValidations: [
				PtValidations.equalsOrLessThan(FilterFormFields.dataActualitzacioTo, FilterFormFields.dataActualitzacioFrom, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan', false),
				PtValidations.equalsOrGreaterThan(FilterFormFields.dataActualitzacioFrom, FilterFormFields.dataActualitzacioTo, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan', false),
				PtValidations.equalsOrLessThan(FilterFormFields.dataCreacioTo, FilterFormFields.dataCreacioFrom, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrLessThan', false),
				PtValidations.equalsOrGreaterThan(FilterFormFields.dataCreacioFrom, FilterFormFields.dataCreacioTo, 'UI_COMPONENTS.VALIDATIONS_ERRORS.equalsOrGreaterThan', false),
			]
		}
	}

	getFilterTagList(filterValue: Partial<BiometricSignatureFilterModal>): PtHeaderTagList[] {
		if (!filterValue) return;

		const tagListToTranslate: string[] = Object.values(TranslateTags);

		return [].concat(...Object.entries(filterValue).filter(
			value => value[1]
		).map(entry => {

			if (Array.isArray(entry[1])) {
				return  (entry[1] as Array<any>).map((arrayEntry, index) => {
					return {
						id: entry[0] + '-' + (arrayEntry instanceof Object ? index : arrayEntry),
						value: this.getTagValue(arrayEntry, tagListToTranslate.includes(entry[0]), entry[0])
					}
				})
			}

			return {
				id: entry[0],
				value: this.getTagValue(entry[1], tagListToTranslate.includes(entry[0]), entry[0]),
			}
		}))
	}

	private getTagValue (tagValue: string | Date | any , translate: boolean = false, filterKey?: string): string {
		const label = this.translateService.instant(FILTER_FIELDS_TRANSLATIONS + '.' + filterKey);

		if (tagValue instanceof Date)
			return  label + ': '+ this.dateFormatService.fromDateToFormat(tagValue, 'dd/MM/yyyy');

		if (translate) {
			return this.translateService.instant(`MODULE_TRACKING.MODULE_BIOMETRIC_SIGNATURE.ENUMS.${filterKey}.${tagValue}`);
		}

		if (tagValue instanceof Object) {
			return label + ': ' + Object.values(tagValue).join(' - ');
		}

		return label + ': '+ tagValue.toString();
	}
}
