import { PtValidations } from "pt-ui-components-mf-lib";
import { ManagementsMonitorFilter, OriginOptions, States, TramitType } from "../../../models/managements-monitor.model";
import { Validators } from "@angular/forms";
import { FilterFieldType, FilterFieldsData } from "src/app/modules/shared/filter-inputs";
import { ExportColumnsData } from "src/app/core/models/export-table.model";

export enum TableColumns {
  idTramitacio = 'idTramitacio',
  origen = 'origen',
  presentadorNom = 'presentadorNom',
  presentadorNif = 'presentadorNif',
  subjectePassiuNom = 'subjectePassiuNom',
  subjectePassiuNif = 'subjectePassiuNif',
  dataModificacio = 'dataModificacio',
  estat = 'estat',
  stateRow = 'stateRow',
  tableActions = 'tableActions',
  tramit='tramit',
  dadesAddicionals ='dadesAddicionals'
}

export enum TableSortFields {
  idTramitacio = "presentacio.idTramitacio",
  presentadorNif = "presentador.nifPresentador",
  presentadorNom = "presentador.nombrePresentador",
  subjectePassiuNif ="subjectesPassius.nif",
  subjectePassiuNom = "subjectesPassius.nom",
  origen = "backOffice",
  stateRow = 'estat'
}

export const EXPORT_COLUMNS_DATA: ExportColumnsData[] = [
  {
    id: TableColumns.idTramitacio,
    columnTitle: "ID_Tramitacio",
    columnType: 'text'
  },
  {
    id: TableColumns.estat,
    columnTitle: "Estat",
    columnType: 'translation',
    translation: 'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.STATES'
  },
  {
    id: TableColumns.presentadorNif,
    columnTitle: "Nif_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.presentadorNom,
    columnTitle: "Nom_Presentador",
    columnType: 'text',
  },
  {
    id: TableColumns.subjectePassiuNif,
    columnTitle: "Nif_Subjecte_Passiu",
    columnType: 'text',
  },
  {
    id: TableColumns.subjectePassiuNom,
    columnTitle: "Nom_Subjecte_Passiu",
    columnType: 'text',
  },
  {
    id: TableColumns.origen,
    columnTitle: "Origen",
    columnType: 'text',
  },
  {
    id: TableColumns.tramit,
    columnTitle: "Tipus",
    columnType: 'text',
  },
  {
    id: TableColumns.dadesAddicionals,
    columnTitle: "Dades_Addicionals",
    columnType: 'text',
  },
  {
    id: TableColumns.dataModificacio,
    columnTitle: "Data_Modificacio",
    columnType: 'date',
  },
]

export enum FilterFormFields {
  dateFromForm = 'dateFromForm',
  dateToForm = 'dateToForm',
  state = 'state',
  presentadorNif = 'presentadorNif',
  subjectePassiuNif = 'subjectePassiuNif',
  tipus = 'tipus',
  dadesAddicionals = 'dadesAddicionals',
  origen = 'origen',
  idTramitacio = 'idTramitacio'
}
export type FilterFormFieldsT = keyof typeof FilterFormFields;

const FILTER_FIELDS_TRANSLATIONS = "MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.FILTER.FIELDS";
export const FILTER_FORM_DATA:FilterFieldsData[][] = [
  [ 
    {
      id: FilterFormFields.dateFromForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateFromForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.dateToForm,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dateToForm}`,
      validations: Validators.required,
      fieldType: FilterFieldType.datePicker
    },
    {
      id: FilterFormFields.state,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.state}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(States),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.STATES'
    },
    {
      id: FilterFormFields.presentadorNif,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.presentadorNif}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text,
    },
    {
      id: FilterFormFields.subjectePassiuNif,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.subjectePassiuNif}`,
      validations: PtValidations.dniNieCif,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.tipus,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.tipus}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(TramitType),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.ROWS.TRAMIT'
    },
    {
      id: FilterFormFields.origen,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.origen}`,
      fieldType: FilterFieldType.select,
      optionsValue: Object.entries(OriginOptions),
      optionsLabelTranslations: 'MODULE_TRACKING.MODULE_MANAGEMENTS_MONITOR.TABLE.FILTER.ORIGIN_OPTIONS'
    },
    {
      id: FilterFormFields.dadesAddicionals,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.dadesAddicionals}`,
      fieldType: FilterFieldType.text
    },
    {
      id: FilterFormFields.idTramitacio,
      label: `${FILTER_FIELDS_TRANSLATIONS}.${FilterFormFields.idTramitacio}`,
      fieldType: FilterFieldType.text
    }
  ]
]

export class FilterFormValue implements ManagementsMonitorFilter {
  dateFromForm: Date;
  dateToForm: Date;
  dateFrom: number;
  dateTo: number;
  state: string;
  subjectePassiuNif: string;
  presentadorNif: string;
  tipus: TramitType;
  dadesAddicionals: string;
  idTramitacio: string;
  
  constructor(
    data?: ManagementsMonitorFilter
  ) {
    const dateFrom = data?.dateFromForm ? new Date(data.dateFromForm) : new Date();
    const dateTo = data?.dateToForm ? new Date(data.dateToForm) : new Date();
    if (!data?.dateFromForm) dateFrom.setDate(dateFrom.getDate() - 30);

    Object.assign(this, {
      ...data,
      dateFromForm: dateFrom,
      dateToForm: dateTo,
    });
  }
}