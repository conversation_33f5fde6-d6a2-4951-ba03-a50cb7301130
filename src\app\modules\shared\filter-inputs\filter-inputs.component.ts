import { Component, Input, OnInit } from '@angular/core';
import { AbstractControl, FormControl, FormGroup } from '@angular/forms';
import {
	PtComponentEventOutput
} from 'pt-ui-components-mf-lib';
import { FilterFieldType, FilterFieldsData } from './models/filter-inputs.model';

@Component({
	selector: 'app-filter-inputs',
	templateUrl: './filter-inputs.component.html'
})
export class FilterInputsComponent implements OnInit {

	@Input() formGroup: FormGroup;
  @Input() fieldData: FilterFieldsData;

	// Other
	yearRange:string;

	// Enums
	FilterFieldType = FilterFieldType;

	constructor() { }

	ngOnInit(): void {
		this.setYearRange();
	}

	private setYearRange (date?: Date){
		const rangeDate = date ? new Date(date) : new Date();
		this.yearRange = `${rangeDate.getFullYear()-11}:${rangeDate.getFullYear()+9}`;
	}

	changeRangeYear(event:PtComponentEventOutput):void {
		this.setYearRange(event.componentValue);
	}

	getFieldKeysControl(field: FilterFieldsData): string[] {
		const group = this.formGroup.controls[field.id] as FormGroup;
		return Object.keys(group.controls)
	}

}
